<!doctype html>
<html lang="pl">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Windows</title>

    <meta name="description" content="Instructions on how to install XAMPP for Windows distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="pl pl_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/pl/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/pl/faq.html">Najczęściej zadawane pytania</a></li>
              <li class="item "><a href="/dashboard/pl/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Windows <span>Najczęściej zadawane pytania</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
      <dt>Jak zainstalować XAMPP?</dt>
      <dd>
      <p>XAMPP dla Windows występuje w trzech różnych odmianach:</p>
      <p>Instalator:<br />
      Prawdopodobnie najprostszy sposób na zainstalowanie XAMPP.</p>
      <p>ZIP:<br />
      Dla purystów: XAMPP jako zwyczajne archiwum ZIP.</p>
      <p>7zip:<br />
      Dla purystów z niskim transferem internetowym: XAMPP jako archiwum 7zip.</p>
      <p>Uwaga: Jeśli wypakujesz pliki, mogą się pojawić fałszywe ostrzeżenia programów antywirusowych.</p>
      <p><strong>Używając programu instalacyjnego:</strong></p>
      <p></p>
      <p>Panel kontrolny XAMPP służy do uruchamiania/zatrzymywania Apache, MySQL, FileZilla & Mercury lub instalacji tych serwerów jako usługi systemowe.</p>
      <p><strong>Instalowanie z pliku ZIP</strong></p>
      <p>Wypakuj archiwum zip do wybranego przez siebie folderu. XAMPP jest domyślnie wypakowywany do katalogu "C:\\xampp". Teraz uruchom plik "setup_xampp.bat" aby dostosować konfigurację XAMPP do twojego systemu.</p>
      <p>Jeżeli wybrałeś katalog docelowy jako C:\\", nie musisz uruchamiać pliku "setup_xampp.bat".</p>
      <p>Tak jak w wersji instalacyjnej, możesz teraz użyć "XAMPP Control Panel" w celu wykonania dodatkowych zadań.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
      <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>Jak mogę uruchomić XAMPP bez instalacji?</dt>
      <dd>
      <p>Jeśli wypakujesz XAMPP do katalogu głównego jak "C:\\" lub "D:\\", możesz uruchomić większość serwerów jak Apache czy MySQL bezpośrednio, bez potrzeby uruchamiania pliku "setup_xampp.bat".</p>
      <p>Należy nie używać skryptu instalacyjnego lub wybrać ścieżki względne w skrypcie instalacyjnym, jest to korzystne w przypadku instalacji XAMPP na dysku USB, ponieważ na każdym komputerze taki napęd może mieć inną literę napędu. Możesz w każdej chwili przełączyć się pomiędzy ścieżkami relatywnymi a absolutnymi w skrypcie instalacyjnym.</p>
      <p>Używając instalatora z naszej strony pobierania, łatwo zainstalujesz pakiet XAMPP. Po instalacji XAMPP znajdziesz w Start => Programy => XAMPP. Możesz użyć Panelu Kontrolnego XAMPP, aby uruchamiać/zatrzymywać wszystkie serwery i także instalować/usuwać usługi.</p>
      <p>Panel kontrolny XAMPP służy do uruchamiania/zatrzymywania Apache, MySQL, FileZilla & Mercury lub instalacji tych serwerów jako usługi systemowe.</p>
      </dd>
      <dt>Jak mogę uruchomić i zatrzymać XAMPP?</dt>
      <dd>
      <p>Uniwersalne centrum kontroli to "Panel Kontroli XAMPP" (dzięki www.nat32.com). Jest uruchamiane z:</p>
      <p><code>\xampp\xampp-control.exe</code></p>
      <p>Możesz też użyć kilku skryptów by uruchomić lub zatrzymać serwery:</p>
      <p>
      <ul>
        <li>Uruchomienie Apache &amp; MySQL:
        <code>\xampp\xampp_start.exe</code></li>
        <li>Zatrzymywanie Apache &amp; MySQL:
        <code>\xampp\xampp_stop.exe</code></li>
        <li>Uruchomienie Apache'a:
        <code>\xampp\apache_start.bat</code></li>
        <li>Zatrzymywanie Apache:
        <code>\xampp\apache_stop.bat</code></li>
        <li>Uruchamianie MySQL:
        <code>\xampp\mysql_start.bat</code></li>
        <li>Zatrzymywanie MySQL:
        <code>\xampp\mysql_stop.bat</code></li>
        <li>Uruchomienie serwera mailowego Mercury:
        <code>\xampp\mercury_start.bat</code></li>
        <li>Zatrzymywanie serwera mailowego Mercury:
        <code>\xampp\mercury_stop.bat</code></li>
        <li>Uruchomienie serwera FileZilla:
        <code>\xampp\filezilla_start.bat</code></li>
        <li>Zatrzymanie serwera FileZilla:
        <code>\xampp\filezilla_stop.bat</code></li></ul></p>
      </p>
      </dd>
      <dt>Jak mogę sprawdzić czy wszystko działa?</dt>
      <dd>
      <p>Wprowadź poniższy URL w swojej ulubionej przeglądarce internetowej:</p>
      <p><code>http://localhost/</code> lub  <code>http://127.0.0.1/</code> </p>
      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-windows-start.jpg" />
      </dd>
      <dt>Jak mogę zainstalować serwer jako usługę?</dt>
      <dd>
      <p>Każdy serwer w pakiecie XAMPP może być zainstalowany jako usługa Windows. Możesz tego dokonać przez Panel Kontrolny XAMPP. W większości przypadków uruchomienie skryptów lub Panelu Kontrolnego wymaga uprawnień administratora.</p>
      <p>Instalacja usługi Apache: \\xampp\\apache\\apache_installservice.bat</p>
      <p>Deinstalacja usługi Apache: \\xampp\\apache\\apache_uninstallservice.bat </p>
      <p>Instalacja usługi MySQL: \\xampp\\mysql\\mysql_installservice.bat </p>
      <p>Deinstalacja usługi MySQL: \\xampp\\mysql\\mysql_uninstallservice.bat </p>
      <p>(De)instalacja usługi FileZilla: \\xampp\\filezilla_setup.bat </p>
      <p>Mercury: Brak instalacji usługi</p>
      </dd>
      <dt>Czy XAMPP jest gotowy do środowiska produkcyjnego?</dt>
      <dd>
      <p>XAMPP nie jest przeznaczony do zastosowań produkcyjnych, ale na środowisko deweloperskie. Sposób w jaki XAMPP jest skonfigurowany by być otwartym jak to tylko możliwe umożliwia deweloperom robić co zechcą. Do zastosowań deweloperskich jest to wspaniałe, ale do zastosowań produkcyjnych może być fatalne.</p>
      <p>To jest lista pominiętych zabezpieczeń w XAMPP:</p>
      <ol>
        <li>Administrator MySQL (root) nie ma hasła.</li>
        <li>Usługa MySQL jest dostępna przez sieć.</li>
        <li>ProFTPD używa hasła "lampp" dla użytkownika "daemon".</li>
        <li>Domyślni użytkownicy programu Mercury i FileZilla są znani.</li>
      </ol>
      <p>Wszystkie punkty mogą być ogromnym zagrożeniem dla bezpieczeństwa. Zwłaszcza jeżeli XAMPP jest dostępny przez sieć i ludzie mają dostęp do niego poza twoją siecią LAN. Także może być pomocne użycie zapory sieciowej lub routera NAT. W przypadku routera lub zapory twój komputer nie jest normalnie dostępny przez sieć. To do ciebie należy zadanie rozwiązania tych problemów. Jako małą pomoc dajemy "konsolę bezpieczeństwa XAMPP".</p>
      <p>Proszę zabezpieczyć XAMPP przed opublikowaniem czegokolwiek w Internecie. Zapora sieciowa lub zewnętrzny router wystarczają tylko na niskim poziomie bezpieczeństwa. Aby wpłynąć na nieco większe bezpieczeństwo, można uruchomić konsolę bezpieczeństwa XAMPP i przypisać hasła.</p>
      <p>Jeżeli chcesz aby twój XAMPP był dostępny przez internet, powinieneś podążyć za poniższym URI, który może naprawić niektóre problemy:</p>
      <p><code> http://localhost/security/</code></p>
      <p>Z konsolą bezpieczeństwa możesz ustawić hasło dla administratora MySQL i phpMyAdmina. Możesz także włączyć uwierzytelnianie dla stron z pakietu XAMPP.</p>
      <p>To narzędzie webowe nie potrafi naprawić każdej dodatkowej kwestii odnośnie bezpieczeństwa. W szczególności musisz sam ustawić serwer FileZilla FTP i serwer mailowy Mercury.</p></dd>
      <dt>Jak odinstalować XAMPP?</dt>
      <dd>
      <p>Jeżeli zainstalowałeś pakiet XAMPP używając instalatora, proszę użyj deinstalatora. Deinstalator skasuje wszystkie wpisy XAMPP z twojego rejestru i także deinstaluje jakieś zainstalowane usługi włączone w pakiecie XAMPP. Bardzo polecamy użycie deinstalatora przy usuwaniu instalacji XAMPP. Proszę zrobić kopię zapasową wszystkich swoich danych przed deinstalacją pakietu XAMPP.</p>
      <p>Jeżeli zainstalowałeś XAMPP używając programu ZIP i 7zip, wyłącz wszystkie serwery XAMPP i wyłącz wszystkie panele. Jeżeli instalowałeś jakieś usługi, odinstaluj je. Teraz możesz usunąć folder gdzie XAMPP został zainstalowany. Nie trzeba czyścić niczego więcej, ponieważ rejestr nie zawiera żadnych wpisów i zmiennych środowiskowych.</p>
      </dd>
      <dt>Co to jest wersja "lite" pakietu XAMPP?</dt>
      <dd>
      <p>XAMPP Lite (oznacza "lekki" w znaczeniu "lekko ważący") jest mniejszym zestawem komponentów XAMPP, który jest zalecany dla szybszej pracy, używającej tylko PHP i MySQL. Niektóre serwery lub narzędzia tj. Mercury Mail i FileZilla FTP nie występują w wersji lekkiej.</p>
      </dd>
      <dt>Gdzie powinienem umieścić pliki z moją aplikacją internetową?</dt>
      <dd>
      <p>Główny folder dla wszystkich dokumentów WWW to \\xampp\\htdocs. Jeżeli umieścisz w tym katalogu plik "test.html", będziesz miał do niego dostęp przez URI: "http://localhost/test.html".</p>
      <p>I "test.php"? Wystarczy użyć http://localhost/test.php". Prostym skryptem testowym może być:</p>
      <p><code>&lt;?php<br />
        echo 'Hello world'; <br />
        ?&gt;</code></p>
      <p>Nowy podkatalog dla twojej strony www? Stwórz nowy katalog (np. "nowy") wewnątrz katalogu "\\xampp\\htdocs" (najlepiej bez znaków odstępów i tylko używając znaków ASCII), utwórz plik testowy w tym katalogu i spróbuj uzyskać do niego dostęp przez URL: "http://localhost/nowy/test.php".</p>
      <p><strong>Inne specyfikacje</strong></p>
      <p>HTML:<br>
      Wykonywalny: \xampp\htdocs<br>
      Dozwolone zakończenia: .html .htm<br>
      => pakiet podstawowy</p>
      <p>SSI:<br>
      Wykonywalny: \xampp\htdocs<br>
      Dozwolone zakończenia: .shtml<br>
      => pakiet podstawowy</p>
      <p>CGI:<br>
      Wykonywalny: \xampp\htdocs and \xampp\cgi-bin<br>
      Dozwolone zakończenia: .cgi<br>
      => pakiet podstawowy</p>
      <p>PHP:<br>
      Wykonywalny: \xampp\htdocs and \xampp\cgi-bin<br>
      Dozwolone zakończenia: .php<br>
      => pakiet podstawowy</p>
      <p>Perl:<br>
      Wykonywalny: \xampp\htdocs and \xampp\cgi-bin<br>
      Dozwolone zakończenia: .pl<br>
      => pakiet podstawowy</p>
      <p>Apache::ASP Perl:<br>
      Wykonywalny: \xampp\htdocs<br>
      Dozwolone zakończenia: .asp<br>
      => pakiet podstawowy</p>
      <p>JSP Java:<br>
      Wykonywalny: \xampp\tomcat\webapps\java (e.g.)<br>
      Dozwolone zakończenia: .jsp<br>
      => Tomcat add-on</p>
      <p>Servlets Java:<br>
      Wykonywalny: \xampp\tomcat\webapps\java (e.g.)<br>
      Dozwolone zakończenia: .html (u.a)<br>
      => Tomcat add-on</p>
      </dd>
      <dt>Czy mogę przenieść instalację XAMPP?</dt>
      <dd>
      <p>Tak. Po przeniesieniu katalogu XAMPP, musisz wykonywać plik setup_xampp.bat". W tym kroku ścieżki w plikach konfiguracyjnych zostaną zaktualizowane.</p>
      <p>Jeżeli masz jakiś serwer zainstalowany jako usługę Windows, musisz najpierw usunąć usługę, a dopiero po przeniesieniu katalogu ponownie zainstalować usługę.</p>
      <p>Ostrzeżenie: Pliki konfiguracji z twoich własnych skryptów, tj. aplikacji PHP, nie są aktualizowane. Ale jest możliwość napisania "wtyczki" dla instalatora. Z tą wtyczką, instalator może też te pliki zaktualizować.</p>
      </dd>
      <dt>Co to są "Automatyczne strony startowe" dla katalogów WWW?</dt>
      <dd>
      <p>Standardowa nazwa pliku dla funkcji Apache'a "DirectoryIndex" to "index.html" lub "index.php". Za każdym razem kiedy przeglądasz folder (np. "http://localhost/xampp/"), Apache szuka jednego z tych plików, zamiast wyświetlać listę plików z katalogu.</p>
      </dd>
      <dt>Gdzie mogę zmienić konfigurację?</dt>
      <dd>
      <p>Prawie wszystkie ustawienia w XAMPP możesz zmienić w plikach konfiguracyjnych. Otwórz plik w edytorze pliku i zmień wybrane przez siebie ustawienie. Tylko aplikacje FileZilla i Mercury powinne być skonfigurowane wykorzystując wewnętrzne narzędzie do konfiguracji.</p>
      </dd>

      <dt>Dlaczego XAMPP nie może pracować na Windows XP SP2?</dt>
      <dd>
      <p>Microsoft dostarcza lepszą zaporę sieciową z Service Pack 2 (SP2), która uruchamia się automatycznie przy starcie systemu. Ta zapora sieciowa blokuje teraz potrzebne porty 80 (http) i 443 (https) i Apache nie może się uruchomić.</p>
      <p><strong>Szybkie rozwiązanie:</strong></p>
      <p>Wyłącz zaporę sieciową Microsoftu i spróbuj ponownie uruchomić XAMPP. Lepszym rozwiązaniem jest zdefiniowanie wyjątków w centrum zabezpieczeń.</p>
      <p><strong>Poniższe porty są używane dla podstawowej funkcjonalności:</strong></p>
      <p>Apache (HTTP): Port 80<br/>
        Apache (WebDAV): Port 81</br>
        Apache (HTTPS): Port 443</br>
        MySQL: Port 3306</br>
        FileZilla (FTP): Port 21</br>
        FileZilla (Admin): Port 14147</br>
        Mercury (SMTP): Port 25</br>
        Mercury (POP3): Port 110</br>
        Mercury (IMAP): Port 143</br>
        Mercury (HTTP): Port 2224</br>
        Mercury (Finger): Port 79</br>
        Mercury (PH): Port 105</br>
        Mercury (PopPass): Port 106</br>
        Tomcat (AJP/1.3): Port 8009</br>
        Tomcat (HTTP): Port 8080</p>
      </dd>

      <dt>Dlaczego XAMPP nie pracuje na Microsoft Vista?</dt>
      <dd>
      <p><strong>Kontrola konta użytkownika (UAC)</strong></p>
      <p>W katalogu "C:\\program files" nie masz pełnych praw zapisu jako administrator. Lub masz ograniczone uprawnienia (np. dla ".\\xampp\\htdocs"). W tym przypadku nie można edytować pliku.</br>
<strong>Rozwiązanie:</strong> Podnieść swoje uprawnienia w eksploratorze (prawy klik / bezpieczeństwo) lub wyłącz kontrolę konta użytkownika (UAC).</p>
      <p>Masz zainstalowany Apache/MySQL w "C:\\xampp" jako usługi Windows. Ale nie uruchomiłeś/zatrzymałeś usługi przez Panel Kontrolny XAMPP lub ich nie odinstalowałeś.</br></br>
<strong>Rozwiązanie:</strong> Użyj konsoli zarządzania usługami w Windowsie lub wyłącz UAC.</p>
      <p><strong>Wyłączanie Kontroli Konta Użytkownika (UAC)</strong></p>
      <p>Aby wyłączyć UAC, użyj programu "msconfig". W "msconfig" idź do "Narzędzia", wybierz "wyłącz kontrolę konta użytkownika" i zweryfikuj swój wybór. Restartuj Windows. W każdym czasie możesz ponownie włączyć UAC.</p>
      </dd>

      <dt>Jak mogę sprawdzić sumę kontrolną MD5?</dt>
      <dd>
      <p>Aby porównać pliki, najczęściej porównuje się ich sumy kontrolne. Do utworzenia tych sum wystarczy standardowo MD5 (Message Digest Algorithm 5).</p>
      <p>Z sumą kontrolną md5 możesz przetestować czy twoja paczka XAMPP została pobrana prawidłowo czy też nie. Oczywiście potrzebujesz programu, który utworzy te sumy kontrolne. Dla Windows możesz użyć narzędzia z Microsoftu:</p>
      <p><a href="http://support.microsoft.com/kb/841290">Dostępność i opis narzędzia integracji weryfikacji sumy kontrolnej pliku</a></p>
      <p>Możliwe jest również użycie innego programu, który potrafi utworzyć sumę kontrolną md5, np. GNU md5sum.</p>
      <p>Jeżeli masz zainstalowany program (np. fciv.exe), wykonaj poniższe kroki:</p>
      <p>
        <ul>
          <li>Pobierz XAMPP (np. xampp-win32-1.8.2-0.exe)</li>
          <li>Utwórz sumę kontrolną z:</br>
            <code>fciv.exe xampp-win32-1.8.2-0.exe</code>
          </li>
          <li>I teraz możesz porównać swoją sumę kontrolną z tą znalezioną na stronie XAMPP dla Windows.</li>
        </ul>
      </p>
      <p>Jeżeli obie sumy są równe, wszystko jest w porządku. Jeżeli nie, pobrany plik jest uszkodzony lub został zmieniony.</p>
      </dd>

      <dt>Dlaczego zmiany w php.ini nie przynoszą efektu?</dt>
      <dd>
      <p>Jeżeli zmiana w "php.ini" nie przynosi efektu, możliwe że PHP używa innego pliku. Możesz zweryfikować to na podstawie funkcji phpinfo(). Idź do http://localhost/xampp/phpinfo.php i poszukaj "Loaded Configuration File". Ta wartość pokazuje który plik "php.ini" jest przez PHP używany.</p>
      <p><strong>Uwaga:</strong> Po zmianach w pliku "php.ini" musisz zrestartować Apache, aby Apache i PHP wczytały nowe ustawienia.</p>
      </dd>

      <dt>Pomocy! W pakiecie XAMPP jest wirus!</dt>
      <dd>
      <p>Niektóre programy antywirusowe traktują XAMPP jak wirus, zwykle wskazując na plik xampp-manager.exe. To jest fałszywe powiadomienie ze strony programu antywirusowego, ponieważ pakiet XAMPP nie zawiera wirusów. Przed opublikowaniem nowej wersji XAMPP, przepuszczamy pakiet i pliki przez program antywirusowy. W tym momencie używamy <a href="http://www.kaspersky.com/virusscanner">Kapersky Online Virus Scanner</a>. You can also use the online tool <a href="https://www.virustotal.com/">Virus Total</a> for scanning XAMPP or send us an email to security (at) apachefriends (dot) org if you find any issue.</p>
      </dd>

      <dt>Jak mogę skonfigurować mój program antywirusowy?</dt>
      <dd>
      <p>Włączyliśmy wszystkie zależności i serwery niezbędne do uruchomienia aplikacji internetowej, więc można zauważyć, że XAMPP instaluje dużą liczbę plików. Jeżeli instalujesz XAMPP na Windowsie z włączonym programem antywirusowym, to może znacznie spowolnić instalację, i jest szansa, że jeden z serwerów (serwer www, serwer bazy danych) może zostać zablokowany przez program antywirusowy. Jeżeli masz włączony program antywirusowy, sprawdź następujące ustawienia by mieć XAMPP działający bez problemów związanych z wydajnością:</p>
      <p>
        <ul>
          <li>Dodaj wyjątki do swojej zapory sieciowej: dla Apache'a, MySQL i innych serwerów.</li>
          <li>Skanuj pliki podczas uruchamiania: Jeśli włączono skanowanie antywirusowe dla wszystkich plików, pliki wykonywalne serwerów mogą być spowalniane.</li>
          <li>Skanuj ruch dla różnych adresów URL: jeżeli rozwijasz z pakietem XAMPP na swoim komputerze, możesz wykluczyć ruch z "localhost" (hosta lokalnego) w ustawieniach programu antywirusowego.</li>
        </ul>
      </p>
      </dd>

      <dt>Dlaczego Apache nie uruchamia się w moim systemie?</dt>
      <dd>
      <p>Ten problem może występować z kilku powodów:</p>
      <p>
        <ul>
          <li>Możesz mieć uruchomionych więcej niż jeden serwer HTTP (IIS, Sambar, ZEUS, lub inny). Tylko jeden serwer może korzystać z portu 80. Ten komunikat o błędzie wskazuje problem:<br/>
<code>(OS 10048)... make_sock: could not bind to adress 0.0.0.0:80
no listening sockets available, shutting down</code></li>
          <li>Jeżeli masz inne oprogramowanie, tj. telefonię internetową Skype, która też blokuje port 80. Jeżeli problem stwarza Skype, w Skype z menu wybierz Akcje --> Opcje --> Połączenie --> usuń znaczek przy opcji "użyj portu 80 jako portu alternatywnego" i zrestartuj Skype. Teraz serwer powinien pracować poprawnie.</li>
          <li>Masz zaporę sieciową, która blokuje port Apache. Nie wszystkie zapory sieciowe są zgodne z Apache, i czasami dezaktywacja zapory nie wystarcza i trzeba ją usunąć. Ten komunikat o błędzie wskazuje zaporę:<br/>
<code>(OS 10038)Socket operation on non-socket: make_sock: for address 0.0.0.0:80,
apr_socket_opt_set: (SO_KEEPALIVE)</code></li>
        </ul>
        <p>Także jeżeli Apache może się uruchomić, ale twoja przeglądarka nie może się połączyć, to może być spowodowane przez:</p>
        <ul>
          <li>Niektóre skanery antywirusowe mogą powodować problemy w podobny sposób jak to robią zapory sieciowe.</li>
          <li>Masz Windows XP Professional bez Service Pack 1. Musisz mieć przynajmniej SP1 dla XAMPP.</li>
        </ul>
      </p>
      <p><strong>Porada:</strong> Jeżeli masz problemy z użyciem portów, możesz spróbować uruchomić narzędzie "xampp-portcheck.exe". To może pomóc.</p>
      </dd>

      <dt>Dlaczego moje obciążenie dla Apache'a jest bliskie 99%?</dt>
      <dd>
      <p>Tutaj może wystąpić jeden z dwóch scenariuszy. Jeżeli twój procesor jest maksymalnie wykorzystywany, lub twoja przeglądarka może połączyć się z serwerem, ale nic nie widzisz (system próbuje nieskutecznie załadować stronę). W każdym przypadku znajdziesz poniższą wiadomość w pliku zdarzeń Apache'a:</p>
      <p><code>Child: Encountered too many AcceptEx faults accepting client connections.
winnt_mpm: falling back to 'AcceptFilter none'.</code></p>
      <p>MPM wraca do bezpiecznego wykonania, ale niektóre żądania klientów nie zostały przetworzone w sposób prawidłowy. Aby uniknąć tego błędu, należy użyć "acceptfilter" z akceptacją filtra "none" w pliku "\\xampp\\apache\\conf\\extra\\httpd-mpm.conf".</p>
      </dd>

      <dt>Dlaczego zdjęcia i arkusze stylu nie są wyświetlane?</dt>
      <dd>
      <p>Czasami są problemy z wyświetleniem obrazków i arkuszy stylów. Zwłaszcza jeśli te pliki znajdują się na dysku sieciowym. W tym przypadku powinieneś włączyć (lub dodać) jedną z poniższych linii w pliku "\\xampp\\apache\\conf\\httpd.conf":</p>
      <p><code>EnableSendfile off</br>
EnableMMAP off</code></p>
      <p>Ten problem może być również spowodowany przez programy do regulacji przepustowości pasma, tj. program NetLimiter.</p>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To configure XAMPP to use the included sendmail.exe binary for email delivery, follow these steps:</p>
        <ul>
          <li>Edit the XAMPP "php.ini" file. Within this file, find the [mail function] section and replace it with the following directives. Change the XAMPP installation path if needed.
          <code>
          sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
          </code>
          </li>
          <li>Edit the XAMPP "sendmail.ini" file. Within this file, find the [sendmail] section and replace it with the following directives:
          <code>
          smtp_server=smtp.gmail.com
          smtp_port=465
          smtp_ssl=auto
          error_logfile=error.log
          auth_username=<EMAIL>
          auth_password=your-gmail-password
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>Restart the Apache server using the XAMPP control panel.
          </li>
        </ul>
        <p>You can now use PHP's mail() function to send email from your application.</p> 
      </dd>
      
      <dt>Jak mogę ustawić hasło administratora w MySQL?</dt>
      <dd>
      <p>Configure it with the "XAMPP Shell" (command prompt). Open the shell from the XAMPP control pane and execute this command:<code>mysqladmin.exe -u root password secret</code>This sets the root password to 'secret'.</p>
      </dd>

      <dt>Czy mogę użyć swojego własnego serwera MySQL?</dt>
      <dd>
      <p>Tak. Najprościej nie uruchamiać MySQL z pakietu XAMPP. Proszę zauważyć, że dwa serwery nie mogą być uruchomione na tym samym porcie. Jeżeli masz ustawione hasło dla użytkownika MySQL "root", proszę nie zapomnieć zaktualizować pliku "\\xampp\\phpMyAdmin\\config.inc.php".</p>
      </dd>

      <dt>Jak mogę ograniczyć dostęp do phpMyAdmina z zewnątrz?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>Przed twoim dostępem do serwera MySQL, phpMyAdmin zapyta cię o nazwę użytkownika i hasło. Nie zapomnij najpierw ustawić hasła dla użytkownika "root".</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
      <p>To enable remote access to phpMyAdmin, follow these steps:</p>
      <ul>
        <li>Edit the apache\conf\extra\httpd-xampp.conf file in your XAMPP installation directory.</li>
        <li>Within this file, find the lines below. 
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require local
          </code></p>
        </li>
        <li>Then replace 'Require local' with 'Require all granted'.</li>
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require all granted
          </code></p>
        <li>Restart the Apache server using the XAMPP control panel.</li>
      </ul>
      </dd>
      
      <dt>Gdzie jest wsparcie IMAP dla PHP?</dt>
      <dd>
      <p>Domyślnie, wsparcie IMAP dla PHP w XAMPP jest wyłączone, ze względu na jakieś tajemnicze błędy inicjalizacji dla wersji domowych podobnych do Windows 98. Jeżeli pracujesz z systemami NT, możesz otworzyć plik "\\xampp\\php\\php.ini" i aktywować rozszerzenie PHP przez usunięcie średnika na początku linii ";extension=php_imap.dll". To powinno być:</br>
<code>extension=php_imap.dll</code></p>
      <p>Teraz zrestartuj Apache'a i IMAP powinien już pracować. Możesz użyć tych samych kroków dla każdego rozszerzenia, który nie jest domyślnie włączony w konfiguracji.</p>
      </dd>

      <dt>Dlaczego niektóre aplikacje PHP o otwartym kodze źródłowym nie pracują z XAMPP na Windowsie?</dt>
      <dd>
      <p>Wiele aplikacji PHP lub rozszerzeń, które powstały na system Linux, nie zostało przepisanych na Windowsa. </p>
      </dd>

      <dt>Czy mogę usunąć katalog "install" po instalacji?</dt>
      <dd>
      <p>Lepiej nie. Skrypty tutaj są wciąż potrzebne dla wszystkich pakietów dodatkowych (dodatków) i aktualizacji XAMPP.</p>
      </dd>

      <dt>Jak mogę aktywować eaccelerator?</dt>
      <dd>
      <p>Podobnie jak w innych (Zend) rozszerzeniach, musisz aktywować to w pliku php.ini. W tym pliku włącz linię ";zend_extension = "\\xampp\\php\\ext\\php_eaccelerator.dll"". To powinno wyglądać:</br>
<code>zend_extension = "\xampp\php\ext\php_eaccelerator.dll"</code></p>
      </dd>

      <dt>Jak mogę naprawić błędy połączenia do mojego serwera MS SQL?</dt>
      <dd>
      <p>Jeżeli rozszerzenie mssql zostało załadowane w pliku php.ini, czasami problemy występują kiedy tylko TCP/IP jest używany. Możesz naprawić ten problem z nową wersją "ntwdblib.dll" ze strony Microsoftu. Zastąp tym nowszym plikiem, plikami znajdującymi się w "\\xampp\\apache\\bin" i "\\xampp\\php". Ze względu na licencję tego pliku, nie możemy spakować jego nowszą wersję z pakietem XAMPP.</p>
      </dd>

      <dt>Jak mogę pracować z rozszerzeniem PHP mcrypt?</dt>
      <dd>
      <p>Dla niego otworzyliśmy temat na forum z przykładami i możliwymi rozwiązaniami: <a href="https://community.apachefriends.org/f/viewtopic.php?t=3012">Temat MCrypt</a></p>
      </dd>

      <dt>Czy Microsoft Active Server Pages (ASP) pracuje z XAMPP?</dt>
      <dd>
      <p>Nie. Apache::ASP z dodatkiem Perl to nie to samo. Apache::ASP zna tylko skrypty Perl, ale ASP z Internet Information Server (IIS) zna także normalny język VBScript. Ale dla ASP .NET są dostępne moduły Apache firm trzecich.</p>
      </dd>

      <dt>How can I get XAMPP working on port 80 under Windows 10?</dt>
      <dd>
      <p>By default, Windows 10 starts Microsoft IIS on port 80, which is the same default port used by Apache in XAMPP. As a result, Apache cannot bind to port 80.</p>
      <p>To deactivate IIS from running on port 80, follow these steps:</p>
      <ul>
        <li>Open the Services panel in Computer Management.</li>
        <li>Search for the 'World Wide Web Publishing Service' and select it.</li>
        <li>Click the link to 'Stop the service'.</li>
        <li>Double-click the service name.</li>
        <li>In the 'Startup type' field, change the startup type to 'Disabled'.</li>
        <li>Click 'OK' to save your changes.</li>
      </ul>
      <p>You should now be able to start Apache in XAMPP on port 80.</p>
      <p>For more information, refer to the 'Troubleshoot Apache Startup Problems' guide included with XAMPP or <a href='https://community.apachefriends.org/f/viewtopic.php?f=16&t=71620'>this forum post</a>.</p>
      </dd>
      
      <dt>How can I use Microsoft Edge to access local addresses under Windows 10?</dt>
      <dd>
      <p>If your local machine has the host name 'myhost', you will not be able to access URLs such as http://myhost in Microsoft Edge. To resolve this, you should instead use the addresses http://127.0.0.1 or http://localhost.</p>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: \xampp\apache\conf\httpd.conf, \xampp\apache\conf\extra\httpd-xampp.conf</li>
          <li>PHP configuration file: \xampp\php\php.ini</li>
          <li>MySQL configuration file: \xampp\mysql\bin\my.ini</li>
          <li>FileZilla Server configuration file: \xampp\FileZillaFTP\FileZilla Server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\tomcat\conf\server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\sendmail\sendmail.ini</li>
          <li>Mercury Mail configuration file: \xampp\MercuryMail\MERCURY.INI</li>
        </ul>
      </dd>

    </dl>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Prawa zastrzeżone (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Polityka prywatności</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN dostarczone przez
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
