<!doctype html>
<html lang="jp">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Windows</title>

    <meta name="description" content="Instructions on how to install XAMPP for Windows distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="jp jp_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/jp/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/jp/faq.html">FAQ</a></li>
              <li class="item "><a href="/dashboard/jp/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Windows <span>よくある質問</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
      <dt>XAMPP のインストール方法</dt>
      <dd>
      <p>Windows用のXAMPPを導入する手段は3つあります。</p>
      <p>インストーラを使う方法：<br />
      おそらく最も簡単な方法です。</p>
      <p>ZIP:<br />
                        補足：XAMPP は通常、ZIP形式で圧縮されています。                </p>
      <p>7zip:<br />
      通信環境が悪い場合：XAMPPの7zip形式の圧縮ファイルを使用してください。</p>
      <p>Note: If you extract the files, there can be false-positives virus warnings.</p>
      <p><strong>Using the installer:</strong></p>
      <p></p>
      <p>XAMPPのコントロールパネルからApache、MySQL、FileZillaとMercury の開始/停止、サービス利用のためのサーバーインストールが行えます。</p>
      <p><strong>Installing from ZIP</strong></p>
      <p>Unzip the zip archives into the folder of your choice. XAMPP is extracting to the subdirectory "C:\xampp" below the selected target directory. Now start the file "setup_xampp.bat", to adjust the XAMPP configuration to your system.</p>
      <p>If you choose a root directory "C:\" as target, you must not start "setup_xampp.bat".</p>
      <p>インストーラのバージョンと同様に、追加のタスクについては、「 XAMPPコントロールパネル」を使用することができます。</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
      <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>どうやったらセットアップをすることなくXAMPPを起動することができますか？</dt>
      <dd>
      <p>もし上位フォルダ（ "C:\\"や"D:\\"など）にXAMPPを展開する場合は、あなたは直接 setup_xampp.bat ファイルを実行することなくApacheやMySQLのようなほとんどのサーバーを起動することができます。</p>
      <p>                  USBドライブにXAMPPをインストールしているなら、セットアップスクリプトを使わない、もしくは、セットアップスクリプト内の相対パスを選ばない方が良いです。それぞれのPCのドライブにはそれぞれのドライブ記号があります。セットアップスクリプトを使えばいつでも絶対パスから相対パスに変更することは可能です。                </p>
      <p>XAMPPをインストールする最も簡単な方法は、私たちのダウンロードページからインストーラを起動することです。インストールが完了したら、 スタート→全てのプログラム→XAMPPの中にXAMPPが格納されます 。あなたはXAMPPコントロールパネルを使用して、全てのサーバーの開始/停止、インストール/アンインストールを行うことができます。</p>
      <p>XAMPPのコントロールパネルからApache、MySQL、FileZillaとMercury の開始/停止、サービス利用のためのサーバーインストールが行えます。</p>
      </dd>
      <dt>How do I start and stop XAMPP?</dt>
      <dd>
      <p>共通のコントロールセンターは、 「 XAMPPコントロールパネル」 （www.nat32.com）です。以下のように開始されます。</p>
      <p><code>\xampp\xampp-control.exe</code></p>
      <p>You can also use some batchfiles to start/stop the servers:</p>
      <p>
      <ul>
        <li>Apache &amp：MySQL の開始：
        <code>\xampp\xampp_start.exe</code></li>
        <li>Apache &amp; MySQL stop:
        <code>\xampp\xampp_stop.exe</code></li>
        <li>Apache start:
        <code>\xampp\apache_start.bat</code></li>
        <li>Apache stop:
        <code>\xampp\apache_stop.bat</code></li>
        <li>MySQL start:
        <code>\xampp\mysql_start.bat</code></li>
        <li>MySQLの停止：
        <code>\xampp\mysql_stop.bat</code></li>
        <li>Mercury メールサーバーの開始：
        <code>\xampp\mercury_start.bat</code></li>
        <li>Mercury メールサーバーの停止：
        <code>\xampp\mercury_stop.bat</code></li>
        <li>FileZilla サーバーの開始：
        <code>\xampp\filezilla_start.bat</code></li>
        <li>FileZilla サーバーの停止：
        <code>\xampp\filezilla_stop.bat</code></li></ul></p>
      </p>
      </dd>
      <dt>どのようにすれば、システムが正常に動いていることを確認できますか？ </dt>
      <dd>
      <p>ウェブブラウザで以下のURLにアクセスしてください：</p>
      <p><code>http://localhost/</code> または  <code>http://127.0.0.1/</code> </p>
      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-windows-start.jpg" />
      </dd>
      <dt>How can I install a server as a service?</dt>
      <dd>
      <p>XAMPP内の各サーバはWindowsサービスとしてもインストールすることができます。また、 XAMPPコントロールパネルからインストールすることができます。この場合には、管理者権限でスクリプトや[コントロールパネル]を実行する必要があります。</p>
      <p>Apache サービスのインストール：\\xampp\\apache\\apache_installservice.bat</p>
      <p>Apache サービスのアンインストール：\\xampp\\apache\\apache_uninstallservice.bat </p>
      <p>MySQL service install: \xampp\mysql\mysql_installservice.bat </p>
      <p>MySQL service uninstall: \xampp\mysql\mysql_uninstallservice.bat </p>
      <p>FileZilla service (un)install: \xampp\filezilla_setup.bat </p>
      <p>Mercury: No service installation available</p>
      </dd>
      <dt>XAMPPを実際の運用で使用することはできますか？</dt>
      <dd>
      <p>XAMPPは製品運用の手段ではなく、開発環境です。開発者の思った通りに何でもできるよう可能な限りオープンに設定されているのがXAMPPです。これは開発環境には素晴らしい事ですが、製品としては致命的です。</p>
      <p>XAMPPのセキュリティ上の脆弱性を以下に示します。</p>
      <ol>
        <li>MySQL の管理者 ID (root) にパスワードが設定されていません。                </li>
        <li>MySQL デーモンは、ネットワークを介してアクセス可能です。</li>
        <li>ProFTPD uses the password "lampp" for user "daemon".</li>
        <li>The default users of Mercury and FileZilla are known.</li>
      </ol>
      <p>All points can be a huge security risk. Especially if XAMPP is accessible via network and people outside your LAN. It can also help to use a firewall or a (NAT) router. In case of a router or firewall, your PC is normally not accessible via network. It is up to you to fix these problems. As a small help there is the "XAMPP Security console".</p>
      <p>オンラインで何かを公開する前にXAMPPをセキュリティで保護してください。ファイアウォールまたは外部ルータは、セキュリティのレベルが低いためです。もう少しセキュリティを強化するために、 「 XAMPPセキュリティ・コンソール"を実行し、パスワードを割り当てることができます。</p>
      <p>                  自分のXAMPPをインターネットからアクセスできるようにさせたいなら、問題を修正できるこのURIへ行って下さい。                </p>
      <p><code> http://localhost/security/</code></p>
      <p>セキュリティコンソールを使用すると、 MySQLユーザー"root" とphpMyAdminのパスワードを設定することができます。また、 XAMPPのデモページの認証を有効にすることができます。</p>
      <p>このWebベースのツールは、追加のセキュリティ問題を修正しません！特に、 FileZillaのFTPサーバとMercuryメールサーバは、あなた自身でセキュリティ保護する必要があります。</p></dd>
      <dt>XAMPPをアンインストールするにはどうしたらよいでしょうか？</dt>
      <dd>
      <p>インストーラのバージョンを使用して、 XAMPPをインストールした場合は、アンインストーラを使用してください。アンインストーラは、レジストリからすべてのXAMPPエントリを削除し、それがXAMPPに含まれているいくつかインストールされているサービスをアンインストールします。我々はXAMPPのインストールを除去するためのアンインストールプログラムを使用することを強くお勧めします。XAMPPをアンインストールする前に、保存しておきたいすべてのデータのバックアップを行ってください。</p>
      <p>If you installed XAMPP using the ZIP and 7zip versions, shut down all XAMPP servers and exit all panels. If you installed any services, uninstall and shut them down too. Now simply delete the entire folder where XAMPP is installed. There are no registry entries and no environment variables to  clean up.</p>
      </dd>
      <dt>What is the "lite" version of XAMPP?</dt>
      <dd>
      <p>XAMPP Lite (means "light" as in "light-weight") is a smaller bundle of XAMPP components, which is recommended for quick work using only PHP and MySQL. Some servers or tools such as Mercury Mail and FileZilla FTP are missing in the Lite version.</p>
      </dd>
      <dt>Where should I place my web content?</dt>
      <dd>
      <p>The main directory for all WWW documents is \xampp\htdocs. If you put a file "test.html" in this directory, you can access it with the URI "http://localhost/test.html".</p>
      <p>And "test.php"? Just use "http://localhost/test.php". A simple testscript can be:</p>
      <p><code>&lt;?php<br />
        echo 'Hello world'; <br />
        ?&gt;</code></p>
      <p>A new subdirectory for your web? Just make a new directory (e.g. "new") inside the directory "\xampp\htdocs" (best without whitespaces and only ASCII), create a test file in this directory and access it with "http://localhost/new/test.php".</p>
      <p><strong>Further specifics</strong></p>
      <p>HTML:<br>
      Executable: \xampp\htdocs<br>
      Allowed endings: .html .htm<br>
      => basic package</p>
      <p>SSI:<br>
      Executable: \xampp\htdocs<br>
      Allowed endings: .shtml<br>
      => basic package</p>
      <p>CGI:<br>
      Executable: \xampp\htdocs and \xampp\cgi-bin<br>
      Allowed endings: .cgi<br>
      => basic package</p>
      <p>PHP:<br>
      Executable: \xampp\htdocs and \xampp\cgi-bin<br>
      Allowed endings: .php<br>
      => basic package</p>
      <p>Perl:<br>
      Executable: \xampp\htdocs and \xampp\cgi-bin<br>
      Allowed endings: .pl<br>
      => basic package</p>
      <p>Apache::ASP Perl:<br>
      Executable: \xampp\htdocs<br>
      Allowed endings: .asp<br>
      => basic package</p>
      <p>JSP Java:<br>
      Executable: \xampp\tomcat\webapps\java (e.g.)<br>
      Allowed endings: .jsp<br>
      => Tomcat add-on</p>
      <p>Servlets Java:<br>
      Executable: \xampp\tomcat\webapps\java (e.g.)<br>
      Allowed endings: .html (u.a)<br>
      => Tomcat add-on</p>
      </dd>
      <dt>Can I move the XAMPP installation?</dt>
      <dd>
      <p>Yes. After moving of the XAMPP directory, you must execute "setup_xampp.bat". The paths in the configuration files will be adjusted with this step.</p>
      <p>If you have installed any server as Windows service, you must first remove the Windows service, and after the moving you can install the service again.</p>
      <p>WARNING: The configuration files from your own scripts, like PHP applications, are not adjusted. But it's possible to write a "plug-in" for the installer. With such a plug-in, the installer can adjust such files too.</p>
      </dd>
      <dt>What are "Automatic start pages" for the WWW directories?</dt>
      <dd>
      <p>The standard filename for the Apache function "DirectoryIndex" is "index.html" or "index.php". Every time you are just browsing to a folder (e.g. "http://localhost/xampp/"), and Apache can find such a file, Apache is displaying this file instead of a directory listing.</p>
      </dd>
      <dt>Where can I change the configuration?</dt>
      <dd>
      <p>Almost all settings in XAMPP you can change with configuration files. Just open the file in a textedit and change the setting you want. Only FileZilla and Mercury should be configured with the application config tool.</p>
      </dd>

      <dt>Why can't XAMPP work on Windows XP SP2?</dt>
      <dd>
      <p>Microsoft delivers a better firewall with service pack 2 (SP2), which starts automatically. This firewall now blocks the necessary ports 80 (http) and 443 (https) and Apache can't start.</p>
      <p><strong>The fast solution:</strong></p>
      <p>Disable the Microsoft firewall with the toolbar and try to start XAMPP onces more. The better solution is to define an exception within the security center.</p>
      <p><strong>The following ports are used for basic functionality:</strong></p>
      <p>Apache (HTTP): Port 80<br/>
        Apache (WebDAV): Port 81</br>
        Apache (HTTPS): Port 443</br>
        MySQL: Port 3306</br>
        FileZilla (FTP): Port 21</br>
        FileZilla (Admin): Port 14147</br>
        Mercury (SMTP): Port 25</br>
        Mercury (POP3): Port 110</br>
        Mercury (IMAP): Port 143</br>
        Mercury (HTTP): Port 2224</br>
        Mercury (Finger): Port 79</br>
        Mercury (PH): Port 105</br>
        Mercury (PopPass): Port 106</br>
        Tomcat (AJP/1.3): Port 8009</br>
        Tomcat (HTTP): Port 8080</p>
      </dd>

      <dt>Why doesn't XAMPP work on Vista?</dt>
      <dd>
      <p><strong>User Account Control (UAC)</strong></p>
      <p>In the directory "C:\program files" you don't have full write privileges, even as Admin. Or you have only limited privileges (e.g. for ".\xampp\htdocs"). In this case you can't edit a file.</br>
<strong>Solution:</strong> Raise your privileges within explorer (right click / security) or deactivate the user account control (UAC).</p>
      <p>You have installed Apache/MySQL in "C:\xampp" as Windows service. But you can't start/stop the services with the "XAMPP Control Panel" or can't uninstall them.</br></br>
<strong>Solution:</strong> Use the service management console from Windows or deactivate UAC.</p>
      <p><strong>Disabling the User Account Control (UAC)</strong></p>
      <p>To deactivate the UAC, use the program "msconfig". In "msconfig" go to "Tools", select "disable user account control" and verify your selection. Now you must restart Windows. At the same time, you can enable the UAC again.</p>
      </dd>

      <dt>How do I check the md5 checksum?</dt>
      <dd>
      <p>To compare files, often checksums are used. A standard to create this checksum md5 (Message Digest Algorithm 5).</p>
      <p>With this md5 checksum you can test, if your download of the XAMPP package is correct or not. Of course you need a program which can create these checksums. For Windows you can use a tool from Microsoft:</p>
      <p><a href="http://support.microsoft.com/kb/841290">Availability and description of the File Checksum Integrity Verifier utility</a></p>
      <p>It's also possible to use any other program which can create md5 checksums, like the GNU md5sum.</p>
      <p>As you have installed such a program (e.g. fciv.exe), you can do following steps:</p>
      <p>
        <ul>
          <li>Download XAMPP (f.e. xampp-win32-1.8.2-0.exe)</li>
          <li>Create the checksum with:</br>
            <code>fciv.exe xampp-win32-1.8.2-0.exe</code>
          </li>
          <li>And now you can compare this checksum with that one you can find on the XAMPP for Windows homepage.</li>
        </ul>
      </p>
      <p>If both checksums equal, all is ok. If not, the download is broken or the file has been changed.</p>
      </dd>

      <dt>Why have changes in my php.ini not taken effect?</dt>
      <dd>
      <p>If a change in the "php.ini" has no effect, it's possible PHP is using a different one. You can verify this with phpinfo(). Go to the URI  http://localhost/xampp/phpinfo.php and search for "Loaded Configuration File". This value shows you the "php.ini" PHP is really using.</p>
      <p><strong>Note:</strong> After changing the "php.ini" you have to restart Apache so that Apache/PHP can read the new settings.</p>
      </dd>

      <dt>Help! There is a virus in XAMPP!</dt>
      <dd>
      <p>Some antivirus programs mistake XAMPP for a virus, typically flagging the file xampp-manager.exe This is a false positive meaning that the antivirus erroneously identified it as a virus, when it is not. Before we release each new version of XAMPP we run it through virus scanning software. At the moment we are using <a href="http://www.kaspersky.com/virusscanner">Kapersky Online Virus Scanner</a>. You can also use the online tool <a href="https://www.virustotal.com/">Virus Total</a> for scanning XAMPP or send us an email to security (at) apachefriends (dot) org if you find any issue.</p>
      </dd>

      <dt>How do I configure my antivirus application?</dt>
      <dd>
      <p>We have included all dependences and servers required for running the bundled web application, so you will find that XAMPP installs large numbers of files. If you are installing a XAMPP application on a Windows machine with an antivirus app enabled, this may slow down the installation significantly, and there is also a chance that one of the servers (web server, database server) may be blocked by the antivirus software. If you have an antivirus tool enabled, check the following settings for running XAMPP without performance issues:</p>
      <p>
        <ul>
          <li>Add exceptions in the firewall: for Apache, MySQL or any other server.</li>
          <li>Scan files when executing: If you have enabled the antivirus scan for all files, the executable files for the servers may slow down.</li>
          <li>Scan the traffic for different URLs: If you are developing with XAMPP on your own machine, you can exclude "localhost" traffic in the Antivirus settings.</li>
        </ul>
      </p>
      </dd>

      <dt>Why doesn't the Apache server start on my system?</dt>
      <dd>
      <p>This problem can be one of several reasons:</p>
      <p>
        <ul>
          <li>You have started more then one HTTP Server (IIS, Sambar, ZEUS and so on). Only one Server can use port 80. This error message indicate the problem:<br/>
<code>(OS 10048)... make_sock: could not bind to adress 0.0.0.0:80
no listening sockets available, shutting down</code></li>
          <li>You have other software, such as the Internet Telephone "Skype" which also blocks the port 80. If the problem is "Skype", you can go in Skype to Actions --> Options --> Connection --> remove the check mark at "use port 80 for an alternate port" and restart Skype. Now it should work.</li>
          <li>You have a firewall which blocks the Apache port. Not all firewalls are compatible with Apache, and sometimes deactivating the firewall is not enough and you must deinstall it. This error message indicates a firewall:<br/>
<code>(OS 10038)Socket operation on non-socket: make_sock: for address 0.0.0.0:80,
apr_socket_opt_set: (SO_KEEPALIVE)</code></li>
        </ul>
        <p>Also if Apache can start, but your browser can't connect to it it could be due to one of the following:</p>
        <ul>
          <li>Some virus scanners can cause this in the same way that firewalls can interfere.</li>
          <li>You have XP Professional without service pack 1. You must have at least SP1 for XAMPP.</li>
        </ul>
      </p>
      <p><strong>Tip:</strong> If you have problems with used ports, you can try the tool "xampp-portcheck.exe". Maybe it can help.</p>
      </dd>

      <dt>Why is my CPU load for Apache almost 99%?</dt>
      <dd>
      <p>There is one of two scenarios at play here. Either your CPU is maxing out, or you can browser connect to the server, but not see anything (the system is trying unsucessfully to load the page). In either case you can find the following message in the Apache log file:</p>
      <p><code>Child: Encountered too many AcceptEx faults accepting client connections.
winnt_mpm: falling back to 'AcceptFilter none'.</code></p>
      <p>The MPM falls back to a safer implementation, but some client requests were not processed correctly. In order to avoid this error, use "AcceptFilter" with accept filter "none" in the "\xampp\apache\conf\extra\httpd-mpm.conf" file.</p>
      </dd>

      <dt>Why are pictures and style-sheets not displayed?</dt>
      <dd>
      <p>Sometimes there are problems with displaying pictures and style-sheets. Especially if these files are located on a network drive. In this case you can enable (or add) one if the following lines in the file "\xampp\apache\conf\httpd.conf":</p>
      <p><code>EnableSendfile off</br>
EnableMMAP off</code></p>
      <p>This problem can also be caused by programs for regulating bandwidth, like NetLimiter.</p>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To configure XAMPP to use the included sendmail.exe binary for email delivery, follow these steps:</p>
        <ul>
          <li>Edit the XAMPP "php.ini" file. Within this file, find the [mail function] section and replace it with the following directives. Change the XAMPP installation path if needed.
          <code>
          sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
          </code>
          </li>
          <li>Edit the XAMPP "sendmail.ini" file. Within this file, find the [sendmail] section and replace it with the following directives:
          <code>
          smtp_server=smtp.gmail.com
          smtp_port=465
          smtp_ssl=auto
          error_logfile=error.log
          auth_username=<EMAIL>
          auth_password=your-gmail-password
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>Restart the Apache server using the XAMPP control panel.
          </li>
        </ul>
        <p>You can now use PHP's mail() function to send email from your application.</p> 
      </dd>
      
      <dt>How can I set a root password in MySQL?</dt>
      <dd>
      <p>Configure it with the "XAMPP Shell" (command prompt). Open the shell from the XAMPP control pane and execute this command:<code>mysqladmin.exe -u root password secret</code>This sets the root password to 'secret'.</p>
      </dd>

      <dt>Can I use my own MySQL server?</dt>
      <dd>
      <p>Yes. Simply don't start the MySQL from the XAMPP package. Please note that two servers cannot be started on the same port. If you have set a password for "root", please do not forget to edit the file "\xampp\phpMyAdmin\config.inc.php".</p>
      </dd>

      <dt>How do I restrict access to phpMyAdmin from the outside?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>Before you can access the MySQL server, phpMyAdmin will prompt you for a user name and password. Don't forget to set a password for the user "root" first.</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
      <p>To enable remote access to phpMyAdmin, follow these steps:</p>
      <ul>
        <li>Edit the apache\conf\extra\httpd-xampp.conf file in your XAMPP installation directory.</li>
        <li>Within this file, find the lines below. 
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require local
          </code></p>
        </li>
        <li>Then replace 'Require local' with 'Require all granted'.</li>
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require all granted
          </code></p>
        <li>Restart the Apache server using the XAMPP control panel.</li>
      </ul>
      </dd>
      
      <dt>Where is the IMAP support for PHP?</dt>
      <dd>
      <p>As default, the IMAP support for PHP is deactivated in XAMPP due to some mysterious initialization errors with some home versions like Windows 98. If you work with NT systems, you can open the file "\xampp\php\php.ini" to activate the php exstension by removing the beginning semicolon at the line ";extension=php_imap.dll". It should be:</br>
<code>extension=php_imap.dll</code></p>
      <p>Now restart Apache and IMAP should work. You can use the same steps for every extension, which is not enabled in the default configuration.</p>
      </dd>

      <dt>Why don't some PHP open source apps work with XAMPP on Windows?</dt>
      <dd>
      <p>A lot of PHP applications or extensions that have been written for Linux haven’t been ported to Windows. </p>
      </dd>

      <dt>Can I delete the "install" directory after installation?</dt>
      <dd>
      <p>It's better not to. The scripts here are still needed for all additional packages (add-ons) and upgrades of XAMPP.</p>
      </dd>

      <dt>How do I activate the eaccelerator?</dt>
      <dd>
      <p>Like other (Zend) extensions, you can activate it in "php.ini". In this file, enable the line ";zend_extension = "\xampp\php\ext\php_eaccelerator.dll"". It should be:</br>
<code>zend_extension = "\xampp\php\ext\php_eaccelerator.dll"</code></p>
      </dd>

      <dt>How do I fix a connection error to my MS SQL server?</dt>
      <dd>
      <p>If the mssql extension was loaded in the php.ini, sometimes problems appear when only TCP/IP is used. You can fix that problem with a newer "ntwdblib.dll" from Microsoft. Please replace the older file in "\xampp\apache\bin" and "\xampp\php" with the new one. Because of the license, we can't package a newer version of this file with XAMPP.</p>
      </dd>

      <dt>How do I work with the PHP mcrypt extension?</dt>
      <dd>
      <p>For this, we have opened a topic in the forum with examples and possible solutions: <a href="https://community.apachefriends.org/f/viewtopic.php?t=3012">MCrypt topic</a></p>
      </dd>

      <dt>Do Microsoft Active Server Pages (ASP) work with XAMPP?</dt>
      <dd>
      <p>No. And Apache::ASP with the Perl Add-On is not the same. Apache::ASP only knows Perl-Script, but ASP from the Internet Information Server (IIS) also knows the normal VBScript. But for ASP .NET, there is a 3rd party Apache module available.</p>
      </dd>

      <dt>How can I get XAMPP working on port 80 under Windows 10?</dt>
      <dd>
      <p>By default, Windows 10 starts Microsoft IIS on port 80, which is the same default port used by Apache in XAMPP. As a result, Apache cannot bind to port 80.</p>
      <p>To deactivate IIS from running on port 80, follow these steps:</p>
      <ul>
        <li>Open the Services panel in Computer Management.</li>
        <li>Search for the 'World Wide Web Publishing Service' and select it.</li>
        <li>Click the link to 'Stop the service'.</li>
        <li>Double-click the service name.</li>
        <li>In the 'Startup type' field, change the startup type to 'Disabled'.</li>
        <li>Click 'OK' to save your changes.</li>
      </ul>
      <p>You should now be able to start Apache in XAMPP on port 80.</p>
      <p>For more information, refer to the 'Troubleshoot Apache Startup Problems' guide included with XAMPP or <a href='https://community.apachefriends.org/f/viewtopic.php?f=16&t=71620'>this forum post</a>.</p>
      </dd>
      
      <dt>How can I use Microsoft Edge to access local addresses under Windows 10?</dt>
      <dd>
      <p>If your local machine has the host name 'myhost', you will not be able to access URLs such as http://myhost in Microsoft Edge. To resolve this, you should instead use the addresses http://127.0.0.1 or http://localhost.</p>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: \xampp\apache\conf\httpd.conf, \xampp\apache\conf\extra\httpd-xampp.conf</li>
          <li>PHP configuration file: \xampp\php\php.ini</li>
          <li>MySQL configuration file: \xampp\mysql\bin\my.ini</li>
          <li>FileZilla Server configuration file: \xampp\FileZillaFTP\FileZilla Server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\tomcat\conf\server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\sendmail\sendmail.ini</li>
          <li>Mercury Mail configuration file: \xampp\MercuryMail\MERCURY.INI</li>
        </ul>
      </dd>

    </dl>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">ブログ</a></li>
            <li><a href="/privacy_policy.html">個人情報保護方針</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDNから提供
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
