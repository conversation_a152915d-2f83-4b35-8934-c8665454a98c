<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Reset the MySQL/MariaDB Root Password</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_reset-mysql-password">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/reset-mysql-password.pdf">          Download PDF
          <span>reset-mysql-password.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
      <h1>Reset the MySQL/MariaDB Root Password</h1>
<div class="paragraph">
<p>By default, the MySQL/MariaDB installation that ships with XAMPP has an empty root password. This is a serious security risk, especially if you plan to use XAMPP in production scenarios.</p>
</div>
<div class="paragraph">
<p>To change the MySQL/MariaDB root password, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Ensure that the MySQL/MariaDB server is running.</p>
</li>
<li>
<p>Open your Windows command prompt by clicking the "Shell" button in the XAMPP control panel.</p>
<div class="imageblock">
<div class="content">
<img src="./images/reset-mysql-password/image1.png" alt="image1">
</div>
</div>
</li>
<li>
<p>Use the <em>mysqladmin</em> command-line utility to alter the MySQL/MariaDB password, using the following syntax:</p>
<div class="literalblock">
<div class="content">
<pre>mysqladmin --user=root password "newpassword"</pre>
</div>
</div>
<div class="paragraph">
<p>For example, to change the MySQL/MariaDB root password from its default empty value to the password <em>gue55me</em>, you would execute:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>mysqladmin --user=root password "gue55me"</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/reset-mysql-password/image2.png" alt="image2">
</div>
</div>
<div class="paragraph">
<p>Or, if a password has already been previously set and you&#8217;d like to change it to a new one, you can use the following syntax:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>mysqladmin --user=root --password=oldpassword password "newpassword"</pre>
</div>
</div>
<div class="paragraph">
<p>For example, to change the root password from <em>12345</em> to <em>gue55me</em>, you would execute:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>mysqladmin --user=root --password=12345 password "gue55me"</pre>
</div>
</div>
</li>
<li>
<p>Test that your password change has been accepted, by attempting to connect to the MySQL/MariaDB server using the <em>mysql</em> command-line client in the same directory. For example, you could use the command below to connect to the server and return the results of a calculation:</p>
<div class="literalblock">
<div class="content">
<pre>mysql --user=root --password=gue55me -e "SELECT 1+1"</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/reset-mysql-password/image3.png" alt="image3">
</div>
</div>
</li>
</ol>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
