#
# Provide access to the documentation on your server as
#  http://yourserver.example.com/manual/
# The documentation is always available at
#  http://httpd.apache.org/docs/2.4/
#
# Required modules: mod_alias, mod_authz_core, mod_authz_host,
#                   mod_setenvif, mod_negotiation
#

AliasMatch ^/manual(?:/(?:da|de|en|es|fr|ja|ko|pt-br|ru|tr|zh-cn))?(/.*)?$ "C:/xampp/apache/manual$1"

<Directory "C:/xampp/apache/manual">
    Options Indexes
    AllowOverride None
    Require all granted

    <Files *.html>
        SetHandler type-map
    </Files>

    # .tr is text/troff in mime.types!
    RemoveType tr

    # Traditionally, used .dk filename extension for da language
    AddLanguage da .da

    SetEnvIf Request_URI ^/manual/(da|de|en|es|fr|ja|ko|pt-br|ru|tr|zh-cn)/ prefer-language=$1
    RedirectMatch 301 ^/manual(?:/(da|de|en|es|fr|ja|ko|pt-br|ru|tr|zh-cn)){2,}(/.*)?$ /manual/$1$2

    # Reflect the greatest effort in translation (most content available),
    # inferring greater attention to detail (potentially false assumption,
    # counting translations presently in-sync would be more helpful.)
    # Use caution counting; safest pattern is '*.xml.XX'. Recent .xml source
    # document count: 266 214 110 94 82 25 22    18     4  1  1
    LanguagePriority   en  fr  ko ja tr es de zh-cn pt-br da ru
    ForceLanguagePriority Prefer Fallback
</Directory>
