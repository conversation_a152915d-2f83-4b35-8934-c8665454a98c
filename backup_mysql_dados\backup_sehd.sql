-- <PERSON><PERSON><PERSON> dump 10.19  Distrib 10.4.32-MariaDB, for Win64 (AMD64)
--
-- Host: localhost    Database: sehd
-- ------------------------------------------------------
-- Server version	10.4.32-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `aulas`
--

DROP TABLE IF EXISTS `aulas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `aulas` (
  `ID_AULAS` int(11) NOT NULL,
  `AULAS_SEMANAIS` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`ID_AULAS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `aulas`
--

LOCK TABLES `aulas` WRITE;
/*!40000 ALTER TABLE `aulas` DISABLE KEYS */;
INSERT INTO `aulas` VALUES (125,'Introdução ao HTML e CSS'),(126,'JavaScript: Conceitos Básicos'),(127,'Funções e Eventos em JavaScript'),(128,'Manipulação de DOM com JavaScript'),(129,'Projeto Final: Desenvolvimento de uma Página Web'),(132,'1'),(133,'2');
/*!40000 ALTER TABLE `aulas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `coordenador`
--

DROP TABLE IF EXISTS `coordenador`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `coordenador` (
  `ID_COORDENADOR` int(11) NOT NULL,
  `RA` int(11) DEFAULT NULL,
  `NOME_COORDENADOR` varchar(100) DEFAULT NULL,
  `data_nasc_coord` varchar(32) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `ID_DISPONIBILIDADE` int(11) DEFAULT NULL,
  `ID_DISCIPLINA` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_COORDENADOR`),
  KEY `ID_CURSO` (`ID_CURSO`),
  KEY `ID_DISCIPLINA` (`ID_DISCIPLINA`),
  KEY `ID_DISPONIBILIDADE` (`ID_DISPONIBILIDADE`),
  CONSTRAINT `coordenador_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`),
  CONSTRAINT `coordenador_ibfk_2` FOREIGN KEY (`ID_DISCIPLINA`) REFERENCES `disciplina` (`ID_DISCIPLINA`),
  CONSTRAINT `coordenador_ibfk_3` FOREIGN KEY (`ID_DISPONIBILIDADE`) REFERENCES `disponibilidade` (`ID_DISPONIBILIDADE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `coordenador`
--

LOCK TABLES `coordenador` WRITE;
/*!40000 ALTER TABLE `coordenador` DISABLE KEYS */;
INSERT INTO `coordenador` VALUES (1,12345,'Jenni','186a8bab269a3e64e01767b76beba61e',1,1,17),(6,3022106,'Rafael Almeida','b614e537987bbba6361270ab4cb935b6',1,1,17),(7,3022107,'Marina Costa','edb20917739d6268cc8f324883704ba9',2,2,18),(8,3022108,'Lucas Fernandes','029e508b4efdde00682f3aafc8f59be0',3,3,19);
/*!40000 ALTER TABLE `coordenador` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `curso`
--

DROP TABLE IF EXISTS `curso`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `curso` (
  `ID_CURSO` int(11) NOT NULL,
  `NOME_CURSO` varchar(100) DEFAULT NULL,
  `QTD_AULAS` int(11) DEFAULT NULL,
  `INICIOPERIODO` date DEFAULT NULL,
  `FIMPERIODO` date DEFAULT NULL,
  PRIMARY KEY (`ID_CURSO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `curso`
--

LOCK TABLES `curso` WRITE;
/*!40000 ALTER TABLE `curso` DISABLE KEYS */;
INSERT INTO `curso` VALUES (1,'Desenvolvimento Web com JavaScript',40,'2025-06-01','2025-08-15'),(2,'Introdução à Cibersegurança',30,'2025-07-01','2025-09-01'),(3,'Banco de Dados com MySQL',35,'2025-06-10','2025-08-20'),(4,'Redes de Computadores e Protocolos',45,'2025-07-05','2025-09-25'),(5,'Análise de Dados com Python',40,'2025-08-01','2025-10-15');
/*!40000 ALTER TABLE `curso` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `diretor`
--

DROP TABLE IF EXISTS `diretor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `diretor` (
  `ID_DIRETOR` int(11) NOT NULL,
  `RA` int(11) DEFAULT NULL,
  `NOME_DIRETOR` varchar(100) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `data_nasc_diretor` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`ID_DIRETOR`),
  KEY `ID_CURSO` (`ID_CURSO`),
  CONSTRAINT `diretor_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `diretor`
--

LOCK TABLES `diretor` WRITE;
/*!40000 ALTER TABLE `diretor` DISABLE KEYS */;
INSERT INTO `diretor` VALUES (1,666,'Chifronesio',1,'c4da8783a949a532e1651e5ba13dc75b');
/*!40000 ALTER TABLE `diretor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `disciplina`
--

DROP TABLE IF EXISTS `disciplina`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `disciplina` (
  `ID_DISCIPLINA` int(11) NOT NULL,
  `NOME_DISCIPLINA` varchar(100) DEFAULT NULL,
  `CARGA_HORARIA` int(11) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `ID_AULAS` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_DISCIPLINA`),
  KEY `ID_CURSO` (`ID_CURSO`),
  KEY `ID_AULAS` (`ID_AULAS`),
  CONSTRAINT `disciplina_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`),
  CONSTRAINT `disciplina_ibfk_2` FOREIGN KEY (`ID_AULAS`) REFERENCES `aulas` (`ID_AULAS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `disciplina`
--

LOCK TABLES `disciplina` WRITE;
/*!40000 ALTER TABLE `disciplina` DISABLE KEYS */;
INSERT INTO `disciplina` VALUES (17,'Introdução ao Python',1,1,125),(18,'Bibliotecas NumPy e Pandas',2,2,126),(19,'Visualização de Dados',3,3,127),(20,'Projeto Final com Python',4,4,128),(22,'bom dia',30,3,133);
/*!40000 ALTER TABLE `disciplina` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `disponibilidade`
--

DROP TABLE IF EXISTS `disponibilidade`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `disponibilidade` (
  `ID_DISPONIBILIDADE` int(11) NOT NULL,
  `HORARIO_INICIO` time DEFAULT NULL,
  `HORARIO_FIM` time DEFAULT NULL,
  `DIA_SEMANA` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`ID_DISPONIBILIDADE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `disponibilidade`
--

LOCK TABLES `disponibilidade` WRITE;
/*!40000 ALTER TABLE `disponibilidade` DISABLE KEYS */;
INSERT INTO `disponibilidade` VALUES (1,'19:00:00','23:30:00','Segunda-feira'),(2,'19:00:00','23:30:00','Terça-feira'),(3,'19:00:00','23:30:00','Quarta-feira'),(4,'19:00:00','23:30:00','Quinta-feira'),(5,'19:00:00','23:30:00','Sexta-feira');
/*!40000 ALTER TABLE `disponibilidade` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login`
--

DROP TABLE IF EXISTS `login`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `login` (
  `ID_LOGIN` int(11) NOT NULL,
  `RA` int(11) NOT NULL,
  `senha` varchar(40) NOT NULL,
  PRIMARY KEY (`ID_LOGIN`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login`
--

LOCK TABLES `login` WRITE;
/*!40000 ALTER TABLE `login` DISABLE KEYS */;
INSERT INTO `login` VALUES (1,12345678,'aba2fbf2a2eed491f28c5a2655a06f50');
/*!40000 ALTER TABLE `login` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `matriz`
--

DROP TABLE IF EXISTS `matriz`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `matriz` (
  `ID_MATRIZ` int(11) NOT NULL,
  `ID_AULAS` int(11) DEFAULT NULL,
  `SEMESTRE` int(11) DEFAULT NULL,
  `HORA_ANO` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_MATRIZ`),
  KEY `ID_AULAS` (`ID_AULAS`),
  CONSTRAINT `matriz_ibfk_1` FOREIGN KEY (`ID_AULAS`) REFERENCES `aulas` (`ID_AULAS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `matriz`
--

LOCK TABLES `matriz` WRITE;
/*!40000 ALTER TABLE `matriz` DISABLE KEYS */;
/*!40000 ALTER TABLE `matriz` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `professor`
--

DROP TABLE IF EXISTS `professor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `professor` (
  `ID_PROFESSOR` int(11) NOT NULL,
  `NOME_PROFESSOR` varchar(100) DEFAULT NULL,
  `data_nasc_prof` varchar(32) DEFAULT NULL,
  `RA` int(11) DEFAULT NULL,
  `ID_CURSO` int(11) DEFAULT NULL,
  `ID_DISPONIBILIDADE` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID_PROFESSOR`),
  KEY `ID_CURSO` (`ID_CURSO`),
  KEY `ID_DISPONIBILIDADE` (`ID_DISPONIBILIDADE`),
  CONSTRAINT `professor_ibfk_1` FOREIGN KEY (`ID_CURSO`) REFERENCES `curso` (`ID_CURSO`),
  CONSTRAINT `professor_ibfk_2` FOREIGN KEY (`ID_DISPONIBILIDADE`) REFERENCES `disponibilidade` (`ID_DISPONIBILIDADE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `professor`
--

LOCK TABLES `professor` WRITE;
/*!40000 ALTER TABLE `professor` DISABLE KEYS */;
INSERT INTO `professor` VALUES (0,'gabi','aba2fbf2a2eed491f28c5a2655a06f50',123456,2,NULL),(1,'Ana Beatriz Martins','cc89ae86f8997ee59a77a4d3e42f8270',2023101,1,1),(2,'Carlos Henrique Souza','1b7b5b68cc0763ef71ef995119cdf4e3',2023102,2,2),(3,'Fernanda Lopes da Silva','1502d4f820259de9bf8fba4089893235',2023103,3,3),(4,'João Paulo Andrade','8fa5b98d9340b873e96a9674cf8222ba',2023104,4,4),(5,'Juliana Ribeiro Costa','e25d81683940fe9c06a61cee4da62e6a',2023105,1,5),(6,'enzo','adab913880ccf3353b236ab29cab8673',1234,4,NULL);
/*!40000 ALTER TABLE `professor` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-30 18:59:38
