<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Activate and Use Xdebug</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_activate-use-xdebug">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/activate-use-xdebug.pdf">          Download PDF
          <span>activate-use-xdebug.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
      <h1>Activate and Use Xdebug</h1>
<div class="paragraph">
<p><a href="http://xdebug.org/">Xdebug</a> is a powerful open source debugger and profiler for PHP. It is included with XAMPP and can be used to display stack traces, analyze code coverage and profile your PHP code.</p>
</div>
<div class="paragraph">
<p>To activate Xdebug, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Edit the <em>php.ini</em> file in the <em>php\</em> subdirectory of your XAMPP installation directory (usually, <em>C:\xampp</em>). Within this file, find the [XDebug] section and within it, add the following configuration directive (or modify the existing one):</p>
<div class="literalblock">
<div class="content">
<pre>zend_extension = "C:/xampp/php/ext/php_xdebug.dll"</pre>
</div>
</div>
</li>
<li>
<p>Restart the Apache server using the XAMPP control panel.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>Xdebug should now be active. To verify this, browse to the URL <a href="http://localhost/xampp/phpinfo.php" class="bare">http://localhost/xampp/phpinfo.php</a>, which displays the output of the <em>phpinfo(</em>) command. Look through the script and verify that the Xdebug extension is now active.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/activate-use-xdebug/image1.png" alt="image1">
</div>
</div>
<div class="paragraph">
<p>Xdebug overloads the default <em>var_dump(</em>) function with its own version that includes (among other things) color coding for different PHP types, so you can see it in action immediately by using the <em>var_dump()</em> function in a PHP script. For example, create a simple PHP script in the <em>htdocs\</em> subdirectory of your XAMPP installation directory with the following content:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>&lt;?php
var_dump($_SERVER);</pre>
</div>
</div>
<div class="paragraph">
<p>When you view your script through a browser, here&#8217;s an example of what you might see:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/activate-use-xdebug/image2.png" alt="image2">
</div>
</div>
<div class="paragraph">
<p>One of Xdebug&#8217;s most powerful features is its ability to profile a PHP script and produce detailed statistics on how long each function call or line of code takes to execute. This can be very useful for performance analysis of complex scripts. To turn on script profiling, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Edit the <em>php.ini</em> file in the <em>php\</em> subdirectory of your XAMPP installation directory. Within this file, find the [XDebug] section and within it, uncomment and modify the lines below so that they look like this:</p>
<div class="literalblock">
<div class="content">
<pre>xdebug.profiler_append = 0
xdebug.profiler_enable = 1
xdebug.profiler_enable_trigger = 0
xdebug.profiler_output_dir = "C:/xampp/tmp"
xdebug.profiler_output_name = "cachegrind.out.%t-%s"</pre>
</div>
</div>
</li>
<li>
<p>Restart the Apache server using the XAMPP control panel.</p>
</li>
</ol>
</div>
<div class="paragraph">
<p>At this point, Xdebug profiling is active. Every PHP script that you run will be profiled and the results will be placed in the <em>C:\xampp\tmp</em> as a so-called cachegrind file. You can view this cachegrind file with a tool like <a href="http://sourceforge.net/projects/wincachegrind/">WinCacheGrind</a>, which you must download and install separately.</p>
</div>
<div class="paragraph">
<p>To illustrate how this works, consider the screenshot below, which shows the profiled output of a script using WinCacheGrind. As the screenshot illustrates, it&#8217;s easy to see the entire life cycle of a PHP script, including the call sequence and the amount of time taken by each function, and thereby find targets for further optimization.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/activate-use-xdebug/image3.png" alt="image3">
</div>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
To find out more about Xdebug’s powerful features, <a href="http://xdebug.org/docs/">read the Xdebug documentation</a>.
</td>
</tr>
</table>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
