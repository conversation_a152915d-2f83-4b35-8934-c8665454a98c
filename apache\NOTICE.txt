Apache HTTP Server
Copyright 2023 The Apache Software Foundation.

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

Portions of this software were developed at the National Center
for Supercomputing Applications (NCSA) at the University of
Illinois at Urbana-Champaign.

This software contains code derived from the RSA Data Security
Inc. MD5 Message-Digest Algorithm, including various
modifications by Spyglass Inc., Carnegie Mellon University, and
Bell Communications Research, Inc (Bellcore).

This software contains code derived from the PCRE library pcreposix.c
source code, written by <PERSON>, Copyright 1997-2004
by the University of Cambridge, England.

Regular expression support is provided by the PCRE library package,
which is open source software, written by <PERSON>, and copyright
by the University of Cambridge, England. The original software is
available from
  ftp://ftp.csx.cam.ac.uk/pub/software/programming/pcre/

This binary distribution includes cryptographic software written by
<PERSON> (<EMAIL>), software written by <PERSON> 
(<EMAIL>), and software developed by the OpenSSL Project 
for use in the OpenSSL Toolkit <http://www.openssl.org/>.

This binary distribution of mod_deflate.so includes zlib compression code
<http://www.gzip.org/zlib/> written by <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
and <PERSON> (<EMAIL>) .

This binary distribution of mod_lua.so includes the Lua language, 
developed at Lua.org, a laboratory of the Department of Computer Science 
of PUC-Rio (the Pontifical Catholic University of Rio de Janeiro in Brazil).
For complete information, visit Lua's web site at http://www.lua.org/

This binary distributions of mod_proxy_html.so and mod_xml2enc.so include the
libxml2 C library written by Daniel Veillard (daniel veillard.com), Bjorn 
Reese (breese users.sourceforge.net) and Gary Pennington (Gary.Pennington 
uk.sun.com). For complete information, visit LibXML2's web site at 
https://http://www.xmlsoft.org/

This binary distribution of mod_http2.so includes nghttp2 C library written 
by Tatsuhiro Tsujikawa. For complete information, visit nghttp2's web site 
at https://nghttp2.org/

This binary distribution of mod_brotli.so includes Brotli C library written 
by the Brotli Authors. For complete information, visit Brotli's web site 
at https://github.com/google/brotli

This binary distribution of mod_md.so includes Curl C library written by 
Daniel Stenberg and many contributors. For complete information, 
visit curl's web site at https://curl.haxx.se/

This binary distribution of mod_md.so includes Jansson C library written 
by the  Petri Lehtinen. For complete information, visit Jansson's web site 
at http://www.digip.org/jansson/
