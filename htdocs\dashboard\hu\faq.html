<!doctype html>
<html lang="hu">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Windows</title>

    <meta name="description" content="Instructions on how to install XAMPP for Windows distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="hu hu_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/hu/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/hu/faq.html">Gyakori kérdések</a></li>
              <li class="item "><a href="/dashboard/hu/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Windows <span>Gyakran ismételt kérdések</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
      <dt>Hogyan telepítsem a XAMPP-ot?</dt>
      <dd>
      <p>A XAMPP Windows változata három különböző formában létezik:</p>
      <p>Telepítő:<br />
      Valószínűleg a legegyszerűbb módja a XAMPP telepítésének.</p>
      <p>ZIP:<br />
      Puristáknak: a XAMPP szokásos ZIP archívumként.</p>
      <p>7zip:<br />
      Puristáknak kis sávszélességgel: a XAMPP 7zip archívumként.</p>
      <p>Megjegyzés: A fájlok kibontásakor előfordulhatnak hamis pozitív vírusriasztások.</p>
      <p><strong>A telepítő használata:</strong></p>
      <p></p>
      <p>Használja a Xampp vezérlőpultot az Apache, MySQL, FileZillaés a Mercury szolgáltatások elindításához és leállításához vagy a server szolgáltatások telepítéséhez.</p>
      <p><strong>Telepítés ZIP-ből</strong></p>
      <p>Bontsa ki a zip archívumokat a választott mappába. A XAMPP a „C:\\xampp” alkönyvtárba kerül kibontásra a választott célkönyvtár alá. Most indítsa el a „setup_xampp.bat” fájlt, hogy elvégezze a XAMPP beállítását a rendszeréhez.</p>
      <p>Ha a „C:\\” gyökérkönyvtárat válassza célnak, akkor nem szabad a „setup_xampp.bat” fájlt futtatnia.</p>
      <p>Mint a telepítő verzióban, most már használhatja a „XAMPP vezérlőpultot” a további feladatokhoz.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
      <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>Hogyan indíthatom a XAMPP programot beállítás nélkül?</dt>
      <dd>
      <p>Ha a XAMPP programot egy felsőszintű mappába bontotta ki, mint például „C:\\” vagy „D:\\”, akkor a legtöbb szolgáltatást - úgymint Apache vagy MySQL - közvetlenül is el tudja indítani a „setup_xampp.bat” fájl futtatása nélkül.</p>
      <p>Nem javasolt a beállító parancsfájl használata, vagy relatív elérési utak kiválasztása a beállító parancsfájlban, ha a XAMPP programot USB-meghajtóra telepítette. Ennek oka, hogy minden PC más meghajtóbetűt rendelhet a meghajtónak. Bármikor átválthat abszolút útvonalakról relatívakra a beállító parancsfájllal.</p>
      <p>A Letöltések oldalról elérhető telepítő használata a legegyszerűbb módja a XAMPP telepítésének. A telepítés befejezése után a Start | Programok | XAMPP helyen fogja megtalálni a XAMPP programot. A XAMPP vezérlőpultot használhatja az összes kiszolgáló indításához és leállításához, valamint szolgáltatások telepítéséhez és eltávolításához.</p>
      <p>Használja a Xampp vezérlőpultot az Apache, MySQL, FileZillaés a Mercury szolgáltatások elindításához és leállításához vagy a server szolgáltatások telepítéséhez.</p>
      </dd>
      <dt>Hogyan indíthatom el és állíthatom le a XAMPP-ot?</dt>
      <dd>
      <p>Az egyetemes vezérlőpult a „XAMPP vezérlőpult” (köszönet: www.nat32.com). Az alábbi módon indítható:</p>
      <p><code>\xampp\xampp-control.exe</code></p>
      <p>Használhat néhány kötegfájlt is a kiszolgálók indításához és leállításához:</p>
      <p>
      <ul>
        <li>Apache és MySQL indítás:
        <code>\xampp\xampp_start.exe</code></li>
        <li>Apache és MySQL leállítás:
        <code>\xampp\xampp_stop.exe</code></li>
        <li>Apache indítás:
        <code>\xampp\apache_start.bat</code></li>
        <li>Apache leállítás:
        <code>\xampp\apache_stop.bat</code></li>
        <li>MySQL indítás:
        <code>\xampp\mysql_start.bat</code></li>
        <li>MySQL leállítás:
        <code>\xampp\mysql_stop.bat</code></li>
        <li>Mercury levelezőkiszolgáló indítás:
        <code>\xampp\mercury_start.bat</code></li>
        <li>Mercury levelezőkiszolgáló leállítás:
        <code>\xampp\mercury_stop.bat</code></li>
        <li>FileZilla kiszolgáló indítás:
        <code>\xampp\filezilla_start.bat</code></li>
        <li>FileZilla kiszolgáló leállítás:
        <code>\xampp\filezilla_stop.bat</code></li></ul></p>
      </p>
      </dd>
      <dt>Hogyan ellenőrizhetem, hogy minden működik-e?</dt>
      <dd>
      <p>Gépelje be a következő URL-t a kedvenc webböngészőjébe:</p>
      <p><code>http://localhost/</code> vagy  <code>http://127.0.0.1/</code> </p>
      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-windows-start.jpg" />
      </dd>
      <dt>Hogyan tudok egy kiszolgálót szolgáltatásként telepíteni?</dt>
      <dd>
      <p>A XAMPP minden kiszolgálója Windows szolgáltatásként is telepíthető. Ezt is a XAMPP vezérlőpultból tudja telepíteni. Ebben az esetben a parancsfájlok vagy a vezérlőpult futtatása rendszergazdai jogosultságokkal szükséges.</p>
      <p>Apache szolgáltatás telepítés: \\xampp\\apache\\apache_installservice.bat</p>
      <p>Apache szolgáltatás eltávolítás: \\xampp\\apache\\apache_uninstallservice.bat </p>
      <p>MySQL szolgáltatás telepítés: \\xampp\\mysql\\mysql_installservice.bat </p>
      <p>MySQL szolgáltatás eltávolítás: \\xampp\\mysql\\mysql_uninstallservice.bat </p>
      <p>FileZilla szolgáltatás telepítés és eltávolítás: \\xampp\\filezilla_setup.bat </p>
      <p>Mercury: nem érhető el szolgáltatás telepítés</p>
      </dd>
      <dt>A XAMPP készen áll a munkához?</dt>
      <dd>
      <p>A XAMPP nem a produktív használatot jelenti, ez csak egy fejlesztői környezet. A XAMPP oly módon lett beállítva, hogy annyira nyitott legyen, amennyire csak lehetséges, hogy lehetővé tegyen a fejlesztőknek bármit, amit szeretnének. Ez nagyszerű egy fejlesztői környezethez, de produktív környezetben végzetes lehet.</p>
      <p>Itt egy lista a XAMPP hiányzó biztonsági dolgairól:</p>
      <ol>
        <li>A MySQL adminisztrátornak (root) nincs jelszava.</li>
        <li>A MySQL démon elérhető a hálózaton keresztül.</li>
        <li>A ProFTPD a „lampp” jelszót használja a „daemon” felhasználóhoz.</li>
        <li>A Mercury és FileZilla alapértelmezett felhasználói ismertek.</li>
      </ol>
      <p>Minden pont hatalmas biztonsági kockázat lehet. Különösen akkor, ha a XAMPP elérhető a hálózaton keresztül és a LAN-on kívüli emberek számára. Segíthet azonban egy tűzfal vagy egy router (NAT) használata. Egy router vagy egy tűzfal esetén a PC normális esetben nem érhető el a hálózaton keresztül. Öntől függ, hogy kijavítja-e ezeket a hibákat. Egy kis segítségként létezik egy „XAMPP biztonsági konzol”.</p>
      <p>Kérjük tegye biztonságossá a XAMPP programot, mielőtt bármit közzétenne online. Egy tűzfal vagy egy külső router csak alacsony szintű biztonsághoz elegendő. A valamivel nagyobb biztonság érdekében futtassa a „XAMPP biztonsági konzolt” és rendeljen hozzá jelszavakat.</p>
      <p>Ha elérhetővé szeretné tenni a XAMPP programot az interneten, akkor látogassa meg a következő URI-t, amely kijavíthat néhány problémát:</p>
      <p><code> http://localhost/security/</code></p>
      <p>A biztonsági konzollal beállíthatja a MySQL „root” felhasználó és a phpMyAdmin jelszavát. Továbbá bekapcsolhatja a hitelesítést a XAMPP bemutató oldalakhoz.</p>
      <p>Ez a webalapú eszköz nem javít ki semmilyen egyéb biztonsági problémát! Különösen a FileZilla FTP-kiszolgáló és a Mercury levelezőkiszolgáló biztonságáról kell önnek gondoskodnia.</p></dd>
      <dt>Hogyan távolíthatom el a XAMPP-ot?</dt>
      <dd>
      <p>Amennyiben a telepítős verzióval telepítette a XAMPP programot, használja az eltávolítót. Az eltávolító el fog távolítani minden XAMPP bejegyzést a rendszerleíró adatbázisból, illetve el fogja távolítani a XAMPP részeként telepített szolgáltatásokat. Erősen ajánljuk, hogy az eltávolító használatával távolítsa el a telepítős verziójú XAMPP telepítést. Készítsen biztonsági mentést a XAMPP eltávolítása előtt minden olyan adatról, amelyet meg szeretne tartani.</p>
      <p>Amennyiben a ZIP és a 7zip verziókkal telepítette a XAMPP programot, állítson le minden XAMPP kiszolgálót és lépjen ki minden panelből. Ha telepített valamilyen szolgáltatást, távolítsa el és állítsa le azokat is. Most egyszerűen törölje az egész mappát, ahova a XAMPP programot telepítette. Nincsenek törlendő rendszerleíró adatbázis bejegyzések és nincsenek törlendő környezeti változók.</p>
      </dd>
      <dt>Mi a XAMPP „lite” verziója?</dt>
      <dd>
      <p>A XAMPP Lite (amely „könnyűt” jelent, úgymint „könnyűsúlyú”) a XAMPP komponensek kisebb csomagja, amely kizárólag PHP és MySQL használatával történő gyors munkavégzéshez ajánlott. Néhány kiszolgáló vagy eszköz, mint például a Mercury levelező és a FileZilla FTP hiányoznak a Lite verzióból.</p>
      </dd>
      <dt>Hová tegyem a webes tartalmakat?</dt>
      <dd>
      <p>Minden WWW dokumentum fő könyvtára a \\xampp\\htdocs. Ha ebbe a könyvtárba teszi a „teszt.html” fájlt, akkor a „http://localhost/teszt.html” URI segítségével férhet hozzá.</p>
      <p>És a „teszt.php”? Csak használja a „http://localhost/teszt.php” hivatkozást. Egy egyszerű teszt-parancsfájl lehet:</p>
      <p><code>&lt;?php<br />
        echo 'Hello world'; <br />
        ?&gt;</code></p>
      <p>Egy új alkönyvtár a webhez? Csak hozzon létre egy új könyvtárat (például „valami”) a „\\xampp\\htdocs” könyvtáron belül (a legjobb szóközök nélkül és csak ASCII karakterekkel), hozzon létre egy tesztfájlt ebben a könyvtárban és elérheti a „http://localhost/valami/teszt.php” címen.</p>
      <p><strong>További specifikációk</strong></p>
      <p>HTML:<br>
      Programfájl: \xampp\htdocs<br>
      Engedélyezett végződések: .html .htm<br>
      => alap csomag</p>
      <p>SSI:<br>
      Programfájl: \xampp\htdocs<br>
      Engedélyezett végződések: .shtml<br>
      => alap csomag</p>
      <p>CGI:<br>
      Programfájl: \xampp\htdocs and \xampp\cgi-bin<br>
      Engedélyezett végződések: .cgi<br>
      => alap csomag</p>
      <p>PHP:<br>
      Programfájl: \xampp\htdocs and \xampp\cgi-bin<br>
      Engedélyezett végződések: .php<br>
      => alap csomag</p>
      <p>Perl:<br>
      Programfájl: \xampp\htdocs and \xampp\cgi-bin<br>
      Engedélyezett végződések: .pl<br>
      => alap csomag</p>
      <p>Apache::ASP Perl:<br>
      Programfájl: \xampp\htdocs<br>
      Engedélyezett végződések: .asp<br>
      => alap csomag</p>
      <p>JSP Java:<br>
      Programfájl: \xampp\tomcat\webapps\java (e.g.)<br>
      Engedélyezett végződések: .jsp<br>
      => Tomcat add-on</p>
      <p>Servlets Java:<br>
      Programfájl: \xampp\tomcat\webapps\java (e.g.)<br>
      Engedélyezett végződések: .html (u.a)<br>
      => Tomcat add-on</p>
      </dd>
      <dt>Áthelyezhetem a XAMPP telepítését?</dt>
      <dd>
      <p>Igen. A XAMPP könyvtár áthelyezése után le kell futtatnia a „setup_xampp.bat” parancsfájlt. A beállítófájlban lévő elérési út ehhez a lépéshez lesz igazítva.</p>
      <p>Amennyiben valamilyen kiszolgálót Windows szolgáltatásként telepített, akkor először el kell távolítania a Windows szolgáltatást, majd az áthelyezés után ismét telepítheti azt.</p>
      <p>Figyelem: a saját parancsfájljaiban lévő beállítófájlok, mint például PHP alkalmazások, nem lesznek hozzáigazítva. De lehetséges egy „bővítmény” írása a telepítőhöz. Egy ilyen bővítménnyel a telepítő ezeket a fájlokat is hozzá tudja igazítani.</p>
      </dd>
      <dt>Mik az „Automatikus nyitóoldalak” a WWW könyvtárakhoz?</dt>
      <dd>
      <p>A szabványos fájlnév a „DirectoryIndex” Apache függvényhez „index.html” vagy „index.php”. Minden esetben, amikor rátallóz egy mappára (például „http://localhost/xampp/”), és az Apache talál egy ilyen fájlt, akkor az Apache ezt a fájlt jeleníti meg a könyvtár listázása helyett.</p>
      </dd>
      <dt>Hol tudom megváltoztatni a beállításokat?</dt>
      <dd>
      <p>A XAMPP majdnem minden beállítása megváltoztatható beállítófájlokkal. Csak nyissa meg a fájlt egy szövegszerkesztővel és változtassa meg azt a beállítást, amelyet szeretne. Csak a FileZilla és a Mercury beállítását kell az alkalmazásbeállító eszközzel elvégezni.</p>
      </dd>

      <dt>Miért nem működik a XAMPP Windows XP SP2 alatt?</dt>
      <dd>
      <p>A Microsoft egy jobb tűzfalat szállított a 2. javítócsomaggal (SP2), amely automatikusan elindul. Ez a tűzfal már blokkolja a szükséges 80-as (http) és 443-as (https) portokat, és az Apache nem tud elindulni.</p>
      <p><strong>A gyors megoldás:</strong></p>
      <p>Tiltsa le a Microsoft tűzfalat az eszköztárral, és próbálja meg még egyszer elindítani a XAMPP programot. A jobb megoldás egy kivétel meghatározása a biztonsági központon belül.</p>
      <p><strong>A következő portok vannak használatban az alap működéshez:</strong></p>
      <p>Apache (HTTP): Port 80<br/>
        Apache (WebDAV): Port 81</br>
        Apache (HTTPS): Port 443</br>
        MySQL: Port 3306</br>
        FileZilla (FTP): Port 21</br>
        FileZilla (Admin): Port 14147</br>
        Mercury (SMTP): Port 25</br>
        Mercury (POP3): Port 110</br>
        Mercury (IMAP): Port 143</br>
        Mercury (HTTP): Port 2224</br>
        Mercury (Finger): Port 79</br>
        Mercury (PH): Port 105</br>
        Mercury (PopPass): Port 106</br>
        Tomcat (AJP/1.3): Port 8009</br>
        Tomcat (HTTP): Port 8080</p>
      </dd>

      <dt>Miért nem működik a XAMPP Vistán?</dt>
      <dd>
      <p><strong>Felhasználói fiókok felügyelete (UAC)</strong></p>
      <p>A „C:\\Program files” könyvtárra nincs teljes írási jogosultsága, kivéve rendszergazdaként. Vagy csak korlátozott jogosultságai vannak (például a „.\\xampp\\htdocs” mappára). Ebben az esetben nem tudja szerkeszteni a fájlt.</br>
<strong>Megoldás:</strong> Emelje meg a jogosultságait az exploreren belül (jobb kattintás / biztonság), vagy tiltsa le a felhasználói fiókok felügyeletét (UAC).</p>
      <p>Az Apache/MySQL kiszolgálókat a „C:\\xampp” helyre telepítette Windows szolgáltatásként. De nem tudja elindítani vagy leállítani a szolgáltatásokat a „XAMPP vezérlőpulttal”, vagy nem tudja eltávolítani azokat.</br></br>
<strong>Megoldás:</strong> Használja a Windows szolgáltatáskezelő konzolját vagy tiltsa le az UAC-ot.</p>
      <p><strong>A felhasználói fiókok felügyeletének (UAC) letiltása</strong></p>
      <p>Az UAC letiltásához használja az „msconfig” programot. Az „msconfig” programban menjen az „Eszközökre”, válassza a „felhasználói fiókok felügyeletének letiltása” lehetőséget, és erősítse meg a választását. Ezután újra kell indítania a Windowst. Ezzel egy időben ismét engedélyezheti az UAC-ot.</p>
      </dd>

      <dt>Hogyan ellenőrizhetem az MD5 ellenőrzőösszeget?</dt>
      <dd>
      <p>Fájlok összehasonlításához gyakran ellenőrzőösszegeket használnak. A szabvány az MD5 (Message Digest Algorithm 5) ellenőrzőösszeg létrehozása.</p>
      <p>Ezzel az MD5 ellenőrzőösszeggel tesztelheti, hogy a XAMPP csomag letöltése helyes volt-e vagy sem. Természetesen szüksége van egy olyan programra, amely elő tudja állítani ezeket az ellenőrzőösszegeket. A Windowsnál használhatja a Microsoft eszközét:</p>
      <p><a href="http://support.microsoft.com/kb/841290">Elérhetőség és fájl ellenőrzőösszeg integritás leírása ellenőrző segédprogram</a></p>
      <p>Lehetséges bármely más olyan program használata is, amely létre tud hozni MD5 ellenőrzőösszegeket, úgymint a GNU md5sum.</p>
      <p>Ha telepített egy ilyen programot (például fciv.exe), végezze el a következő lépéseket:</p>
      <p>
        <ul>
          <li>Töltse le a XAMPP csomagot (például xampp-win32-1.8.2-0.exe)</li>
          <li>Hozza létre az ellenőrzőösszeget ezzel:</br>
            <code>fciv.exe xampp-win32-1.8.2-0.exe</code>
          </li>
          <li>És most már össze tudja hasonlítani ezt az ellenőrzőösszeget azzal, amelyet a Windows rendszerre készített XAMPP honlapján talál.</li>
        </ul>
      </p>
      <p>Ha mindkét ellenőrzőösszeg azonos, akkor minden rendben van. Ha nem, akkor a letöltött fájl sérült vagy a fájl módosult.</p>
      </dd>

      <dt>Miért nem érvényesülnek a php.ini fájlban elvégzett módosítások?</dt>
      <dd>
      <p>Ha a „php.ini” fájlban elvégzett módosításnak nincs hatása, lehetséges, hogy a PHP egy másikat használ. Ezt a phpinfo() függvénnyel ellenőrizheti. Látogassa meg a http://localhost/xampp/phpinfo.php oldalt, és keressen rá a „Loaded Configuration File” kifejezésre. Ez az érték jeleníti meg azt a „php.ini” fájlt, amelyet a PHP igazából használ.</p>
      <p><strong>Megjegyzés:</strong> A „php.ini” módosítása után újra kell indítania az Apache webkiszolgálót, hogy az Apache/PHP be tudja olvasni az új beállításokat.</p>
      </dd>

      <dt>Segítség! Vírusos a XAMPP!</dt>
      <dd>
      <p>Néhány vírusirtó program hibásan vírusnak érzékeli a XAMPP-ot, tipikusan a xampp-manager.exe fájlt megjelölve. Ez egy vakriasztás, amely azt jelenti, hogy a vírusirtó tévesen vírusként azonosítja, miközben nem az. A XAMPP minden egyes új verziójának kiadása előtt lefuttatunk egy víruskereső programot. Jelenleg ezt használjuk: <a href="http://www.kaspersky.com/virusscanner">Kapersky Online Virus Scanner</a>. You can also use the online tool <a href="https://www.virustotal.com/">Virus Total</a> for scanning XAMPP or send us an email to security (at) apachefriends (dot) org if you find any issue.</p>
      </dd>

      <dt>Hogyan állítsam be a vírusirtó alkalmazásomat?</dt>
      <dd>
      <p>Minden függőséget és kiszolgálót mellékelünk, amely a csomagolt webalkalmazás futtatásához szükséges, így a XAMPP telepítésben nagy számú fájlt fog találni. Amennyiben a XAMPP alkalmazást egy olyan Windowst futtató számítógépre telepíti, amelyen bekapcsolt vírusirtó alkalmazás van, akkor ez jelentősen lelassíthatja a telepítést, és megvan az esély arra, hogy a kiszolgálók egyikét (webkiszolgáló, adatbázis-kiszolgáló) blokkolja a vírusirtó program. Ha engedélyezve van egy vírusirtó eszköz, ellenőrizze a következő beállításokat a XAMPP teljesítményproblémák nélküli futtatásához:</p>
      <p>
        <ul>
          <li>Kivételek hozzáadása a tűzfalhoz: az Apache, a MySQL vagy bármely más kiszolgálóhoz.</li>
          <li>Fájlok vizsgálata végrehajtáskor: ha engedélyezve van a vírusirtó vizsgálata minden fájlra, a kiszolgálók végrehajtható fájljait lelassíthatja.</li>
          <li>A forgalom vizsgálata különböző URL-eknél: ha a saját gépén fejleszt a XAMPP programmal, zárja ki a „localhost” forgalmát a vírusirtó beállításaiban.</li>
        </ul>
      </p>
      </dd>

      <dt>Miért nem indul az Apache kiszolgáló a rendszeremen?</dt>
      <dd>
      <p>Ennek a problémának számos oka lehet:</p>
      <p>
        <ul>
          <li>Egynél több HTTP kiszolgálót indított el (IIS, Sambar, ZEUS, stb.). Csak egyetlen kiszolgáló használhatja a 80-as portot. Ez a hibaüzenet jelzi a problémát:<br/>
<code>(OS 10048)... make_sock: could not bind to adress 0.0.0.0:80
no listening sockets available, shutting down</code></li>
          <li>Más szoftverei is vannak, mint például a „Skype” internettelefon, amely szintén blokkolja a 80-as portot. Ha a „Skype” a probléma, menjen a Skype programban a Műveletek --> Beállítások --> Kapcsolat menüpontra, és távolítsa el a pipát a „80-as port használata tartalék portként” lehetőségből, majd indítsa újra a Skype programot. Ezután működnie kellene.</li>
          <li>Tűzfala van, amely blokkolja az Apache portját. Nem minden tűzfal kompatibilis az Apache kiszolgálóval, és néha a tűzfal kikapcsolása nem elegendő, el kell távolítania azt. Ez a hibaüzenet jelzi a tűzfalat:<br/>
<code>(OS 10038)Socket operation on non-socket: make_sock: for address 0.0.0.0:80,
apr_socket_opt_set: (SO_KEEPALIVE)</code></li>
        </ul>
        <p>Ha az Apache el tud ugyan indulni, de a böngésző nem tud csatlakozni hozzá, akkor ez a következők egyike miatt lehet:</p>
        <ul>
          <li>Néhány vírusirtó okozhatja ezt ugyanolyan módon, ahogy a tűzfalak zavarhatják.</li>
          <li>XP Professional rendszere van 1. javítócsomag nélkül. Legalább SP1 szükséges a XAMPP programhoz.</li>
        </ul>
      </p>
      <p><strong>Tipp:</strong> Ha problémái vannak a portok használatával, megpróbálhatja a „xampp-portcheck.exe” eszközt. Talán segíthet.</p>
      </dd>

      <dt>Miért van majdnem 99%-os CPU terhelés az Apache használatakor?</dt>
      <dd>
      <p>Két forgatókönyv játszhat itt szerepet. Vagy a CPU van maximálisan kihasználva, vagy a böngésző képes ugyan csatlakozni a kiszolgálóhoz, de nem lát semmit (a rendszer sikertelenül próbálja betölteni az oldalt). Mindkét esetben a következő üzenetet láthatja az Apache naplófájljában:</p>
      <p><code>Child: Encountered too many AcceptEx faults accepting client connections.
winnt_mpm: falling back to 'AcceptFilter none'.</code></p>
      <p>Az MPM visszaáll egy biztonságosabb megvalósításra, de néhány klienskérés nem lett helyesen feldolgozva. Ezen hiba elkerülésének érdekében használja az „AcceptFilter” beállítást a „none” elfogadásszűrővel a „\\xampp\\apache\\conf\\extra\\httpd-mpm.conf” fájlban.</p>
      </dd>

      <dt>Miért nem jelennek meg a képek és a stíluslapok?</dt>
      <dd>
      <p>Néha problémák vannak a képek és a stíluslapok megjelenítésével. Különösen akkor, ha ezek a fájlok egy hálózati eszközön találhatók. Ebben az esetben engedélyezhet (vagy hozzáadhat) egyet, ha a következő sorok a „\\xampp\\apache\\conf\\httpd.conf” fájlban vannak:</p>
      <p><code>EnableSendfile off</br>
EnableMMAP off</code></p>
      <p>Ezt a hibát okozhatják sávszélesség-szabályzó programok is, mint például a NetLimiter.</p>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To configure XAMPP to use the included sendmail.exe binary for email delivery, follow these steps:</p>
        <ul>
          <li>Edit the XAMPP "php.ini" file. Within this file, find the [mail function] section and replace it with the following directives. Change the XAMPP installation path if needed.
          <code>
          sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
          </code>
          </li>
          <li>Edit the XAMPP "sendmail.ini" file. Within this file, find the [sendmail] section and replace it with the following directives:
          <code>
          smtp_server=smtp.gmail.com
          smtp_port=465
          smtp_ssl=auto
          error_logfile=error.log
          auth_username=<EMAIL>
          auth_password=your-gmail-password
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>Restart the Apache server using the XAMPP control panel.
          </li>
        </ul>
        <p>You can now use PHP's mail() function to send email from your application.</p> 
      </dd>
      
      <dt>Hogyan állíthatok be rendszergazda jelszót a MySQL-ben?</dt>
      <dd>
      <p>Configure it with the "XAMPP Shell" (command prompt). Open the shell from the XAMPP control pane and execute this command:<code>mysqladmin.exe -u root password secret</code>This sets the root password to 'secret'.</p>
      </dd>

      <dt>Használhatom a saját MySQL kiszolgálómat?</dt>
      <dd>
      <p>Igen. Egyszerűen ne indítsa el a MySQL kiszolgálót a XAMPP csomagból. Vegye figyelembe, hogy nem lehet két kiszolgálót elindítani ugyanazon a porton. Ha a „root” felhasználónak állított be jelszót, ne felejtse el szerkeszteni a „\\xampp\\phpMyAdmin\\config.inc.php” fájlt.</p>
      </dd>

      <dt>Hogyan tudom korlátozni a phpMyAdmin kívülről való hozzáférését?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>Mielőtt hozzáférne a MySQL kiszolgálóhoz, a phpMyAdmin kérni fog Öntől egy felhasználónevet és egy jelszót. Ne felejtsen el először beállítani jelszót a „root” felhasználónak.</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
      <p>To enable remote access to phpMyAdmin, follow these steps:</p>
      <ul>
        <li>Edit the apache\conf\extra\httpd-xampp.conf file in your XAMPP installation directory.</li>
        <li>Within this file, find the lines below. 
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require local
          </code></p>
        </li>
        <li>Then replace 'Require local' with 'Require all granted'.</li>
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require all granted
          </code></p>
        <li>Restart the Apache server using the XAMPP control panel.</li>
      </ul>
      </dd>
      
      <dt>Hol van az IMAP támogatás a PHP-hoz?</dt>
      <dd>
      <p>Alapértelmezetten az IMAP támogatás nincs aktiválva a PHP-hoz az XAMPP programban néhány megmagyarázhatatlan előkészítési hiba miatt egy-két otthoni verzióban, mint például a Windows 98-ban. Ha NT rendszerekkel dolgozik, megnyithatja a „\\xampp\\php\\php.ini” fájlt a PHP-kiterjesztés aktiválásához a kezdő pontosvessző eltávolításával a „;extension=php_imap.dll” sor elejéről. Ilyennek kell lennie:</br>
<code>extension=php_imap.dll</code></p>
      <p>Most indítsa újra az Apache kiszolgálót és az IMAP működni fog. Ugyanezeket a lépéseket használhatja minden olyan kiterjesztésnél, amely nincs engedélyezve az alapértelmezett beállításban.</p>
      </dd>

      <dt>Miért nem működik néhány nyílt forrású PHP alkalmazás a XAMPP programmal Windowson?</dt>
      <dd>
      <p>Számos Linuxhoz írt PHP alkalmazást vagy kiterjesztést még nem írtak át Windowsra. </p>
      </dd>

      <dt>Törölhetem az „install” könyvtárat a telepítés után?</dt>
      <dd>
      <p>Jobban tenné, ha nem. Az itt lévő parancsfájlok még szükségesek a XAMPP minden további csomagjához (kiegészítőkhöz) és frissítéséhez.</p>
      </dd>

      <dt>Hogyan aktiválhatom az eAccelerator kiterjesztést?</dt>
      <dd>
      <p>Mint a többi (Zend) kiterjesztést, a „php.ini” fájlban aktiválhatja. Ebben a fájlban engedélyezze a „;zend_extension = "\\xampp\\php\\ext\\php_eaccelerator.dll"” sort. Ilyennek kell lennie:</br>
<code>zend_extension = "\xampp\php\ext\php_eaccelerator.dll"</code></p>
      </dd>

      <dt>Hogyan tudom javítani a csatlakozási hibát az MS SQL kiszolgálómhoz?</dt>
      <dd>
      <p>Ha az mssql kiterjesztés be lett töltve a php.ini fájlban, néha probléma jelentkezik, amikor kizárólag TCP/IP van használva. Kijavíthatja ezt a hibát a Microsoft által közzétett újabb „ntwdblib.dll” fájllal. Cserélje le a régebbi fájlt a „\\xampp\\apache\\bin” és a „\\xampp\\php” könyvtárban az újra. A licenc miatt nem csomagolhatjuk a XAMPP-hoz ennek a fájlnak az újabb verzióját.</p>
      </dd>

      <dt>Hogyan dolgozhatok a PHP mcrypt kiterjesztéssel?</dt>
      <dd>
      <p>Ehhez nyitottunk egy témát a fórumban példákkal és lehetséges megoldásokkal: <a href="https://community.apachefriends.org/f/viewtopic.php?t=3012">MCrypt téma</a></p>
      </dd>

      <dt>Működik a Microsoft Active Server Pages (ASP) XAMPP-on?</dt>
      <dd>
      <p>Nem. És az Apache::ASP a Perl kiegészítővel nem ugyanaz. Az Apache::ASP csak ismeri a Perl-parancsfájlokat, de az Internet Information Server (IIS) által tartalmazott ASP szintén ismeri a normál VBScriptet. Azonban az ASP .NET-hez elérhető harmadik fél által készített Apache-modul.</p>
      </dd>

      <dt>How can I get XAMPP working on port 80 under Windows 10?</dt>
      <dd>
      <p>By default, Windows 10 starts Microsoft IIS on port 80, which is the same default port used by Apache in XAMPP. As a result, Apache cannot bind to port 80.</p>
      <p>To deactivate IIS from running on port 80, follow these steps:</p>
      <ul>
        <li>Open the Services panel in Computer Management.</li>
        <li>Search for the 'World Wide Web Publishing Service' and select it.</li>
        <li>Click the link to 'Stop the service'.</li>
        <li>Double-click the service name.</li>
        <li>In the 'Startup type' field, change the startup type to 'Disabled'.</li>
        <li>Click 'OK' to save your changes.</li>
      </ul>
      <p>You should now be able to start Apache in XAMPP on port 80.</p>
      <p>For more information, refer to the 'Troubleshoot Apache Startup Problems' guide included with XAMPP or <a href='https://community.apachefriends.org/f/viewtopic.php?f=16&t=71620'>this forum post</a>.</p>
      </dd>
      
      <dt>How can I use Microsoft Edge to access local addresses under Windows 10?</dt>
      <dd>
      <p>If your local machine has the host name 'myhost', you will not be able to access URLs such as http://myhost in Microsoft Edge. To resolve this, you should instead use the addresses http://127.0.0.1 or http://localhost.</p>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: \xampp\apache\conf\httpd.conf, \xampp\apache\conf\extra\httpd-xampp.conf</li>
          <li>PHP configuration file: \xampp\php\php.ini</li>
          <li>MySQL configuration file: \xampp\mysql\bin\my.ini</li>
          <li>FileZilla Server configuration file: \xampp\FileZillaFTP\FileZilla Server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\tomcat\conf\server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\sendmail\sendmail.ini</li>
          <li>Mercury Mail configuration file: \xampp\MercuryMail\MERCURY.INI</li>
        </ul>
      </dd>

    </dl>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright © Apache Friends, 2022.</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Adatvédelmi irányelv</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN által nyújtott
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
