// FileZilla Server - a Windows ftp server

// Copyright (C) 2002-2004 - <PERSON> <<EMAIL>>

// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License
// as published by the Free Software Foundation; either version 2
// of the License, or (at your option) any later version.

// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.

// You should have received a copy of the GNU General Public License
// along with this program; if not, write to the Free Software
// Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.

#pragma once

BOOL GetLength64(LPCTSTR filename, _int64 &size);

struct CFileStatus64
{
	FILETIME m_ctime;          // creation date/time of file
	FILETIME m_mtime;          // last modification date/time of file
	FILETIME m_atime;          // last access date/time of file
	_int64 m_size;            // logical size of file in bytes
	BYTE m_attribute;       // logical OR of CFile::Attribute enum values
	//BYTE _m_padding;        // pad the structure to a WORD
	//TCHAR m_szFullName[_MAX_PATH]; // absolute path name
};

BOOL PASCAL GetStatus64(LPCTSTR lpszFileName, CFileStatus64& rStatus);

_int64 GetPosition64(HANDLE hFile);