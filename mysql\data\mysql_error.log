2025-03-04 17:52:58 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 27704
2025-03-04 17:52:58 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-03-04 17:52:58 0 [Note] InnoDB: Uses event mutexes
2025-03-04 17:52:58 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-03-04 17:52:58 0 [Note] InnoDB: Number of pools: 1
2025-03-04 17:52:58 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-03-04 17:52:58 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-03-04 17:52:58 0 [Note] InnoDB: Completed initialization of buffer pool
2025-03-04 17:52:58 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=300288
2025-03-04 17:52:59 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-03-04 17:52:59 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-03-04 17:52:59 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-03-04 17:52:59 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-03-04 17:52:59 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-03-04 17:52:59 0 [Note] InnoDB: Waiting for purge to start
2025-03-04 17:52:59 0 [Note] InnoDB: 10.4.32 started; log sequence number 300297; transaction id 170
2025-03-04 17:52:59 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-03-04 17:52:59 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-03-04 17:52:59 0 [Note] InnoDB: Buffer pool(s) load completed at 250304 17:52:59
2025-03-04 17:52:59 0 [Note] Server socket created on IP: '::'.
2025-03-04 18:11:59 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 24136
2025-03-04 18:11:59 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-03-04 18:11:59 0 [Note] InnoDB: Uses event mutexes
2025-03-04 18:11:59 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-03-04 18:11:59 0 [Note] InnoDB: Number of pools: 1
2025-03-04 18:11:59 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-03-04 18:11:59 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-03-04 18:11:59 0 [Note] InnoDB: Completed initialization of buffer pool
2025-03-04 18:11:59 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=304584
2025-03-04 18:11:59 0 [Note] InnoDB: Starting final batch to recover 9 pages from redo log.
2025-03-04 18:12:00 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-03-04 18:12:00 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-03-04 18:12:00 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-03-04 18:12:00 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-03-04 18:12:00 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-03-04 18:12:00 0 [Note] InnoDB: Waiting for purge to start
2025-03-04 18:12:00 0 [Note] InnoDB: 10.4.32 started; log sequence number 306569; transaction id 199
2025-03-04 18:12:00 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-03-04 18:12:00 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-03-04 18:12:00 0 [Note] Server socket created on IP: '::'.
2025-03-04 18:12:22 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 6824
2025-03-04 18:12:22 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-03-04 18:12:22 0 [Note] InnoDB: Uses event mutexes
2025-03-04 18:12:22 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-03-04 18:12:22 0 [Note] InnoDB: Number of pools: 1
2025-03-04 18:12:22 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-03-04 18:12:22 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-03-04 18:12:22 0 [Note] InnoDB: Completed initialization of buffer pool
2025-03-04 18:12:22 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=306841
2025-03-04 18:12:22 0 [Note] InnoDB: Starting final batch to recover 9 pages from redo log.
2025-03-04 18:12:23 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-03-04 18:12:23 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-03-04 18:12:23 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-03-04 18:12:23 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-03-04 18:12:23 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-03-04 18:12:23 0 [Note] InnoDB: Waiting for purge to start
2025-03-04 18:12:23 0 [Note] InnoDB: 10.4.32 started; log sequence number 306895; transaction id 199
2025-03-04 18:12:23 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-03-04 18:12:23 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-03-04 18:12:23 0 [Note] Server socket created on IP: '::'.
2025-03-31 22:12:29 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 10376
2025-03-31 22:12:29 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-03-31 22:12:29 0 [Note] InnoDB: Uses event mutexes
2025-03-31 22:12:29 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-03-31 22:12:29 0 [Note] InnoDB: Number of pools: 1
2025-03-31 22:12:29 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-03-31 22:12:29 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-03-31 22:12:29 0 [Note] InnoDB: Completed initialization of buffer pool
2025-03-31 22:12:30 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=322942
2025-03-31 22:12:30 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-03-31 22:12:30 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-03-31 22:12:30 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-03-31 22:12:30 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-03-31 22:12:30 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-03-31 22:12:30 0 [Note] InnoDB: Waiting for purge to start
2025-03-31 22:12:30 0 [Note] InnoDB: 10.4.32 started; log sequence number 322951; transaction id 265
2025-03-31 22:12:30 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-03-31 22:12:30 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-03-31 22:12:30 0 [Note] InnoDB: Buffer pool(s) load completed at 250331 22:12:30
2025-03-31 22:12:30 0 [Note] Server socket created on IP: '::'.
2025-04-02 20:32:44 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 17848
2025-04-02 20:32:44 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-04-02 20:32:44 0 [Note] InnoDB: Uses event mutexes
2025-04-02 20:32:44 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-04-02 20:32:44 0 [Note] InnoDB: Number of pools: 1
2025-04-02 20:32:44 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-04-02 20:32:44 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-04-02 20:32:44 0 [Note] InnoDB: Completed initialization of buffer pool
2025-04-02 20:32:44 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=322960
2025-04-02 20:32:44 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-04-02 20:32:44 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-04-02 20:32:44 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-04-02 20:32:44 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-04-02 20:32:44 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-04-02 20:32:44 0 [Note] InnoDB: Waiting for purge to start
2025-04-02 20:32:44 0 [Note] InnoDB: 10.4.32 started; log sequence number 322969; transaction id 265
2025-04-02 20:32:44 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-04-02 20:32:44 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-04-02 20:32:44 0 [Note] InnoDB: Buffer pool(s) load completed at 250402 20:32:44
2025-04-02 20:32:44 0 [Note] Server socket created on IP: '::'.
2025-04-09 20:05:53 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 21920
2025-04-09 20:05:53 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-04-09 20:05:53 0 [Note] InnoDB: Uses event mutexes
2025-04-09 20:05:53 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-04-09 20:05:53 0 [Note] InnoDB: Number of pools: 1
2025-04-09 20:05:53 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-04-09 20:05:53 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-04-09 20:05:53 0 [Note] InnoDB: Completed initialization of buffer pool
2025-04-09 20:05:53 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=322978
2025-04-09 20:05:54 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-04-09 20:05:54 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-04-09 20:05:54 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-04-09 20:05:54 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-04-09 20:05:54 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-04-09 20:05:54 0 [Note] InnoDB: Waiting for purge to start
2025-04-09 20:05:54 0 [Note] InnoDB: 10.4.32 started; log sequence number 322987; transaction id 265
2025-04-09 20:05:54 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-04-09 20:05:54 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-04-09 20:05:54 0 [Note] InnoDB: Buffer pool(s) load completed at 250409 20:05:54
2025-04-09 20:05:54 0 [Note] Server socket created on IP: '::'.
2025-04-09 20:07:39 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 24036
2025-04-09 20:07:39 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-04-09 20:07:39 0 [Note] InnoDB: Uses event mutexes
2025-04-09 20:07:39 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-04-09 20:07:39 0 [Note] InnoDB: Number of pools: 1
2025-04-09 20:07:39 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-04-09 20:07:39 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-04-09 20:07:39 0 [Note] InnoDB: Completed initialization of buffer pool
2025-04-09 20:07:39 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=322978
2025-04-09 20:07:39 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-04-09 20:07:39 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-04-09 20:07:39 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-04-09 20:07:39 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-04-09 20:07:39 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-04-09 20:07:39 0 [Note] InnoDB: Waiting for purge to start
2025-04-09 20:07:39 0 [Note] InnoDB: 10.4.32 started; log sequence number 322987; transaction id 265
2025-04-09 20:07:39 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-04-09 20:07:39 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-04-09 20:07:39 0 [Note] Server socket created on IP: '::'.
2025-04-09 20:17:20 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 23860
2025-04-09 20:17:20 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-04-09 20:17:20 0 [Note] InnoDB: Uses event mutexes
2025-04-09 20:17:20 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-04-09 20:17:20 0 [Note] InnoDB: Number of pools: 1
2025-04-09 20:17:20 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-04-09 20:17:20 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-04-09 20:17:20 0 [Note] InnoDB: Completed initialization of buffer pool
2025-04-09 20:17:20 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=326345
2025-04-09 20:17:20 0 [Note] InnoDB: Starting final batch to recover 12 pages from redo log.
2025-04-09 20:17:20 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-04-09 20:17:20 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-04-09 20:17:20 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-04-09 20:17:20 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-04-09 20:17:20 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-04-09 20:17:20 0 [Note] InnoDB: Waiting for purge to start
2025-04-09 20:17:21 0 [Note] InnoDB: 10.4.32 started; log sequence number 328411; transaction id 299
2025-04-09 20:17:21 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-04-09 20:17:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-04-09 20:17:21 0 [Note] Server socket created on IP: '::'.
2025-05-07 19:54:36 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 18652
2025-05-07 19:54:36 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-07 19:54:36 0 [Note] InnoDB: Uses event mutexes
2025-05-07 19:54:36 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-07 19:54:36 0 [Note] InnoDB: Number of pools: 1
2025-05-07 19:54:36 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-07 19:54:36 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-07 19:54:36 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-07 19:54:36 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=643678
2025-05-07 19:54:37 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-07 19:54:37 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-07 19:54:37 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-07 19:54:37 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-07 19:54:37 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-07 19:54:37 0 [Note] InnoDB: Waiting for purge to start
2025-05-07 19:54:37 0 [Note] InnoDB: 10.4.32 started; log sequence number 643687; transaction id 604
2025-05-07 19:54:37 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-07 19:54:37 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-07 19:54:37 0 [Note] InnoDB: Buffer pool(s) load completed at 250507 19:54:37
2025-05-07 19:54:37 0 [Note] Server socket created on IP: '::'.
2025-05-14 18:04:38 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 20752
2025-05-14 18:04:38 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-14 18:04:38 0 [Note] InnoDB: Uses event mutexes
2025-05-14 18:04:38 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-14 18:04:38 0 [Note] InnoDB: Number of pools: 1
2025-05-14 18:04:38 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-14 18:04:38 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-14 18:04:38 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-14 18:04:38 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=644953
2025-05-14 18:04:38 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-14 18:04:38 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-14 18:04:38 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-14 18:04:38 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-14 18:04:38 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-14 18:04:38 0 [Note] InnoDB: Waiting for purge to start
2025-05-14 18:04:38 0 [Note] InnoDB: 10.4.32 started; log sequence number 644962; transaction id 630
2025-05-14 18:04:38 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-14 18:04:38 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-14 18:04:39 0 [Note] InnoDB: Buffer pool(s) load completed at 250514 18:04:39
2025-05-14 18:04:39 0 [Note] Server socket created on IP: '::'.
2025-05-18 20:32:52 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 5772
2025-05-18 20:32:52 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-18 20:32:52 0 [Note] InnoDB: Uses event mutexes
2025-05-18 20:32:52 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-18 20:32:52 0 [Note] InnoDB: Number of pools: 1
2025-05-18 20:32:52 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-18 20:32:52 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-18 20:32:52 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-18 20:32:52 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=1003796
2025-05-18 20:32:53 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-18 20:32:53 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-18 20:32:53 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-18 20:32:53 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-18 20:32:53 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-18 20:32:53 0 [Note] InnoDB: Waiting for purge to start
2025-05-18 20:32:53 0 [Note] InnoDB: 10.4.32 started; log sequence number 1003805; transaction id 1075
2025-05-18 20:32:53 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-18 20:32:53 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-18 20:32:53 0 [Note] InnoDB: Buffer pool(s) load completed at 250518 20:32:53
2025-05-18 20:32:53 0 [Note] Server socket created on IP: '::'.
2025-05-21 19:15:09 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 5756
2025-05-21 19:15:09 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-21 19:15:09 0 [Note] InnoDB: Uses event mutexes
2025-05-21 19:15:09 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-21 19:15:09 0 [Note] InnoDB: Number of pools: 1
2025-05-21 19:15:09 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-21 19:15:09 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-21 19:15:09 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-21 19:15:09 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=1409739
2025-05-21 19:15:10 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-21 19:15:10 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-21 19:15:10 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-21 19:15:10 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-21 19:15:10 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-21 19:15:10 0 [Note] InnoDB: Waiting for purge to start
2025-05-21 19:15:10 0 [Note] InnoDB: 10.4.32 started; log sequence number 1409748; transaction id 1460
2025-05-21 19:15:10 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-21 19:15:10 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-21 19:15:10 0 [Note] InnoDB: Buffer pool(s) load completed at 250521 19:15:10
2025-05-21 19:15:10 0 [Note] Server socket created on IP: '::'.
2025-05-28 20:25:22 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 22576
2025-05-28 20:25:22 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-28 20:25:22 0 [Note] InnoDB: Uses event mutexes
2025-05-28 20:25:22 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-28 20:25:22 0 [Note] InnoDB: Number of pools: 1
2025-05-28 20:25:22 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-28 20:25:22 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-28 20:25:22 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-28 20:25:22 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=2860579
2025-05-28 20:25:23 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-28 20:25:23 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-28 20:25:23 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-28 20:25:23 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-28 20:25:23 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-28 20:25:23 0 [Note] InnoDB: Waiting for purge to start
2025-05-28 20:25:23 0 [Note] InnoDB: 10.4.32 started; log sequence number 2860588; transaction id 3018
2025-05-28 20:25:23 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-28 20:25:23 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-28 20:25:23 0 [Note] InnoDB: Buffer pool(s) load completed at 250528 20:25:23
2025-05-28 20:25:23 0 [Note] Server socket created on IP: '::'.
2025-05-29 18:40:46 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 27908
2025-05-29 18:40:46 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-29 18:40:46 0 [Note] InnoDB: Uses event mutexes
2025-05-29 18:40:46 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-29 18:40:46 0 [Note] InnoDB: Number of pools: 1
2025-05-29 18:40:46 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-29 18:40:46 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-29 18:40:46 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-29 18:40:46 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3259372
2025-05-29 18:40:47 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-29 18:40:47 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-29 18:40:47 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-29 18:40:47 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-29 18:40:47 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-29 18:40:47 0 [Note] InnoDB: Waiting for purge to start
2025-05-29 18:40:47 0 [Note] InnoDB: 10.4.32 started; log sequence number 3259381; transaction id 3502
2025-05-29 18:40:47 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-29 18:40:47 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-29 18:40:47 0 [Note] InnoDB: Buffer pool(s) load completed at 250529 18:40:47
2025-05-29 18:40:47 0 [Note] Server socket created on IP: '::'.
2025-05-30 17:58:48 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 11180
2025-05-30 17:58:49 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 17:58:49 0 [Note] InnoDB: Uses event mutexes
2025-05-30 17:58:49 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 17:58:49 0 [Note] InnoDB: Number of pools: 1
2025-05-30 17:58:49 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 17:58:49 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 17:58:49 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 17:58:49 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320768
2025-05-30 17:58:49 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-30 17:58:49 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-30 17:58:49 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-30 17:58:49 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-30 17:58:49 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-30 17:58:49 0 [Note] InnoDB: Waiting for purge to start
2025-05-30 17:58:49 0 [Note] InnoDB: 10.4.32 started; log sequence number 3320777; transaction id 3695
2025-05-30 17:58:49 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-30 17:58:49 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-30 17:58:49 0 [Note] InnoDB: Buffer pool(s) load completed at 250530 17:58:49
2025-05-30 17:58:49 0 [Note] Server socket created on IP: '::'.
2025-05-30 18:00:08 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 15892
2025-05-30 18:00:08 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:00:08 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:00:08 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:00:08 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:00:08 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:00:08 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:00:08 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:00:08 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:00:08 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:00:08 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:00:08 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:00:27 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 22408
2025-05-30 18:00:27 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:00:27 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:00:27 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:00:27 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:00:27 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:00:27 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:00:27 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:00:27 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:00:27 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:00:27 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:00:27 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:00:39 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 33380
2025-05-30 18:00:39 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:00:39 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:00:39 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:00:39 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:00:39 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:00:39 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:00:39 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:00:39 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:00:39 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:00:39 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:00:39 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:01:51 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 22480
2025-05-30 18:01:51 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:01:51 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:01:51 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:01:51 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:01:51 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:01:51 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:01:51 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:01:51 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:01:51 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:01:51 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:01:51 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:02:28 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 28156
2025-05-30 18:02:28 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:02:28 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:02:28 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:02:28 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:02:28 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:02:28 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:02:28 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:02:28 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:02:28 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:02:28 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:02:28 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:04:09 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 19400
2025-05-30 18:04:09 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:04:09 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:04:09 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:04:09 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:04:09 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:04:09 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:04:09 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:04:09 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:04:09 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:04:09 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:04:09 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:07:44 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 8256
2025-05-30 18:07:44 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:07:44 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:07:44 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:07:44 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:07:44 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:07:44 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:07:44 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:07:44 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:07:44 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:07:44 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:07:44 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:28:38 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 17480
2025-05-30 18:28:38 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:28:38 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:28:38 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:28:38 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:28:38 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:28:38 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:28:38 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:28:38 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:28:38 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:28:38 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:28:38 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 18:55:53 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 10460
2025-05-30 18:55:53 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 18:55:53 0 [Note] InnoDB: Uses event mutexes
2025-05-30 18:55:53 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 18:55:53 0 [Note] InnoDB: Number of pools: 1
2025-05-30 18:55:53 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 18:55:53 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 18:55:53 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 18:55:53 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=3320786
2025-05-30 18:55:53 0 [Note] InnoDB: Starting final batch to recover 3 pages from redo log.
2025-05-30 18:55:53 0 [ERROR] [FATAL] InnoDB: Trying to read page number 3721 in space 0, space name innodb_system, which is outside the tablespace bounds. Byte offset 0, len 16384Please check that the configuration matches the InnoDB system tablespace location (ibdata files)
250530 18:55:53 [ERROR] mysqld got exception 0x80000003 ;
Sorry, we probably made a mistake, and this is a bug.

Your assistance in bug reporting will enable us to fix this for the next release.
To report this bug, see https://mariadb.com/kb/en/reporting-bugs

We will try our best to scrape up some info that will hopefully help
diagnose the problem, but since we have already crashed, 
something is definitely wrong and this may fail.

Server version: 10.4.32-MariaDB source revision: c4143f909528e3fab0677a28631d10389354c491
key_buffer_size=16777216
read_buffer_size=262144
max_used_connections=0
max_threads=65537
thread_count=0
It is possible that mysqld could use up to 
key_buffer_size + (read_buffer_size + sort_buffer_size)*max_threads = 20304 K  bytes of memory
Hope that's ok; if not, decrease some variables in the equation.

Thread pointer: 0x0
Attempting backtrace. You can use the following information to find out
where mysqld died. If you see no messages after this, something went
terribly wrong...
mysqld.exe!my_parameter_handler()
ucrtbase.dll!raise()
ucrtbase.dll!abort()
mysqld.exe!??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!?_Forced_rehash@?$_Hash@V?$_Umap_traits@PEAU_iobuf@@PEAXV?$_Uhash_compare@PEAU_iobuf@@U?$hash@PEAU_iobuf@@@std@@U?$equal_to@PEAU_iobuf@@@3@@std@@V?$allocator@U?$pair@QEAU_iobuf@@PEAX@std@@@3@$0A@@std@@@std@@IEAAX_K@Z()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!pthread_dummy()
mysqld.exe!??$_Reallocate_grow_by@V<lambda_1>@?1??append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV34@QEBD_K@Z@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_1>@?1??append@01@QEAAAEAV01@QEBD0@Z@PEBD_K@Z()
mysqld.exe!?ha_initialize_handlerton@@YAHPEAUst_plugin_int@@@Z()
mysqld.exe!?plugin_dl_foreach@@YA_NPEAVTHD@@PEBUst_mysql_const_lex_string@@P6AD0PEAUst_plugin_int@@PEAX@Z3@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?plugin_init@@YAHPEAHPEAPEADH@Z()
mysqld.exe!?init_net_server_extension@@YAXPEAVTHD@@@Z()
mysqld.exe!?win_main@@YAHHPEAPEAD@Z()
mysqld.exe!?mysql_service@@YAXPEAX@Z()
mysqld.exe!?mysqld_main@@YAHHPEAPEAD@Z()
mysqld.exe!strxnmov()
KERNEL32.DLL!BaseThreadInitThunk()
ntdll.dll!RtlUserThreadStart()
The manual page at https://mariadb.com/kb/en/how-to-produce-a-full-stack-trace-for-mariadbd/ contains
information that should help you find out what is causing the crash.
Writing a core file at C:\xampp\mysql\data
Minidump written to C:\xampp\mysql\data\mysqld.dmp
2025-05-30 20:33:58 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 27376
2025-05-30 20:33:58 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-05-30 20:33:58 0 [Note] InnoDB: Uses event mutexes
2025-05-30 20:33:58 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-05-30 20:33:58 0 [Note] InnoDB: Number of pools: 1
2025-05-30 20:33:58 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-05-30 20:33:58 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-05-30 20:33:58 0 [Note] InnoDB: Completed initialization of buffer pool
2025-05-30 20:33:58 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=92220
2025-05-30 20:33:58 0 [Note] InnoDB: Resizing redo log from 2*50331648 to 2*5242880 bytes; LSN=92238
2025-05-30 20:33:58 0 [Note] InnoDB: Starting to delete and rewrite log files.
2025-05-30 20:33:58 0 [Note] InnoDB: Setting log file C:\xampp\mysql\data\ib_logfile101 size to 5242880 bytes
2025-05-30 20:33:58 0 [Note] InnoDB: Setting log file C:\xampp\mysql\data\ib_logfile1 size to 5242880 bytes
2025-05-30 20:33:58 0 [Note] InnoDB: Renaming log file C:\xampp\mysql\data\ib_logfile101 to C:\xampp\mysql\data\ib_logfile0
2025-05-30 20:33:58 0 [Note] InnoDB: New log files created, LSN=92238
2025-05-30 20:33:58 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-05-30 20:33:58 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-05-30 20:33:58 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-05-30 20:33:58 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-05-30 20:33:58 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-05-30 20:33:58 0 [Note] InnoDB: Waiting for purge to start
2025-05-30 20:33:58 0 [Note] InnoDB: 10.4.32 started; log sequence number 92238; transaction id 72
2025-05-30 20:33:58 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-05-30 20:33:58 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-05-30 20:33:58 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-05-30 20:33:58 0 [Note] Server socket created on IP: '::'.
2025-06-01 12:40:33 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 20412
2025-06-01 12:40:33 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-01 12:40:33 0 [Note] InnoDB: Uses event mutexes
2025-06-01 12:40:33 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-01 12:40:33 0 [Note] InnoDB: Number of pools: 1
2025-06-01 12:40:33 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-01 12:40:33 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-01 12:40:33 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-01 12:40:33 0 [Note] InnoDB: The log sequence number 92238 in the system tablespace does not match the log sequence number 92684 in the ib_logfiles!
2025-06-01 12:40:33 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-01 12:40:33 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-01 12:40:33 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-01 12:40:33 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-01 12:40:33 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-01 12:40:33 0 [Note] InnoDB: Waiting for purge to start
2025-06-01 12:40:33 0 [Note] InnoDB: 10.4.32 started; log sequence number 92684; transaction id 72
2025-06-01 12:40:33 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-01 12:40:33 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-01 12:40:33 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-06-01 12:40:33 0 [Note] Server socket created on IP: '::'.
2025-06-02 18:04:32 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 5960
2025-06-02 18:04:32 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-02 18:04:32 0 [Note] InnoDB: Uses event mutexes
2025-06-02 18:04:32 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-02 18:04:32 0 [Note] InnoDB: Number of pools: 1
2025-06-02 18:04:32 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-02 18:04:32 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-02 18:04:32 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-02 18:04:32 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=95265
2025-06-02 18:04:33 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-02 18:04:33 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-02 18:04:33 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-02 18:04:33 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-02 18:04:33 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-02 18:04:33 0 [Note] InnoDB: Waiting for purge to start
2025-06-02 18:04:33 0 [Note] InnoDB: 10.4.32 started; log sequence number 95274; transaction id 79
2025-06-02 18:04:33 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-02 18:04:33 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-02 18:04:33 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-06-02 18:04:33 0 [Note] Server socket created on IP: '::'.
2025-06-03 22:28:33 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 24136
2025-06-03 22:28:33 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-03 22:28:33 0 [Note] InnoDB: Uses event mutexes
2025-06-03 22:28:33 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-03 22:28:33 0 [Note] InnoDB: Number of pools: 1
2025-06-03 22:28:33 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-03 22:28:33 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-03 22:28:33 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-03 22:28:33 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=95283
2025-06-03 22:28:34 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-03 22:28:34 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-03 22:28:34 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-03 22:28:34 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-03 22:28:34 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-03 22:28:34 0 [Note] InnoDB: Waiting for purge to start
2025-06-03 22:28:34 0 [Note] InnoDB: 10.4.32 started; log sequence number 95292; transaction id 79
2025-06-03 22:28:34 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-03 22:28:34 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-03 22:28:34 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-06-03 22:28:34 0 [Note] Server socket created on IP: '::'.
2025-06-04 17:42:02 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 22284
2025-06-04 17:42:02 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-04 17:42:02 0 [Note] InnoDB: Uses event mutexes
2025-06-04 17:42:02 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-04 17:42:02 0 [Note] InnoDB: Number of pools: 1
2025-06-04 17:42:02 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-04 17:42:02 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-04 17:42:02 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-04 17:42:02 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=95301
2025-06-04 17:42:02 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-04 17:42:02 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-04 17:42:02 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-04 17:42:02 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-04 17:42:02 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-04 17:42:02 0 [Note] InnoDB: Waiting for purge to start
2025-06-04 17:42:02 0 [Note] InnoDB: 10.4.32 started; log sequence number 95310; transaction id 79
2025-06-04 17:42:02 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-04 17:42:02 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-04 17:42:02 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-06-04 17:42:02 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:40:21 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 3084
2025-06-05 21:40:21 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:40:21 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:40:21 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:40:21 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:40:21 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:40:21 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:40:21 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:40:21 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=417900
2025-06-05 21:40:21 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:40:21 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-05 21:40:21 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:40:21 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:40:21 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:40:21 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:40:21 0 [Note] InnoDB: 10.4.32 started; log sequence number 417909; transaction id 492
2025-06-05 21:40:21 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:40:21 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:40:21 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-06-05 21:40:21 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:46:02 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 16756
2025-06-05 21:46:02 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:46:02 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:46:02 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:46:02 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:46:02 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:46:02 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:46:02 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:46:02 0 [Note] InnoDB: Starting crash recovery from checkpoint LSN=417900
2025-06-05 21:46:03 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:46:03 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-05 21:46:03 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:46:03 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:46:03 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:46:03 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:46:03 0 [Note] InnoDB: 10.4.32 started; log sequence number 417909; transaction id 492
2025-06-05 21:46:03 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:46:03 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:46:03 0 [Note] InnoDB: Cannot open 'C:\xampp\mysql\data\ib_buffer_pool' for reading: No such file or directory
2025-06-05 21:46:03 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:46:03 0 [ERROR] Fatal error: Can't open and lock privilege tables: Incorrect file format 'proxies_priv'
2025-06-05 21:46:03 0 [ERROR] Aborting
2025-06-05 21:46:04 0 [ERROR] mysqld.exe: Error writing file 'C:\xampp\mysql\data\aria_log_control' (Errcode: 9 "Bad file descriptor")
2025-06-05 21:46:04 0 [ERROR] mysqld.exe: Aria engine: checkpoint failed
2025-06-05 21:46:04 0 [ERROR] mysqld.exe: Error writing file 'C:\xampp\mysql\data\aria_log_control' (Errcode: 9 "Bad file descriptor")
2025-06-05 21:46:04 0 [ERROR] mysqld.exe: Error on close of 'C:\xampp\mysql\data\aria_log_control' (Errcode: 9 "Bad file descriptor")
2025-06-05 21:53:53 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 3348
2025-06-05 21:53:53 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:53:53 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:53:53 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:53:53 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:53:53 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:53:53 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:53:53 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:53:53 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:53:53 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:53:53 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:53:53 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:53:53 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:53:53 0 [Note] InnoDB: 10.4.32 started; log sequence number 417918; transaction id 492
2025-06-05 21:53:53 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:53:53 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:53:53 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 21:53:53
2025-06-05 21:53:53 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:54:07 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 26732
2025-06-05 21:54:07 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:54:07 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:54:07 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:54:07 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:54:07 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:54:07 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:54:07 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:54:07 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:54:07 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:54:07 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:54:07 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:54:07 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:54:07 0 [Note] InnoDB: 10.4.32 started; log sequence number 417927; transaction id 492
2025-06-05 21:54:07 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:54:07 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:54:07 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 21:54:07
2025-06-05 21:54:07 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:56:02 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 18156
2025-06-05 21:56:02 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:56:02 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:56:02 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:56:02 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:56:02 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:56:02 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:56:02 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:56:02 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:56:02 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:56:02 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:56:02 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:56:02 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:56:02 0 [Note] InnoDB: 10.4.32 started; log sequence number 417936; transaction id 492
2025-06-05 21:56:02 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:56:02 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:56:02 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 21:56:02
2025-06-05 21:56:02 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:56:09 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 26304
2025-06-05 21:56:09 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:56:09 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:56:09 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:56:09 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:56:09 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:56:09 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:56:09 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:56:09 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:56:09 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:56:09 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:56:09 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:56:09 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:56:09 0 [Note] InnoDB: 10.4.32 started; log sequence number 417945; transaction id 492
2025-06-05 21:56:09 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:56:09 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:56:09 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 21:56:09
2025-06-05 21:56:09 0 [Note] Server socket created on IP: '::'.
2025-06-05 21:56:14 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 9972
2025-06-05 21:56:14 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 21:56:14 0 [Note] InnoDB: Uses event mutexes
2025-06-05 21:56:14 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 21:56:14 0 [Note] InnoDB: Number of pools: 1
2025-06-05 21:56:14 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 21:56:14 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 21:56:14 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 21:56:14 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 21:56:14 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 21:56:14 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 21:56:14 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 21:56:14 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 21:56:14 0 [Note] InnoDB: 10.4.32 started; log sequence number 417954; transaction id 492
2025-06-05 21:56:14 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 21:56:14 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 21:56:14 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 21:56:14
2025-06-05 21:56:14 0 [Note] Server socket created on IP: '::'.
2025-06-05 22:01:26 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 22092
2025-06-05 22:01:26 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 22:01:26 0 [Note] InnoDB: Uses event mutexes
2025-06-05 22:01:26 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 22:01:26 0 [Note] InnoDB: Number of pools: 1
2025-06-05 22:01:26 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 22:01:26 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 22:01:26 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 22:01:26 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 22:01:26 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 22:01:26 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 22:01:26 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 22:01:26 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 22:01:26 0 [Note] InnoDB: 10.4.32 started; log sequence number 417963; transaction id 492
2025-06-05 22:01:26 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 22:01:26 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 22:01:26 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 22:01:26
2025-06-05 22:01:26 0 [Note] Server socket created on IP: '::'.
2025-06-05 22:15:29 0 [Note] Starting MariaDB 10.4.32-MariaDB source revision c4143f909528e3fab0677a28631d10389354c491 as process 25196
2025-06-05 22:15:30 0 [Note] InnoDB: Mutexes and rw_locks use Windows interlocked functions
2025-06-05 22:15:30 0 [Note] InnoDB: Uses event mutexes
2025-06-05 22:15:30 0 [Note] InnoDB: Compressed tables use zlib 1.3
2025-06-05 22:15:30 0 [Note] InnoDB: Number of pools: 1
2025-06-05 22:15:30 0 [Note] InnoDB: Using SSE2 crc32 instructions
2025-06-05 22:15:30 0 [Note] InnoDB: Initializing buffer pool, total size = 16M, instances = 1, chunk size = 16M
2025-06-05 22:15:30 0 [Note] InnoDB: Completed initialization of buffer pool
2025-06-05 22:15:30 0 [Note] InnoDB: 128 out of 128 rollback segments are active.
2025-06-05 22:15:30 0 [Note] InnoDB: Removed temporary tablespace data file: "ibtmp1"
2025-06-05 22:15:30 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-06-05 22:15:30 0 [Note] InnoDB: Setting file 'C:\xampp\mysql\data\ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-06-05 22:15:30 0 [Note] InnoDB: File 'C:\xampp\mysql\data\ibtmp1' size is now 12 MB.
2025-06-05 22:15:30 0 [Note] InnoDB: Waiting for purge to start
2025-06-05 22:15:30 0 [Note] InnoDB: 10.4.32 started; log sequence number 417963; transaction id 492
2025-06-05 22:15:30 0 [Note] InnoDB: Loading buffer pool(s) from C:\xampp\mysql\data\ib_buffer_pool
2025-06-05 22:15:30 0 [Note] Plugin 'FEEDBACK' is disabled.
2025-06-05 22:15:30 0 [Note] InnoDB: Buffer pool(s) load completed at 250605 22:15:30
2025-06-05 22:15:30 0 [Note] Server socket created on IP: '::'.
