<!doctype html>
<html lang="zh_cn">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Windows</title>

    <meta name="description" content="Instructions on how to install XAMPP for Windows distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="zh_cn zh_cn_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/zh_cn/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/zh_cn/faq.html">常见问题</a></li>
              <li class="item "><a href="/dashboard/zh_cn/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Windows <span>经常提到的问题</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
      <dt>如何安装XAMPP？</dt>
      <dd>
      <p>XAMPP在Windows平台有三个不同版本：</p>
      <p>安装程序：<br />
      大概是安装XAMPP最容易的方法了。</p>
      <p>ZIP:<br />
      为纯净软件爱好者提供的：XAMPP的标准zip压缩包。</p>
      <p>7zip:<br />
      为网速慢的纯净软件爱好者提供的：XAMPP的7zip压缩包。</p>
      <p>注意：如果你解压文件的话，可能会被杀软误报毒。</p>
      <p><strong>使用安装程序：</strong></p>
      <p></p>
      <p>XAMPP控制面板可以用于启动/停止Apache, MySQL, FileZilla和Mercury以及将它们安装为服务。</p>
      <p><strong>通过ZIP安装</strong></p>
      <p>解压ZIP压缩文件到您选择的文件夹中。 XAMPP被提取到子目录"C:\\xampp"选定的目标目录下。然后启动文件“setup_xampp.bat”进行配置以便XAMPP的配置调整到系统中。</p>
      <p>如果你选择根目录"C:\\"作为安装目标，你绝对不要启动"setup_xampp.bat"。</p>
      <p>就像安装版一样，你现在可以使用XAMPP控制面板来完成更多工作。</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
      <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>我如何免安装启动XAMPP？</dt>
      <dd>
      <p>如果你将XAMPP解压到一个根目录，比如"C:\\"或者是"D:\\"，你就可以直接启动大多数服务器例如Apache或者MySQL而无需执行文件"setup_xampp.bat"。</p>
      <p>如果你想在一个U盘上面安装XAMPP的话，不使用安装脚本，或者在安装脚本里面选择相对路径应该会是更好的做法。因为在不同的PC上面U盘的盘符应该都是不一样的。你可以通过安装脚本在任何时候从绝对路径切换到相对路径。</p>
      <p>下载页面上的安装包版本应该是安装XAMPP的最简单的方式了。安装过程结束之后，你会在开始菜单-程序-XAMPP下找到XAMPP。你可以使用XAMPP的控制面板来启动/停止任意一个服务器或者是安装/卸载服务。</p>
      <p>XAMPP控制面板可以用于启动/停止Apache, MySQL, FileZilla和Mercury以及将它们安装为服务。</p>
      </dd>
      <dt>我如何启动和停止XAMPP？</dt>
      <dd>
      <p>通用的控制中心位于XAMPP控制面板(感谢www.nat32.com)。它开始于：</p>
      <p><code>\xampp\xampp-control.exe</code></p>
      <p>您也可以使用一些批处理文件来启动/停止服务器：</p>
      <p>
      <ul>
        <li>Apache &amp; MySQL 启动:
        <code>\xampp\xampp_start.exe</code></li>
        <li>Apache &amp; MySQL 停止:
        <code>\xampp\xampp_stop.exe</code></li>
        <li>启动Apache
        <code>\xampp\apache_start.bat</code></li>
        <li>停止Apache
        <code>\xampp\apache_stop.bat</code></li>
        <li>启动MySQL：
        <code>\xampp\mysql_start.bat</code></li>
        <li>停止MySQL：
        <code>\xampp\mysql_stop.bat</code></li>
        <li>启动Mercury邮件服务器：
        <code>\xampp\mercury_start.bat</code></li>
        <li>停止Mercury邮件服务器：
        <code>\xampp\mercury_stop.bat</code></li>
        <li>启动FileZilla服务器：
        <code>\xampp\filezilla_start.bat</code></li>
        <li>停止FileZilla服务器：
        <code>\xampp\filezilla_stop.bat</code></li></ul></p>
      </p>
      </dd>
      <dt>我如何检查是否一切正常？</dt>
      <dd>
      <p>在你喜欢的浏览器中输入下面的链接：</p>
      <p><code>http://localhost/</code> 或  <code>http://127.0.0.1/</code> </p>
      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-windows-start.jpg" />
      </dd>
      <dt>我如何将一个服务器安装成为服务？</dt>
      <dd>
      <p>你可以将XAMPP中的每一个服务器安装成为Windows服务。你也可以从XAMPP控制面板里面安装它。在这种情况下必须通过管理员权限运行控制面板或脚本。</p>
      <p>安装Apache服务：\\xampp\\apache\\apache_installservice.bat</p>
      <p>卸载Apache服务：\\xampp\\apache\\apache_uninstallservice.bat </p>
      <p>安装MySQL服务: \\xampp\\mysql\\mysql_installservice.bat </p>
      <p>卸载MySQL服务: \\xampp\\mysql\\mysql_uninstallservice.bat </p>
      <p>安装/卸载FileZilla服务: \\xampp\\filezilla_setup.bat </p>
      <p>Mercury: 没有服务可供安装</p>
      </dd>
      <dt>XAMPP准备好了吗？</dt>
      <dd>
      <p>XAMPP是一个非常优秀而又实用的开发测试环境，XAMPP的配置是开放的，允许开发者任意去修改或配置适合您的开发环境。XAMPP并非商业运行环境，用于商业运作将是一个致命的环境！</p>
      <p>如下是XAMPP不安全的配置：</p>
      <ol>
        <li>MySQL数据库管理帐号 (root)没设置密码。</li>
        <li>MySQL数据库可以通过网络打开。</li>
        <li>ProFTPD uses the password "lampp" for user "daemon".</li>
        <li>Mercury和FileZilla的默认用户是已知的。</li>
      </ol>
      <p>所有的点都可能成为一个巨大的安全隐患。尤其是如果XAMPP是可以通过网络或你的局域网外的人访问的话。使用防火墙或一个（带NAT的）路由器也是有帮助的。在有路由器或者防火墙的情况下，你的电脑正常情况下是无法从外部互联网访问的。是否修复这些问题要取决于你。XAMPP安全控制台可以提供一点小的帮助。</p>
      <p>请在发布任何东西到网上之前做好XAMPP的安全。一个防火墙或者一个外部路由器只能让你达到一个比较低的安全水平。需要高一点的安全的话，你可以运行下XAMPP安全控制台并设置密码。</p>
      <p>如果你想要让你的XAMPP通过互联网访问的话，你需要访问下述网站来修复一些问题：</p>
      <p><code> http://localhost/security/</code></p>
      <p>借助安全控制台，您可以设置MySQL的“root”用户和phpMyAdmin的密码。您还可以给XAMPP的演示页启用一个认证。</p>
      <p>这个基于网络的工具不能修复任何附加的安全问题！尤其是FileZilla FTP服务器和Mercury邮件服务器，你必须自行保护它们。</p></dd>
      <dt>如何卸载XAMPP？</dt>
      <dd>
      <p>如果你使用安装包版本安装了XAMPP，请使用卸载工具卸载。卸载工具会删除掉所有的XAMPP的文件并会卸载掉已经安装的服务，包括XAMPP自身。我们强烈推荐你使用卸载程序移除XAMPP的安装。请在卸载之前备份你所有想保留的文件。</p>
      <p>如果你使用zip或者7zip版本安装了XAMPP，请关掉所有的XAMPP服务器并退出控制面板。如果你安装了服务，也需要卸载并关闭他们。现在只需要简单的把XAMPP所在文件夹一删就完事了，不会再有需要清理的任何注册表项或者环境变量啦。</p>
      </dd>
      <dt>XAMPP的“lite”版本是什么玩意？</dt>
      <dd>
      <p>XAMPP Lite（意思是“轻的”或者“轻量级的”）是一个更小的XAMPP的组件合集，推荐用来做一些只需要PHP和MySQL的快速的工作。像Mercury邮件或者是FileZilla的FTP服务器在这个版本里都是没有的。</p>
      </dd>
      <dt>我应该把我的web内容放在哪里呢？</dt>
      <dd>
      <p>所有www文档的根目录都是\\xampp\\htdocs。如果你在目录里放一个文件“test.html”，你可以通过这个URL访问它：http://localhost/test.html。</p>
      <p>至于"test.php"呢？用"http://localhost/test.php"就可以啦。一个简单的测试脚本可以这么写：</p>
      <p><code>&lt;?php<br />
        echo 'Hello world'; <br />
        ?&gt;</code></p>
      <p>想给你的web服务器设一个新的子目录？只需要在目录\\xampp\\htdocs新建个文件夹就行了（最好不要空格并且只使用英文字母和数字），并且建一个测试文件然后访问它，例如http://localhost/new/test.php。</p>
      <p><strong>更多细节</strong></p>
      <p>HTML:<br>
      可执行的： \xampp\htdocs<br>
      允许的结尾： .html .htm<br>
      => 基本的包</p>
      <p>SSI:<br>
      可执行的： \xampp\htdocs<br>
      允许的结尾： .shtml<br>
      => 基本的包</p>
      <p>CGI:<br>
      可执行的： \xampp\htdocs and \xampp\cgi-bin<br>
      允许的结尾： .cgi<br>
      => 基本的包</p>
      <p>PHP:<br>
      可执行的： \xampp\htdocs and \xampp\cgi-bin<br>
      允许的结尾： .php<br>
      => 基本的包</p>
      <p>Perl:<br>
      可执行的： \xampp\htdocs and \xampp\cgi-bin<br>
      允许的结尾： .pl<br>
      => 基本的包</p>
      <p>Apache::ASP Perl:<br>
      可执行的： \xampp\htdocs<br>
      允许的结尾： .asp<br>
      => 基本的包</p>
      <p>JSP Java:<br>
      可执行的： \xampp\tomcat\webapps\java (e.g.)<br>
      允许的结尾： .jsp<br>
      => Tomcat add-on</p>
      <p>Servlets Java:<br>
      可执行的： \xampp\tomcat\webapps\java (e.g.)<br>
      允许的结尾： .html (u.a)<br>
      => Tomcat add-on</p>
      </dd>
      <dt>我可以移动XAMPP安装文件么？</dt>
      <dd>
      <p>是的，当移动XAMPP文件目录之后，你必须执行一下"setup_xampp.bat"。在配置文件里的路径会在这一步之后被改变。</p>
      <p>如果你已经将任意一个服务器安装成了Windows服务，你必须先移除服务，移动之后你可以重新安装服务。</p>
      <p>警告：你自己的脚本的配置文件，例如你的PHP应用，是不会被调整的。但是你可以为安装工具写一个插件，借助这样一个插件，安装工具就也可以同时调整这些文件了。</p>
      </dd>
      <dt>访问WWW目录的时候指向的“默认起始页”的名称是什么呢？</dt>
      <dd>
      <p>Apache的"DirectoryIndex"（目录起始页）功能的预设文件名是“index.html”或者“index.php”。每次当你访问一个文件夹的时候（例如http://localhost/xampp/），Apache会先找一下指定名称的文件，并且找的话会默认显示那个文件而不会再显示目录的文件列表啦。</p>
      </dd>
      <dt>我可以在哪儿修改配置呢？</dt>
      <dd>
      <p>XAMPP几乎所有的设置都可以在配置文件里修改。只需要在文本编辑器里打开文件然后就可以按你的需要修改设置了。只有Filezilla和Mercury需要使用应用配置工具里自定义设置。</p>
      </dd>

      <dt>为啥XAMPP不能在Windows XP SP2下工作呢？</dt>
      <dd>
      <p>微软在service pack 2 (SP2)里面提供了一个更加高大上的会自动启动的防火墙，这个防火墙会拦截掉Apache必需的80和443端口，然后Apache就启动不了了。</p>
      <p><strong>最快的解决方案：</strong></p>
      <p>把微软那个防火墙给它关了，然后再启动一下XAMPP试试。更好的解决办法是在安全中心里面给XAMPP定义一个例外。</p>
      <p><strong>下述端口是提供给基本功能用的：</strong></p>
      <p>Apache (HTTP): Port 80<br/>
        Apache (WebDAV): Port 81</br>
        Apache (HTTPS): Port 443</br>
        MySQL: Port 3306</br>
        FileZilla (FTP): Port 21</br>
        FileZilla (Admin): Port 14147</br>
        Mercury (SMTP): Port 25</br>
        Mercury (POP3): Port 110</br>
        Mercury (IMAP): Port 143</br>
        Mercury (HTTP): Port 2224</br>
        Mercury (Finger): Port 79</br>
        Mercury (PH): Port 105</br>
        Mercury (PopPass): Port 106</br>
        Tomcat (AJP/1.3): Port 8009</br>
        Tomcat (HTTP): Port 8080</p>
      </dd>

      <dt>为什么XAMPP不能在Vista上工作？</dt>
      <dd>
      <p><strong>用户帐户控制（UAC）</strong></p>
      <p>在目录"C:\\program files"当中你是没有完整的写权限的，即便管理员也不行。或者你就只有有限的权限（例如".\\xampp\\htdocs"）了。在这种情况下你就不能编辑文件了。</br>
<strong>解决方案：</strong> 通过浏览器（右键-安全）来提高你的权限或者禁用用户帐户控制（UAC）</p>
      <p>你已经在"C:\\xampp"将Apache/MySQL安装成Windows服务但你不能通过"XAMPP控制面板"启动/停止这些服务或者不能卸载它们。</br></br>
<strong>解决方案：</strong> 从Windows使用服务管理控制台或禁用UAC。</p>
      <p><strong>禁用用户帐户控制（UAC）</strong></p>
      <p>为了禁用UAC，你需要使用程序“msconfig”。在msconfig当中找到“工具”，选择“禁用用户账户控制”然后保存你的更改。现在你必须重启一下电脑。同时你也可以再次启动UAC。</p>
      </dd>

      <dt>我如何检查md5校验值呢？</dt>
      <dd>
      <p>想要比较文件，通常使用的办法是计算校验值。md5（消息摘要算法5）就是其中的一个生成校验值的标准。</p>
      <p>使用这个md5校验值你可以检查出来你下载的XAMPP包是不是完好的。当然你需要有一个工具来生成这个校验值。Windows用户可以使用微软的一个工具：</p>
      <p><a href="http://support.microsoft.com/kb/841290">可用性和文件校验和完整性验证程序的描述</a></p>
      <p>也可以使用其他能够生成md5的程序，例如GNU md5sum。</p>
      <p>安装好这样的一个程序（例如fciv.exe）之后，你可以完成如下的步骤：</p>
      <p>
        <ul>
          <li>下载XAMPP（例如xampp-win32-1.8.2-0.exe）</li>
          <li>用于生成校验值的：</br>
            <code>fciv.exe xampp-win32-1.8.2-0.exe</code>
          </li>
          <li>然后你就可以用你的校验值和在XAMPP主页找到的校验值进行比较了。</li>
        </ul>
      </p>
      <p>如果两个校验值是一样的，就是好的。否则，要么下载文件损坏了要么是文件被修改过。</p>
      </dd>

      <dt>为啥我在php.ini里做的修改没有生效?</dt>
      <dd>
      <p>如果在"php.ini"里做的修改没有生效，很可能是因为PHP用的是另外一个。你可以通过phpinfo()来检查这一点。访问这个网址 http://localhost/xampp/phpinfo.php 并找到Loaded Configuration File。这个值会告诉你PHP实际使用的php.ini。</p>
      <p><strong>注意：</strong> 在修改php.ini之后你必须重启Apache这样Apache/PHP才能够读取设置。</p>
      </dd>

      <dt>卧槽！XAMPP里头有病毒！</dt>
      <dd>
      <p>有些杀软会误报你懂的。通常他们会把xampp-manager.exe这个文件当成病毒。误报的意思就是本来不是病毒的东西被杀软错误的看做了病毒。我们每次发布新版XAMPP的时候都会用杀软扫一遍的。我们目前使用的是 <a href="http://www.kaspersky.com/virusscanner">Kapersky Online Virus Scanner</a>. You can also use the online tool <a href="https://www.virustotal.com/">Virus Total</a> for scanning XAMPP or send us an email to security (at) apachefriends (dot) org if you find any issue.</p>
      </dd>

      <dt>我如何配置我的杀软？</dt>
      <dd>
      <p>我们已经添加好了所有运行这个打包好的web应用所需的必要组件和服务器软件，所以你会发现XAMPP会安装一大堆文件。如果你在一个开着杀软的Windows的机器上安装XAMPP应用，可能会明显的拖缓安装进程的速度，而且还可能会有服务器（如web服务器，数据库服务器）被杀软阻塞了。如果你有开着的杀软，请检查下列可以保证运行XAMPP而不造成性能问题的设置：</p>
      <p>
        <ul>
          <li>给防火墙添加例外：针对Apache，MySQL或者其他任何的服务器。</li>
          <li>执行的时候扫描文件：如果你对所有文件都启动了杀软的扫描功能，服务器的可执行文件可能会被拖慢。</li>
          <li>扫描不同网址的流量：如果你在自己的机器上用XAMPP做开发，你可以在杀软的设置里排除掉localhost的流量监测。</li>
        </ul>
      </p>
      </dd>

      <dt>为什么Apache服务器在我的系统上启动不了呢？</dt>
      <dd>
      <p>这个问题可能是由这几种原因之一造成的：</p>
      <p>
        <ul>
          <li>你开启了不止一个HTTP的服务器(IIS, Sambar, ZEUS以及别的什么)。只有一个服务器可以占用80端口。这个错误消息指出了这个问题：<br/>
<code>(OS 10048)... make_sock: could not bind to adress 0.0.0.0:80
no listening sockets available, shutting down</code></li>
          <li>你有其他的软件，例如也会阻断80端口的互联网电话软件“skype”。如果问题来源于skype，你可以进入skype找到操作 - >选项 - >连接 - >取消勾选“使用端口80作为备用端口”，然后重新启动Skype。现在应该可以正常工作了。</li>
          <li>你有一个阻断了Apache端口的防火墙。不是所有的防火墙都兼容Apache，而有的时候禁用防火墙是不够的，还得卸载掉它才行。这个错误信息是指向防火墙的：<br/>
<code>(OS 10038)Socket operation on non-socket: make_sock: for address 0.0.0.0:80,
apr_socket_opt_set: (SO_KEEPALIVE)</code></li>
        </ul>
        <p>此外，如果Apache可以启动，但您的浏览器无法连接到它可能是由于以下原因之一：</p>
        <ul>
          <li>有些病毒扫描程序可能会像防火墙一样产生干扰并导致这个问题。</li>
          <li>你的XP Professional没有安装sp1。你必须至少安装一个SP1才能用XAMPP。</li>
        </ul>
      </p>
      <p><strong>提示：</strong> If you have problems with used ports, you can try the tool "xampp-portcheck.exe". Maybe it can help.</p>
      </dd>

      <dt>为什么我的Apache占用的CPU负载接近99%呢？</dt>
      <dd>
      <p>有两种情况，要么是你的CPU确实达到上限了，要么你的浏览器能够连接到服务器但是啥也看不到（系统尝试加载页面但是失败了），不管是哪种情况你都可以在Apache的log文件里面找到下述信息：</p>
      <p><code>Child: Encountered too many AcceptEx faults accepting client connections.
winnt_mpm: falling back to 'AcceptFilter none'.</code></p>
      <p>多道处理模块（MPM）回归到一个更加安全的执行模式，从而导致一些客户端的请求没有没有正确的执行。为了避免这样的错误，可以在"\\xampp\\apache\\conf\\extra\\httpd-mpm.conf"文件当中将可接受的过滤器"AcceptFilter"置为"none"。</p>
      </dd>

      <dt>为什么图片和样式表显示不出来？</dt>
      <dd>
      <p>有时候显示图片和样式表会有问题，特别是如果这些文件位于网络驱动器上的时候。在这种情况下，您可以启用（或增加）一行，如果文件"\\xampp\\apache\\conf\\httpd.conf"中的以下行:</p>
      <p><code>EnableSendfile off</br>
EnableMMAP off</code></p>
      <p>这个问题也可能是由带宽调整软件，例如NetLimiter导致的。</p>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To configure XAMPP to use the included sendmail.exe binary for email delivery, follow these steps:</p>
        <ul>
          <li>Edit the XAMPP "php.ini" file. Within this file, find the [mail function] section and replace it with the following directives. Change the XAMPP installation path if needed.
          <code>
          sendmail_path = "\"C:\xampp\sendmail\sendmail.exe\" -t"
          </code>
          </li>
          <li>Edit the XAMPP "sendmail.ini" file. Within this file, find the [sendmail] section and replace it with the following directives:
          <code>
          smtp_server=smtp.gmail.com
          smtp_port=465
          smtp_ssl=auto
          error_logfile=error.log
          auth_username=<EMAIL>
          auth_password=your-gmail-password
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>Restart the Apache server using the XAMPP control panel.
          </li>
        </ul>
        <p>You can now use PHP's mail() function to send email from your application.</p> 
      </dd>
      
      <dt>我如何在MySQL里面设置一个root的密码？</dt>
      <dd>
      <p>Configure it with the "XAMPP Shell" (command prompt). Open the shell from the XAMPP control pane and execute this command:<code>mysqladmin.exe -u root password secret</code>This sets the root password to 'secret'.</p>
      </dd>

      <dt>我可以使用自己的MySQL服务器么？</dt>
      <dd>
      <p>可以的，只是不要启动XAMPP包里的MySQL就够了。请注意两个服务器不能在同一个端口上同时启动如果你已经设置了一个root的密码，请不要忘记修改这个文件：\\xampp\\phpMyAdmin\\config.inc.php。</p>
      </dd>

      <dt>我如何限制phpMyAdmin的外部访问呢？</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>当你能访问MySQL服务器之前，phpMyAdmin会提示你输入用户名和密码。不要忘记先给用户"root"设个密码哦。</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
      <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
      <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
      <p>To enable remote access to phpMyAdmin, follow these steps:</p>
      <ul>
        <li>Edit the apache\conf\extra\httpd-xampp.conf file in your XAMPP installation directory.</li>
        <li>Within this file, find the lines below. 
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require local
          </code></p>
        </li>
        <li>Then replace 'Require local' with 'Require all granted'.</li>
          <p><code>
              Alias /phpmyadmin "C:/xampp/phpMyAdmin/"
              &lt;Directory "C:/xampp/phpMyAdmin"&gt;
                AllowOverride AuthConfig
                Require all granted
          </code></p>
        <li>Restart the Apache server using the XAMPP control panel.</li>
      </ul>
      </dd>
      
      <dt>PHP的IMAP支持在哪儿呢？</dt>
      <dd>
      <p>默认的话，在XAMPP里面PHP对IMAP的支持是被停用的，因为对于某些家用的系统版本比如Windows 98会出现一些诡异的初始化错误。如果你用的是Windows NT系统的话，你可以打开文件"\\xampp\\php\\php.ini"并去掉";extension=php_imap.dll"这一行前面的分号来激活这个PHP扩展。它应该是这样的：</br>
<code>extension=php_imap.dll</code></p>
      <p>现在重启一下Apache然后IMAP就好使啦。你可以使用同样的步骤来操作任何一个在默认配置里没有激活的扩展。</p>
      </dd>

      <dt>为什么一些PHP开源应用不能在Windows版的XAMPP工作呢？</dt>
      <dd>
      <p>许多PHP应用或者扩展都是为linux写的，还没有被移植到Windows。 </p>
      </dd>

      <dt>Can I delete the "install" directory after installation?</dt>
      <dd>
      <p>最好不要这样。所有的附加组件和XAMPP的升级仍然是需要使用这个脚本的。</p>
      </dd>

      <dt>我如何激活eaccelerator？</dt>
      <dd>
      <p>就像很多(Zend)扩展一样，你可以在"php.ini"里激活它。在这个文件里，启用这一行";zend_extension = "\\xampp\\php\\ext\\php_eaccelerator.dll""。它应该是这样子的：</br>
<code>zend_extension = "\xampp\php\ext\php_eaccelerator.dll"</code></p>
      </dd>

      <dt>我如何修复我的MS SQL服务器的连接错误？</dt>
      <dd>
      <p>如果mssql扩展加载到了php.ini，有时候当只有TCP/IP使用的时候会出问题。你可以从微软获取一个新的“ntwdblib.dll”来修复这个问题。请用新的替换掉位于"\\xampp\\apache\\bin"和"\\xampp\\php"的旧的文件。因为许可证问题，我们不能把这个文件的更新版本打包进XAMPP。</p>
      </dd>

      <dt>我如何使用PHP mcrypt扩展？</dt>
      <dd>
      <p>为了这个，我们已经在社区里开了一个话题，关于例子和有效的解决方案：<a href="https://community.apachefriends.org/f/viewtopic.php?t=3012">MCrypt主题</a></p>
      </dd>

      <dt>Do Microsoft Active Server Pages (ASP) work with XAMPP?</dt>
      <dd>
      <p>不行。那个和带有Perl的Apache::ASP附加组件是不一样的。Apache::ASP只能运行Perl脚本，但是IIS上的ASP文件还会有正常的VBScript。不过对于ASP .NET，有一个第三方的Apache扩展模块可以使用。</p>
      </dd>

      <dt>How can I get XAMPP working on port 80 under Windows 10?</dt>
      <dd>
      <p>By default, Windows 10 starts Microsoft IIS on port 80, which is the same default port used by Apache in XAMPP. As a result, Apache cannot bind to port 80.</p>
      <p>To deactivate IIS from running on port 80, follow these steps:</p>
      <ul>
        <li>Open the Services panel in Computer Management.</li>
        <li>Search for the 'World Wide Web Publishing Service' and select it.</li>
        <li>Click the link to 'Stop the service'.</li>
        <li>Double-click the service name.</li>
        <li>In the 'Startup type' field, change the startup type to 'Disabled'.</li>
        <li>Click 'OK' to save your changes.</li>
      </ul>
      <p>You should now be able to start Apache in XAMPP on port 80.</p>
      <p>For more information, refer to the 'Troubleshoot Apache Startup Problems' guide included with XAMPP or <a href='https://community.apachefriends.org/f/viewtopic.php?f=16&t=71620'>this forum post</a>.</p>
      </dd>
      
      <dt>How can I use Microsoft Edge to access local addresses under Windows 10?</dt>
      <dd>
      <p>If your local machine has the host name 'myhost', you will not be able to access URLs such as http://myhost in Microsoft Edge. To resolve this, you should instead use the addresses http://127.0.0.1 or http://localhost.</p>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: \xampp\apache\conf\httpd.conf, \xampp\apache\conf\extra\httpd-xampp.conf</li>
          <li>PHP configuration file: \xampp\php\php.ini</li>
          <li>MySQL configuration file: \xampp\mysql\bin\my.ini</li>
          <li>FileZilla Server configuration file: \xampp\FileZillaFTP\FileZilla Server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\tomcat\conf\server.xml</li>
          <li>Apache Tomcat configuration file: \xampp\sendmail\sendmail.ini</li>
          <li>Mercury Mail configuration file: \xampp\MercuryMail\MERCURY.INI</li>
        </ul>
      </dd>

    </dl>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">版权所有(c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">博客</a></li>
            <li><a href="/privacy_policy.html">隐私政策</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN提供
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
