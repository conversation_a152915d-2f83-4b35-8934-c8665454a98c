// ---------------------------------------------------------------------------------------------------------------------------------
//                                                 _     
//                                                | |    
//  _ __   ___  _ __ ___  _ __ ___   __ _ _ __    | |__  
// | '_ \ / _ \| '_ ` _ \| '_ ` _ \ / _` | '__|   | '_ \ 
// | | | | (_) | | | | | | | | | | | (_| | |    _ | | | |
// |_| |_|\___/|_| |_| |_|_| |_| |_|\__, |_|   (_)|_| |_|
//                                   __/ |               
//                                  |___/                
//
// Memory manager & tracking software
//
// Best viewed with 8-character tabs and (at least) 132 columns
//
// ---------------------------------------------------------------------------------------------------------------------------------
//
// Restrictions & freedoms pertaining to usage and redistribution of this software:
//
//  * This software is 100% free
//  * If you use this software (in part or in whole) you must credit the author.
//  * This software may not be re-distributed (in part or in whole) in a modified
//    form without clear documentation on how to obtain a copy of the original work.
//  * You may not use this software to directly or indirectly cause harm to others.
//  * This software is provided as-is and without warrantee. Use at your own risk.
//
// For more information, visit HTTP://www.FluidStudios.com
//
// ---------------------------------------------------------------------------------------------------------------------------------
// Originally created on 12/22/2000 by Paul Nettle
//
// Copyright 2000, Fluid Studios, Inc., all rights reserved.
// ---------------------------------------------------------------------------------------------------------------------------------

#ifdef	new
#undef	new
#endif

#ifdef	delete
#undef	delete
#endif

#ifdef	malloc
#undef	malloc
#endif

#ifdef	calloc
#undef	calloc
#endif

#ifdef	realloc
#undef	realloc
#endif

#ifdef	free
#undef	free
#endif

// ---------------------------------------------------------------------------------------------------------------------------------
// nommgr.h - End of file
// ---------------------------------------------------------------------------------------------------------------------------------
