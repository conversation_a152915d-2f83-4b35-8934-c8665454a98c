//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by FileZilla server.rc
//
#define IDR_RT_MANIFEST1                1
#define IDR_MAINFRAME                   2
#define IDB_LEDS                        100
#define IDD_ABOUTBOX                    101
#define IDD_CONNECT_DIALOG              102
#define IDD_ENTERSOMETHING              103
#define IDD_GROUPS                      104
#define IDD_GROUPS_GENERAL              106
#define IDD_GROUPS_SPEEDLIMIT           107
#define IDD_NEWUSER                     108
#define IDD_OFFLINEASK                  109
#define IDD_OPTIONS_ADMININTERFACE      110
#define IDD_OPTIONS_GENERAL             112
#define IDD_OPTIONS_GENERAL_WELCOMEMESSAGE 113
#define IDD_OPTIONS_GSS                 114
#define IDD_OPTIONS_LOGGING             115
#define IDD_OPTIONS_MISC                116
#define IDD_OPTIONS_SECURITY            117
#define IDD_OPTIONS_SPEEDLIMIT          118
#define IDD_SAPREFS                     119
#define IDD_SPEEDLIMIT_RULE_DIALOG      120
#define IDD_USERS                       121
#define IDD_USERS_GENERAL               122
#define IDD_USERS_SPEEDLIMIT            123
#define IDI_EMPTY                       124
#define IDD_OPTIONS_PASV                124
#define IDI_GREEN                       125
#define IDD_OPTIONS_COMPRESSION         125
#define IDI_YELLOW                      126
#define IDD_OPTIONS_GENERAL_IPBINDINGS  126
#define IDI_RED                         127
#define IDD_USERS_SHAREDFOLDERS         127
#define IDI_UNKNOWN                     128
#define IDD_GROUPS_SHAREDFOLDERS        128
#define IDI_HOME                        129
#define IDD_OPTIONS_IPFILTER            129
#define IDP_SOCKETS_INIT_FAILED         130
#define IDD_ENTERSOMETHING_LARGE        131
#define IDR_DIRCONTEXT                  131
#define IDR_GROUPCONTEXT                132
#define IDD_USERS_IPFILTER              132
#define IDR_OUTPUTCONTEXT               133
#define IDD_GROUPS_IPFILTER             133
#define IDR_SYSTRAY_MENU                134
#define IDR_USERCONTEXT                 135
#define IDR_USERLISTTOOLBAR             135
#define IDR_USERVIEWCONTEXT             136
#define IDS_ADDGROUPDIALOG              137
#define IDS_BROWSEFORFOLDER             139
#define IDS_COPYGROUPDIALOG             141
#define IDS_COPYUSERDIALOG              142
#define IDS_ERRORMSG_ENTERSTRING        143
#define IDS_ERRORMSG_GROUPALREADYEXISTS 144
#define IDS_ERRORMSG_USERALREADYEXISTS  145
#define IDS_INPUTDIALOGTEXT_RENAME      146
#define IDS_OPTIONSPAGE_ADMININTERFACE  147
#define IDS_OPTIONSPAGE_GENERAL         148
#define IDS_OPTIONSPAGE_GENERAL_WELCOMEMESSAGE 149
#define IDS_OPTIONSPAGE_GSS             150
#define IDS_OPTIONSPAGE_LOGGING         151
#define IDS_OPTIONSPAGE_MISC            152
#define IDS_OPTIONSPAGE_SECURITY        153
#define IDS_OPTIONSPAGE_SPEEDLIMIT      154
#define IDS_OPTIONSPAGE_PASV            155
#define IDB_DONATE                      156
#define IDS_OPTIONSPAGE_COMPRESSION     156
#define IDS_OPTIONSPAGE_GENERAL_IPBINDINGS 157
#define IDB_TRANSFERINFO                157
#define IDS_OPTIONSPAGE_GENERAL_IPFILTER 158
#define IDS_OPTIONSPAGE_SSL             159
#define IDS_OPTIONSPAGE_AUTOBAN           160
#define IDI_DOWN                        161
#define IDI_UP                          162
#define IDR_SORTMENU                    163
#define IDD_DELETEGROUPINUSE            163
#define IDD_DELETEINUSEGROUP            164
#define IDD_OPTIONS_SSL                 164
#define IDD_CERTGEN                     165
#define IDI_CERT                        166
#define IDD_OPTIONS_AUTOBAN             167
#define IDC_CAPTION_BAR                 1000
#define IDS_SHAREDFOLDERS_ENTERALIASES  1000
#define IDC_CHECK_DAY1                  1001
#define IDC_CHECK_DAY2                  1002
#define IDC_CHECK_DAY3                  1003
#define IDC_CHECK_DAY4                  1004
#define IDC_CHECK_DAY5                  1005
#define IDC_CHECK_DAY6                  1006
#define IDC_CHECK_DAY7                  1007
#define IDC_CONNECTDIALOG_ADDRESS       1008
#define IDC_CONNECTDIALOG_ALWAYS        1009
#define IDC_CONNECTDIALOG_PASSWORD      1010
#define IDC_CONNECTDIALOG_PORT          1011
#define IDC_DATE_CHECK                  1012
#define IDC_DATE_VALUE                  1013
#define IDC_DIRADD                      1014
#define IDC_DIRREMOVE                   1015
#define IDC_DIRRENAME                   1016
#define IDC_DIRS                        1017
#define IDC_DIRS_CREATE                 1018
#define IDC_DIRS_DELETE                 1019
#define IDC_DIRS_LIST                   1020
#define IDC_DIRS_SUBDIRS                1021
#define IDC_DIRSETASHOME                1022
#define IDC_DLG_FRAME                   1023
#define IDC_EDIT1                       1024
#define IDC_FILES_APPEND                1025
#define IDC_OPTIONS_IPFILTER_DISALLOWED 1025
#define IDC_FILES_DELETE                1026
#define IDC_FILES_READ                  1027
#define IDC_FILES_WRITE                 1028
#define IDC_FROM_CHECK                  1029
#define IDC_FROM_TIME                   1030
#define IDC_GROUP                       1031
#define IDC_GROUPS_ADDGROUP             1032
#define IDC_GROUPS_AUTOCREATE           1033
#define IDC_GROUPS_COPYGROUP            1034
#define IDC_GROUPS_GROUPLIST            1036
#define IDC_GROUPS_IPLIMIT              1037
#define IDC_GROUPS_MAXCONNCOUNT         1038
#define IDC_GROUPS_MAXUSERBYPASS        1039
#define IDC_GROUPS_REMOVEGROUP          1040
#define IDC_GROUPS_RENAMEGROUP          1041
#define IDC_GROUPS_SPEEDLIMIT_SERVERBYPASS_DOWNLOAD 1042
#define IDC_GROUPS_SPEEDLIMIT_SERVERBYPASS_UPLOAD 1043
#define IDC_HOMEPAGE                    1044
#define IDC_INFXP                       1045
#define IDC_INFXPSTRICT                 1046
#define IDC_IPLIMIT                     1047
#define IDC_LOGINTIMEOUT                1050
#define IDC_MAIL                        1051
#define IDC_MAXCONNCOUNT                1052
#define IDC_MAXUSERBYPASS               1053
#define IDC_MAXUSERS                    1054
#define IDC_NEEDPASS                    1055
#define IDC_NEWUSER_GROUP               1056
#define IDC_NEWUSER_NAME                1057
#define IDC_OPTIONS_ADMININTERFACE_CHANGEPASS 1058
#define IDC_OPTIONS_ADMININTERFACE_IPADDRESSES 1059
#define IDC_OPTIONS_ADMININTERFACE_IPBINDINGS 1060
#define IDC_OPTIONS_ADMININTERFACE_NEWPASS 1061
#define IDC_OPTIONS_ADMININTERFACE_NEWPASS2 1062
#define IDC_OPTIONS_ADMININTERFACE_PORT 1063
#define IDC_OPTIONS_GENERAL_WELCOMEMESSAGE_WELCOMEMESSAGE 1064
#define IDC_OPTIONS_LOGGING_DELETE      1065
#define IDC_OPTIONS_LOGGING_DELETETIME  1066
#define IDC_OPTIONS_LOGGING_ENABLE      1067
#define IDC_OPTIONS_LOGGING_LIMIT       1068
#define IDC_OPTIONS_LOGGING_LIMITSIZE   1069
#define IDC_OPTIONS_LOGGING_LOGTYPE     1070
#define IDC_OPTIONS_LOGGING_LOGTYPE2    1071
#define IDC_OPTIONS_MISC_DONTSHOWPASS   1077
#define IDC_OPTIONS_MISC_STARTMINIMIZED 1078
#define IDC_OPTIONS_TRANSFERBUFFERSIZE  1079
#define IDC_OUTFXP                      1080
#define IDC_OPTIONS_TRANSFERBUFFERSIZE2 1080
#define IDC_OUTFXPSTRICT                1081
#define IDC_PAGE_TREE                   1082
#define IDC_PASS                        1083
#define IDC_PHELP                       1084
#define IDC_PORT                        1085
#define IDC_PROMPTPASSWORD              1086
#define IDC_RADIO1                      1087
#define IDC_RADIO2                      1088
#define IDC_RADIO3                      1089
#define IDC_SPEED                       1091
#define IDC_SPEEDLIMIT_DOWNLOAD1        1092
#define IDC_SPEEDLIMIT_DOWNLOAD2        1093
#define IDC_SPEEDLIMIT_DOWNLOAD3        1094
#define IDC_SPEEDLIMIT_DOWNLOAD4        1095
#define IDC_SPEEDLIMIT_DOWNLOAD_ADD     1096
#define IDC_SPEEDLIMIT_DOWNLOAD_DOWN    1097
#define IDC_SPEEDLIMIT_DOWNLOAD_REMOVE  1098
#define IDC_SPEEDLIMIT_DOWNLOAD_RULES_LIST 1099
#define IDC_SPEEDLIMIT_DOWNLOAD_UP      1100
#define IDC_SPEEDLIMIT_DOWNLOAD_VALUE   1101
#define IDC_SPEEDLIMIT_UPLOAD1          1102
#define IDC_SPEEDLIMIT_UPLOAD2          1103
#define IDC_SPEEDLIMIT_UPLOAD3          1104
#define IDC_SPEEDLIMIT_UPLOAD4          1105
#define IDC_SPEEDLIMIT_UPLOAD_ADD       1106
#define IDC_SPEEDLIMIT_UPLOAD_DOWN      1107
#define IDC_SPEEDLIMIT_UPLOAD_REMOVE    1108
#define IDC_SPEEDLIMIT_UPLOAD_RULES_LIST 1109
#define IDC_SPEEDLIMIT_UPLOAD_UP        1110
#define IDC_SPEEDLIMIT_UPLOAD_VALUE     1111
#define IDC_TEXT                        1112
#define IDC_THREADNUM                   1113
#define IDC_TIMEOUT                     1114
#define IDC_TO_CHECK                    1115
#define IDC_TO_TIME                     1116
#define IDC_TRANSFERTIMEOUT             1117
#define IDC_USEGSS                      1118
#define IDC_USERADD                     1119
#define IDC_USERCOPY                    1120
#define IDC_USERLIST                    1121
#define IDC_USERREMOVE                  1122
#define IDC_USERRENAME                  1123
#define IDC_USERS_SPEEDLIMIT_SERVERBYPASS_DOWNLOAD 1124
#define IDC_USERS_SPEEDLIMIT_SERVERBYPASS_UPLOAD 1125
#define IDC_VERSION                     1126
#define IDC_DONATE                      1127
#define IDD_OPTIONS_PASV_USEPORTRANGE   1134
#define IDC_OPTIONS_PASV_PORTMIN        1135
#define IDC_OPTIONS_PASV_PORTMAX        1136
#define IDC_OPTIONS_PASV_NOLOCAL        1137
#define IDC_OPTIONS_PASV_IPTYPE1        1138
#define IDC_OPTIONS_PASV_IPTYPE2        1139
#define IDC_OPTIONS_PASV_IPTYPE3        1140
#define IDC_OPTIONS_PASV_IP             1141
#define IDC_OPTIONS_PASV_URL            1142
#define IDC_OPTIONS_PASV_TEXT           1143
#define IDC_OPTIONS_COMPRESSION_USE     1145
#define IDC_OPTIONS_EXCLUDELOCAL        1146
#define IDC_OPTIONS_COMPRESSION_DISALLOWED_IPS 1147
#define IDC_OPTIONS_COMPRESSION_LEVELMIN 1148
#define IDC_OPTIONS_COMPRESSION_LEVELMAX 1149
#define IDC_OPTIONS_GENERAL_IPBINDUNGS_IPBINDINGS 1150
#define IDC_USERS_GENERAL_ENABLE        1151
#define IDC_GROUPS_GENERAL_ENABLE       1153
#define IDC_OPTIONS_IPFILTER_ALLOWED    1154
#define IDC_USERS_IPFILTER_DISALLOWED   1155
#define IDC_USERS_IPFILTER_ALLOWED      1156
#define IDC_GROUPS_IPFILTER_DISALLOWED  1157
#define IDC_GROUPS_IPFILTER_ALLOWED     1158
#define IDC_USERS_GENERAL_COMMENTS      1159
#define IDC_GROUPS_GENERAL_COMMENTS     1160
#define IDC_OPTIONS_WELCOMEMESSAGE_HIDE 1161
#define IDC_DESC                        1162
#define IDC_NEWGROUPCOMBO               1163
#define IDC_ENABLESSL                   1164
#define IDC_GENERATE                    1165
#define IDC_PRIVATEKEY                  1166
#define IDC_CERTIFICATE                 1167
#define IDC_PRIVATEKEY_BROWSE           1168
#define IDC_CERTIFICATE_BROWSE          1169
#define IDC_KEYPASS                     1170
#define IDC_ALLOWEXPLICIT               1171
#define IDC_SSLONLY                     1172
#define IDC_KEYSIZE1                    1176
#define IDC_KEYSIZE2                    1177
#define IDC_KEYSIZE3                    1178
#define IDC_STATE                       1179
#define IDC_COUNTRY                     1180
#define IDC_CITY                        1182
#define IDC_ORGANIZATION                1184
#define IDC_UNIT                        1185
#define IDC_EMAIL                       1186
#define IDC_CNAME                       1187
#define IDC_FILE                        1188
#define IDC_BROWSE                      1190
#define IDC_FORCEEXPLICIT               1191
#define IDC_FORCEPROTP                  1192
#define IDC_FORCESSL                    1194
#define IDC_SHAREWRITE                  1197
#define IDC_ACTIVE_IGNORELOCAL          1199
#define IDC_AUTOBAN                     1201
#define ID_ATTEMPTS                     1202
#define ID_BANMINUTES                   1203
#define ID_BANTIME                      1203
#define IDC_BANTYPE1                    1204
#define IDC_BANTYPE2                    1205
#define ID_ACTIVE                       32768
#define ID_DIRMENU_ADD                  32769
#define ID_DIRMENU_REMOVE               32770
#define ID_DIRMENU_RENAME               32771
#define ID_EDIT_SETTINGS                32773
#define ID_FILE_CONNECT                 32774
#define ID_FILE_DISCONNECT              32775
#define ID_GROUPMENU_ADD                32776
#define ID_GROUPMENU_COPY               32777
#define ID_GROUPMENU_REMOVE             32778
#define ID_GROUPMENU_RENAME             32779
#define ID_GROUPS                       32780
#define ID_INDICATOR_RECVCOUNT          32781
#define ID_INDICATOR_RECVLED            32782
#define ID_INDICATOR_RECVRATE           32783
#define ID_INDICATOR_SENDCOUNT          32784
#define ID_INDICATOR_SENDLED            32785
#define ID_INDICATOR_SENDRATE           32786
#define ID_LOCK                         32787
#define ID_MENU_EDIT_GROUPS             32788
#define ID_MENU_EDIT_USERS              32789
#define ID_OUTPUTCONTEXT_CLEARALL       32790
#define ID_OUTPUTCONTEXT_COPYTOCLIPBOARD 32791
#define ID_TRAY_EXIT                    32792
#define ID_TRAY_RESTORE                 32793
#define ID_USERMENU_ADD                 32794
#define ID_USERMENU_COPY                32795
#define ID_USERMENU_REMOVE              32796
#define ID_USERMENU_RENAME              32797
#define ID_USERS                        32798
#define ID_DIRMENU_EDITALIASES          32800
#define ID_DIRMENU_SETASHOMEDIR         32801
#define ID_USERLISTTOOLBAR_DISPLAYLOGICAL 32802
#define ID_USERLISTTOOLBAR_DISPLAYPHYSICAL 32803
#define ID_USERLISTTOOLBAR_SORT         32804
#define ID_USERVIEWCONTEXT_BAN          32805
#define ID_USERVIEWCONTEXT_KICK         32806
#define ID_DISPLAY_SORTBYUSERID         32811
#define ID_DISPLAY_SORTBYIP             32812
#define ID_DISPLAY_SORTBYACCOUNT        32813

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_3D_CONTROLS                     1
#define _APS_NEXT_RESOURCE_VALUE        168
#define _APS_NEXT_COMMAND_VALUE         32807
#define _APS_NEXT_CONTROL_VALUE         1206
#define _APS_NEXT_SYMED_VALUE           1228
#endif
#endif
