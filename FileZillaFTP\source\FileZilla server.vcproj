<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="Service"
	ProjectGUID="{45702DF1-B087-4692-9E2A-2F65133A7539}"
	RootNamespace="Service"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Compat Debug|Win32"
			OutputDirectory=".\Compat_Debug"
			IntermediateDirectory=".\Compat_Debug"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			ExcludeBuckets="5;6;8;18"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Compat_Debug/FileZilla server.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/w44996"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(ProjectDir)includes&quot;;&quot;C:\dev\libraries\zlib-1.2.3&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;SERVICE;_SCL_SECURE_NO_WARNINGS"
				MinimalRebuild="false"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="false"
				EnableFunctionLevelLinking="false"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Compat_Debug/FileZilla server.pch"
				AssemblerListingLocation=".\Compat_Debug/"
				ObjectFile=".\Compat_Debug/"
				ProgramDataBaseFileName=".\Compat_Debug/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1031"
			/>
			<Tool
				Name="VCPreLinkEventTool"
				Description="Terminate service..."
				CommandLine="if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /stop&#x0D;&#x0A;if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /compat /stop&#x0D;&#x0A;if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /uninstall&#x0D;&#x0A;"
				ExcludedFromBuild="true"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="zlibd.lib version.lib ws2_32.lib"
				OutputFile=".\Compat_Debug/FileZilla server.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				AdditionalLibraryDirectories=".\Compat_Debug"
				IgnoreDefaultLibraryNames=""
				GenerateDebugInformation="true"
				ProgramDatabaseFile=".\Compat_Debug/FileZilla server.pdb"
				GenerateMapFile="true"
				MapFileName=".\Compat_Debug/FileZilla server.map"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
				CommandLine="&quot;$(OutDir)\FileZilla Server.exe&quot; /stop&#x0D;&#x0A;&quot;$(OutDir)\FileZilla Server.exe&quot; /uninstall&#x0D;&#x0A;"
				ExcludedFromBuild="true"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory=".\Release"
			IntermediateDirectory=".\Release"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			ExcludeBuckets="5;6;8;18"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/FileZilla server.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				WholeProgramOptimization="true"
				AdditionalIncludeDirectories="&quot;$(ProjectDir)includes&quot;;&quot;C:\dev\libraries\zlib-1.2.3&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="true"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Release/FileZilla server.pch"
				AssemblerOutput="2"
				AssemblerListingLocation=".\Release/"
				ObjectFile=".\Release/"
				ProgramDataBaseFileName=".\Release/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				CompileAs="0"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1031"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="version.lib ws2_32.lib zlib.lib"
				OutputFile=".\Release/FileZilla server.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile=".\Release/FileZilla server.pdb"
				GenerateMapFile="true"
				MapFileName=".\Release/FileZilla server.map"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				OptimizeForWindows98="1"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory=".\Debug"
			IntermediateDirectory=".\Debug"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			ExcludeBuckets="5;6;8;18"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/FileZilla server.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(ProjectDir)includes&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Debug/FileZilla server.pch"
				AssemblerOutput="2"
				AssemblerListingLocation=".\Debug/"
				ObjectFile=".\Debug/"
				ProgramDataBaseFileName=".\Debug/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1031"
			/>
			<Tool
				Name="VCPreLinkEventTool"
				Description="Terminate service..."
				CommandLine="if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /stop&#x0D;&#x0A;if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /uninstall&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="zlibd.lib version.lib ws2_32.lib odbc32.lib odbccp32.lib"
				OutputFile=".\Debug/FileZilla server.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile=".\Debug/FileZilla server.pdb"
				GenerateMapFile="true"
				MapFileName=".\Debug/FileZilla server.map"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
				Description="Install Service..."
				CommandLine="&quot;$(OutDir)\FileZilla Server.exe&quot; /stop&#x0D;&#x0A;&quot;$(OutDir)\FileZilla Server.exe&quot; /uninstall&#x0D;&#x0A;&quot;$(OutDir)\FileZilla Server.exe&quot; /install auto&#x0D;&#x0A;&quot;$(OutDir)\FileZilla Server.exe&quot; /start&#x0D;&#x0A;"
			/>
		</Configuration>
		<Configuration
			Name="Memcheck|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			ExcludeBuckets="5;6;8;18"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="true"
				SuppressStartupBanner="true"
				TargetEnvironment="1"
				TypeLibraryName=".\Compat_Debug/FileZilla server.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;$(ProjectDir)includes&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS,MMGR"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Compat_Debug/FileZilla server.pch"
				AssemblerListingLocation=".\Compat_Debug/"
				ObjectFile=".\Compat_Debug/"
				ProgramDataBaseFileName=".\Compat_Debug/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1031"
			/>
			<Tool
				Name="VCPreLinkEventTool"
				Description="Terminate service..."
				CommandLine="if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /stop&#x0D;&#x0A;if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /compat /stop&#x0D;&#x0A;if EXIST &quot;$(OutDir)\FileZilla Server.exe&quot; &quot;$(OutDir)\FileZilla Server.exe&quot; /uninstall&#x0D;&#x0A;"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="zlibd.lib version.lib ws2_32.lib odbc32.lib odbccp32.lib"
				OutputFile=".\memcheck/FileZilla server.exe"
				LinkIncremental="1"
				SuppressStartupBanner="true"
				IgnoreDefaultLibraryNames=""
				GenerateDebugInformation="true"
				ProgramDatabaseFile=".\Compat_Debug/FileZilla server.pdb"
				GenerateMapFile="true"
				MapFileName=".\Compat_Debug/FileZilla server.map"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
				CommandLine="&quot;$(OutDir)\FileZilla Server.exe&quot; /stop&#x0D;&#x0A;&quot;$(OutDir)\FileZilla Server.exe&quot; /uninstall&#x0D;&#x0A;"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Quellcodedateien"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="Accounts.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="AdminInterface.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="AdminListenSocket.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="AdminSocket.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="AsyncGssSocketLayer.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="AsyncSocketEx.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="AsyncSocketExLayer.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\AsyncSslSocketLayer.cpp"
				>
			</File>
			<File
				RelativePath=".\autobanmanager.cpp"
				>
			</File>
			<File
				RelativePath="ControlSocket.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\conversion.cpp"
				>
			</File>
			<File
				RelativePath="ExternalIpCheck.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="FileLogger.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="FileZilla server.rc"
				>
			</File>
			<File
				RelativePath=".\hash_thread.cpp"
				>
			</File>
			<File
				RelativePath=".\iputils.cpp"
				>
			</File>
			<File
				RelativePath="ListenSocket.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="misc\md5.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="MFC64bitFix.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\misc\mmgr.cpp"
				>
			</File>
			<File
				RelativePath="Options.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Permissions.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Server.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="ServerThread.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Service.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SpeedLimit.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StdAfx.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						UsePrecompiledHeader="1"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Thread.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tinyxml\tinystr.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tinyxml\tinyxml.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tinyxml\tinyxmlerror.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tinyxml\tinyxmlparser.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="TransferSocket.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="version.cpp"
				>
				<FileConfiguration
					Name="Compat Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Memcheck|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\xml_utils.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header-Dateien"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="Accounts.h"
				>
			</File>
			<File
				RelativePath="AdminInterface.h"
				>
			</File>
			<File
				RelativePath="AdminListenSocket.h"
				>
			</File>
			<File
				RelativePath="AdminSocket.h"
				>
			</File>
			<File
				RelativePath="AsyncGssSocketLayer.h"
				>
			</File>
			<File
				RelativePath="AsyncSocketEx.h"
				>
			</File>
			<File
				RelativePath="AsyncSocketExLayer.h"
				>
			</File>
			<File
				RelativePath=".\AsyncSslSocketLayer.h"
				>
			</File>
			<File
				RelativePath=".\autobanmanager.h"
				>
			</File>
			<File
				RelativePath="config.h"
				>
			</File>
			<File
				RelativePath="ControlSocket.h"
				>
			</File>
			<File
				RelativePath=".\conversion.h"
				>
			</File>
			<File
				RelativePath=".\defs.h"
				>
			</File>
			<File
				RelativePath="ExternalIpCheck.h"
				>
			</File>
			<File
				RelativePath="FileLogger.h"
				>
			</File>
			<File
				RelativePath=".\hash_thread.h"
				>
			</File>
			<File
				RelativePath=".\iputils.h"
				>
			</File>
			<File
				RelativePath="ListenSocket.h"
				>
			</File>
			<File
				RelativePath="MFC64bitFix.h"
				>
			</File>
			<File
				RelativePath=".\OptionLimits.h"
				>
			</File>
			<File
				RelativePath="Options.h"
				>
			</File>
			<File
				RelativePath="OptionTypes.h"
				>
			</File>
			<File
				RelativePath="Permissions.h"
				>
			</File>
			<File
				RelativePath="resource.h"
				>
			</File>
			<File
				RelativePath="Server.h"
				>
			</File>
			<File
				RelativePath="ServerThread.h"
				>
			</File>
			<File
				RelativePath="StdAfx.h"
				>
			</File>
			<File
				RelativePath="Thread.h"
				>
			</File>
			<File
				RelativePath="TransferSocket.h"
				>
			</File>
			<File
				RelativePath="version.h"
				>
			</File>
			<File
				RelativePath=".\xml_utils.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Ressourcendateien"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe"
			>
			<File
				RelativePath="res\empty.ico"
				>
			</File>
			<File
				RelativePath="res\FileZilla server.ico"
				>
			</File>
			<File
				RelativePath="res\green.ico"
				>
			</File>
			<File
				RelativePath="res\icon1.ico"
				>
			</File>
			<File
				RelativePath="res\red.ico"
				>
			</File>
			<File
				RelativePath="res\Toolbar.bmp"
				>
			</File>
			<File
				RelativePath="res\yellow.ico"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
