/* line 4, /app/source-xampp-windows/stylesheets/all.scss */
body {
  display: flex;
  flex-direction: column;
  min-height: 100vh; }

/* line 10, /app/source-xampp-windows/stylesheets/all.scss */
.wrapper {
  background: white;
  padding-bottom: 2em;
  flex: 1; }

/* line 16, /app/source-xampp-windows/stylesheets/all.scss */
ol.no-number {
  list-style: none;
  margin-left: 0; }

/* line 21, /app/source-xampp-windows/stylesheets/all.scss */
code {
  white-space: pre-line; }

/* line 27, /app/source-xampp-windows/stylesheets/all.scss */
ol.separate > li, ul.separate > li {
  border-bottom: 1px solid #ddd;
  margin-bottom: 0.5em; }

/* Locale selector */
/* line 36, /app/source-xampp-windows/stylesheets/all.scss */
#locales a img {
  margin-right: 0.5em;
  vertical-align: baseline; }

/* line 43, /app/source-xampp-windows/stylesheets/all.scss */
h1 a[name], h2 a[name], h3 a[name], h4 a[name], h5 a[name], h6 a[name] {
  color: inherit;
  cursor: inherit;
  text-decoration: none; }

/* line 50, /app/source-xampp-windows/stylesheets/all.scss */
h3 + a img, h4 + a img {
  margin-bottom: 0.5em; }

/* line 55, /app/source-xampp-windows/stylesheets/all.scss */
img.margin.right, body[class$="about"] dl dd img.margin {
  margin: 0 0 1em 1em; }
/* line 58, /app/source-xampp-windows/stylesheets/all.scss */
img.margin.left {
  margin: 0 1em 1em 0; }

/* line 63, /app/source-xampp-windows/stylesheets/all.scss */
p code {
  direction: ltr;
  font-size: inherit;
  font-weight: normal;
  color: #2a5d84;
  display: block;
  padding: 0.5em 1em !important; }

/* latest news headling */
/* line 78, /app/source-xampp-windows/stylesheets/all.scss */
#breaking div:first-child {
  display: table;
  text-align: center;
  margin-bottom: 0.5em; }
  /* line 83, /app/source-xampp-windows/stylesheets/all.scss */
  #breaking div:first-child h4 {
    font-size: 2em;
    line-height: 1.2em;
    display: table-cell;
    vertical-align: middle; }

/* line 94, /app/source-xampp-windows/stylesheets/all.scss */
a.download, .hero #download > .columns > a, .hero #stack-list #download > li > a, #stack-list .hero #download > li > a, .hero #theme-list li#download > div > a, #theme-list .hero li#download > div > a, a#forum {
  background: #fff;
  border: 1px solid #ccc;
  box-shadow: inset 0 1px 0 #fff;
  margin: 0.5em 0;
  padding: 10px;
  display: block;
  color: #777; }
  /* line 105, /app/source-xampp-windows/stylesheets/all.scss */
  a.download:active, .hero #download > .columns > a:active, .hero #stack-list #download > li > a:active, #stack-list .hero #download > li > a:active, .hero #theme-list li#download > div > a:active, #theme-list .hero li#download > div > a:active, a#forum:active, a.download:visited, .hero #download > .columns > a:visited, .hero #stack-list #download > li > a:visited, #stack-list .hero #download > li > a:visited, .hero #theme-list li#download > div > a:visited, #theme-list .hero li#download > div > a:visited, a#forum:visited {
    color: #ccc; }
  /* line 109, /app/source-xampp-windows/stylesheets/all.scss */
  a.download strong, .hero #download > .columns > a strong, .hero #stack-list #download > li > a strong, #stack-list .hero #download > li > a strong, .hero #theme-list li#download > div > a strong, #theme-list .hero li#download > div > a strong, a#forum strong {
    color: #555555; }
  /* line 113, /app/source-xampp-windows/stylesheets/all.scss */
  a.download img, .hero #download > .columns > a img, .hero #stack-list #download > li > a img, #stack-list .hero #download > li > a img, .hero #theme-list li#download > div > a img, #theme-list .hero li#download > div > a img, a#forum img {
    margin-right: 10px;
    vertical-align: text-top;
    width: 20px;
    height: 20px; }

/* hero unit */
/* line 121, /app/source-xampp-windows/stylesheets/all.scss */
.hero {
  background: #efeee5;
  padding: 1em 0;
  margin-bottom: 2em;
  /* download bar */ }
  /* line 126, /app/source-xampp-windows/stylesheets/all.scss */
  .hero h1 {
    font-weight: bold;
    font-size: 3em;
    line-height: 1em;
    padding: 0.3em 0; }
    /* line 133, /app/source-xampp-windows/stylesheets/all.scss */
    .hero h1 span {
      font-weight: normal;
      font-size: 0.5em;
      display: block; }
    /* line 138, /app/source-xampp-windows/stylesheets/all.scss */
    .hero h1 img {
      height: 1em;
      margin-left: 0.3em;
      vertical-align: bottom; }
  /* line 149, /app/source-xampp-windows/stylesheets/all.scss */
  .hero h2 {
    font-size: 1.6em;
    font-weight: bold; }
  /* line 153, /app/source-xampp-windows/stylesheets/all.scss */
  .hero h3 {
    font-size: 1.4em; }
  /* line 157, /app/source-xampp-windows/stylesheets/all.scss */
  .hero #download {
    background: #fff;
    border: 1px solid #ccc;
    padding: 5px;
    margin-top: 1em; }
    /* line 164, /app/source-xampp-windows/stylesheets/all.scss */
    .hero #download > .columns:first-child, .hero #stack-list #download > li:first-child, #stack-list .hero #download > li:first-child, .hero #theme-list li#download > div:first-child, #theme-list .hero li#download > div:first-child {
      display: none;
      /* overriden by @media query */
      padding-left: 0; }
    /* line 169, /app/source-xampp-windows/stylesheets/all.scss */
    .hero #download > .columns > a, .hero #stack-list #download > li > a, #stack-list .hero #download > li > a, .hero #theme-list li#download > div > a, #theme-list .hero li#download > div > a {
      text-align: center;
      background: #eee; }
      /* line 173, /app/source-xampp-windows/stylesheets/all.scss */
      .hero #download > .columns > a:active, .hero #stack-list #download > li > a:active, #stack-list .hero #download > li > a:active, .hero #theme-list li#download > div > a:active, #theme-list .hero li#download > div > a:active, .hero #download > .columns > a:visited, .hero #stack-list #download > li > a:visited, #stack-list .hero #download > li > a:visited, .hero #theme-list li#download > div > a:visited, #theme-list .hero li#download > div > a:visited {
        color: inherit; }
      /* line 177, /app/source-xampp-windows/stylesheets/all.scss */
      .hero #download > .columns > a span, .hero #stack-list #download > li > a span, #stack-list .hero #download > li > a span, .hero #theme-list li#download > div > a span, #theme-list .hero li#download > div > a span {
        display: block;
        margin-top: 0.5em;
        color: #777; }
    /* line 185, /app/source-xampp-windows/stylesheets/all.scss */
    .hero #download #arrow {
      height: 80px;
      padding: 10px 20px 0 0;
      position: relative;
      color: #fff;
      background: #5e8949; }
      /* line 197, /app/source-xampp-windows/stylesheets/all.scss */
      .hero #download #arrow:after {
        top: 50%;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        right: 100%;
        border-right-color: #5e8949;
        margin-right: -30px;
        border-width: 40px;
        margin-top: -40px;
        background: #fff; }
      /* line 219, /app/source-xampp-windows/stylesheets/all.scss */
      .hero #download #arrow h2 {
        color: inherit;
        margin-bottom: 0;
        font-weight: normal;
        font-size: 1.4em; }
      /* line 226, /app/source-xampp-windows/stylesheets/all.scss */
      .hero #download #arrow a {
        font-size: 0.8em;
        color: rgba(255, 255, 255, 0.8); }
        /* line 230, /app/source-xampp-windows/stylesheets/all.scss */
        .hero #download #arrow a:hover {
          text-decoration: underline; }

/* line 238, /app/source-xampp-windows/stylesheets/all.scss */
body.index .hero {
  padding: 2em 0; }
  /* line 241, /app/source-xampp-windows/stylesheets/all.scss */
  body.index .hero h1 {
    text-align: center;
    border-top: 2px solid #ccc;
    border-bottom: 2px solid #ccc;
    margin-bottom: 1em; }

/* line 250, /app/source-xampp-windows/stylesheets/all.scss */
.panel h1, p code h1, ul#downloads > li h1, .panel h2, p code h2, ul#downloads > li h2, .panel h3, p code h3, ul#downloads > li h3, .panel h4, p code h4, ul#downloads > li h4, .panel p, p code p, ul#downloads > li p {
  margin: 0; }

/* about, documentation & discussions */
/* line 257, /app/source-xampp-windows/stylesheets/all.scss */
#about h3, #documentation h3, #discussions h3 {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 1em;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.2em; }

/* line 268, /app/source-xampp-windows/stylesheets/all.scss */
#documentation dt, #documentation h5, #discussions dt, #discussions h5 {
  font-weight: bold;
  color: inherit;
  font-size: 1em; }
/* line 274, /app/source-xampp-windows/stylesheets/all.scss */
#documentation p, #discussions p {
  margin-bottom: 0; }
/* line 278, /app/source-xampp-windows/stylesheets/all.scss */
#documentation li, #documentation dd, #discussions li, #discussions dd {
  margin-bottom: 1em; }

/* sub page layout */
/* line 284, /app/source-xampp-windows/stylesheets/all.scss */
aside {
  padding: 0.5em 1em;
  background: #eee; }
  /* line 289, /app/source-xampp-windows/stylesheets/all.scss */
  aside h3:not(:first-child), aside h4:not(:first-child) {
    margin: 0.5em 0; }
  /* line 293, /app/source-xampp-windows/stylesheets/all.scss */
  aside > img {
    margin: 0.5em 0; }
  /* line 296, /app/source-xampp-windows/stylesheets/all.scss */
  aside .social {
    margin-bottom: 1em; }
  /* line 299, /app/source-xampp-windows/stylesheets/all.scss */
  aside ol.sections {
    list-style: none;
    margin-left: 0; }
    /* line 302, /app/source-xampp-windows/stylesheets/all.scss */
    aside ol.sections li {
      margin-bottom: 0.25em; }
      /* line 304, /app/source-xampp-windows/stylesheets/all.scss */
      aside ol.sections li:last-child {
        margin-bottom: 0; }

/* XAMPP stamps table */
/* line 314, /app/source-xampp-windows/stylesheets/all.scss */
table#stamps td:first-child {
  border-right: 1px solid #ddd;
  text-align: center; }

/* social icons */
/* line 323, /app/source-xampp-windows/stylesheets/all.scss */
.social {
  margin-bottom: 0; }
  /* line 327, /app/source-xampp-windows/stylesheets/all.scss */
  .social li {
    display: inline-block;
    margin-right: 5px; }
    /* line 331, /app/source-xampp-windows/stylesheets/all.scss */
    .social li a {
      display: block;
      width: 25px;
      height: 25px;
      text-indent: -9999px;
      background-image: url("/dashboard/images/social-icons.png"); }
    /* line 338, /app/source-xampp-windows/stylesheets/all.scss */
    .social li.twitter a {
      background-position: 0 0; }
    /* line 341, /app/source-xampp-windows/stylesheets/all.scss */
    .social li.facebook a {
      background-position: -25px 0; }
    /* line 344, /app/source-xampp-windows/stylesheets/all.scss */
    .social li.google a {
      background-position: -50px 0; }
  /* line 349, /app/source-xampp-windows/stylesheets/all.scss */
  .social.large {
    margin-bottom: 1em; }
    /* line 353, /app/source-xampp-windows/stylesheets/all.scss */
    .social.large li a {
      width: 70px;
      height: 70px;
      background-image: url("/dashboard/images/social-icons-large.png"); }
    /* line 358, /app/source-xampp-windows/stylesheets/all.scss */
    .social.large li.twitter a {
      background-position: 0 0; }
    /* line 361, /app/source-xampp-windows/stylesheets/all.scss */
    .social.large li.facebook a {
      background-position: -70px 0; }
    /* line 364, /app/source-xampp-windows/stylesheets/all.scss */
    .social.large li.google a {
      background-position: -140px 0; }

/* tweet it form */
/* line 373, /app/source-xampp-windows/stylesheets/all.scss */
form#tweet input, form#tweet textarea {
  margin-bottom: 0; }

/* downloads list */
/* line 386, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li h3 img {
  vertical-align: text-top;
  width: 40px;
  height: 40px;
  margin-right: 10px; }
/* line 391, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li h3 span {
  color: #777; }
/* line 396, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li table {
  margin-top: 1.5em; }
  /* line 399, /app/source-xampp-windows/stylesheets/all.scss */
  ul#downloads > li table th {
    text-align: left; }
  /* line 405, /app/source-xampp-windows/stylesheets/all.scss */
  ul#downloads > li table td ul.inline-list li:last-child {
    margin-right: 0; }
  /* line 411, /app/source-xampp-windows/stylesheets/all.scss */
  ul#downloads > li table .button {
    margin: 0 0 5px 5px;
    padding: 7px;
    font-size: 0.9em; }
    /* line 416, /app/source-xampp-windows/stylesheets/all.scss */
    ul#downloads > li table .button:last-child {
      margin-bottom: 0; }
/* line 423, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li ul.inline-list, ul#downloads > li ul.inline-list li {
  margin-left: 0; }
/* line 426, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li ul.inline-list li {
  margin-right: 1em; }
/* line 431, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li .notes {
  max-width: 550px !important; }
  /* line 435, /app/source-xampp-windows/stylesheets/all.scss */
  ul#downloads > li .notes p {
    font-size: 0.8em;
    color: #777;
    margin-bottom: 0; }
/* line 443, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li#download-windows p:last-child {
  color: #777;
  margin-top: 1em;
  font-size: 0.8em; }

/* line 453, /app/source-xampp-windows/stylesheets/all.scss */
ul#downloads > li table ul[id$="-download"], #download > div ul[id$="-download"] {
  padding: 0 !important;
  max-width: 50px !important;
  text-align: center; }

/* team images */
/* line 464, /app/source-xampp-windows/stylesheets/all.scss */
body[class$="about"] dl dd img {
  border: 1px solid #eee;
  padding: 3px;
  margin: 0 0 0.5em 0.5em; }

/* line 472, /app/source-xampp-windows/stylesheets/all.scss */
#mailing-list {
  margin-bottom: 0.5em; }
  /* line 475, /app/source-xampp-windows/stylesheets/all.scss */
  #mailing-list input, #mailing-list a {
    margin-bottom: 0; }

/* line 480, /app/source-xampp-windows/stylesheets/all.scss */
#intro {
  margin-bottom: 0; }

/* line 484, /app/source-xampp-windows/stylesheets/all.scss */
#stack-list {
  list-style: none;
  margin-left: 0; }
  /* line 489, /app/source-xampp-windows/stylesheets/all.scss */
  #stack-list li {
    display: inline-block;
    margin: 0 0 0.5em 0;
    text-align: center; }
    /* line 495, /app/source-xampp-windows/stylesheets/all.scss */
    #stack-list li a {
      color: #2a5d84;
      display: block;
      min-height: 200px; }
      /* line 500, /app/source-xampp-windows/stylesheets/all.scss */
      #stack-list li a span {
        display: block; }
        /* line 503, /app/source-xampp-windows/stylesheets/all.scss */
        #stack-list li a span.type {
          font-size: 0.9em;
          color: #777; }

/* line 512, /app/source-xampp-windows/stylesheets/all.scss */
#theme-list {
  list-style: none;
  margin-left: 0; }
  /* line 516, /app/source-xampp-windows/stylesheets/all.scss */
  #theme-list li {
    margin-bottom: 1.5em !important; }
    /* line 520, /app/source-xampp-windows/stylesheets/all.scss */
    #theme-list li h4 a {
      color: inherit; }

/* line 535, /app/source-xampp-windows/stylesheets/all.scss */
body.add-ons #theme-list {
  margin-bottom: 2em; }
/* line 538, /app/source-xampp-windows/stylesheets/all.scss */
body.add-ons aside ul {
  margin-bottom: 0; }

/* line 544, /app/source-xampp-windows/stylesheets/all.scss */
.button.icon img {
  margin-right: 1em; }

/* line 549, /app/source-xampp-windows/stylesheets/all.scss */
a#forum {
  display: inline-block;
  font-size: 1.4em;
  color: #fb7a24; }
  /* line 555, /app/source-xampp-windows/stylesheets/all.scss */
  a#forum img {
    width: 30px;
    height: 30px;
    margin-right: 0.5em; }
  /* line 560, /app/source-xampp-windows/stylesheets/all.scss */
  a#forum span {
    display: block;
    font-size: 0.7em;
    color: #777;
    text-align: right; }

/* line 570, /app/source-xampp-windows/stylesheets/all.scss */
ol#articles li span {
  font-size: 0.8em;
  color: #777;
  display: block; }

/* line 579, /app/source-xampp-windows/stylesheets/all.scss */
.accordion dt {
  border-bottom: 1px solid #ddd;
  padding: 0.5em 0;
  margin-bottom: 0;
  font-size: 1.1em; }
/* line 585, /app/source-xampp-windows/stylesheets/all.scss */
.accordion dd {
  margin-top: 1em; }

/* Retina Overrides */
@media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2 / 1), only screen and (min--moz-device-pixel-ratio: 2), only screen and (min-device-pixel-ratio: 2) {
  /* line 595, /app/source-xampp-windows/stylesheets/all.scss */
  .social li a {
    background-image: url("/dashboard/images/<EMAIL>");
    background-size: auto 25px; }

  /* line 599, /app/source-xampp-windows/stylesheets/all.scss */
  .social.large li a {
    background-image: url("/dashboard/images/<EMAIL>");
    background-size: auto 70px; } }
/* line 1, /app/source-xampp-windows/stylesheets/_footer.scss */
.footer {
  background-color: #333333;
  color: #888888;
  padding: 2em 0; }
  /* line 7, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer p,
  .footer a {
    font-size: 0.8em;
    color: inherit; }
  /* line 12, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer p {
    margin-bottom: 0; }
  /* line 19, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer a:hover, .footer a:active, .footer a:visited {
    color: inherit; }
  /* line 24, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer a:hover, .footer a:active {
    text-decoration: underline; }
  /* line 32, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer .footer_lists-container.row > *:not(:last-child), .footer .footer_lists-container#stack-list > *:not(:last-child), .footer #theme-list li.footer_lists-container > *:not(:last-child), #theme-list .footer li.footer_lists-container > *:not(:last-child), .footer ul.footer_lists-container#docs-list > *:not(:last-child) {
    margin-bottom: 0.5em; }
    @media only screen and (min-width: 64.063em) {
      /* line 32, /app/source-xampp-windows/stylesheets/_footer.scss */
      .footer .footer_lists-container.row > *:not(:last-child), .footer .footer_lists-container#stack-list > *:not(:last-child), .footer #theme-list li.footer_lists-container > *:not(:last-child), #theme-list .footer li.footer_lists-container > *:not(:last-child), .footer ul.footer_lists-container#docs-list > *:not(:last-child) {
        margin-bottom: 0;
        margin-right: 0.5em; } }
  /* line 42, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer .footer_social {
    text-align: center; }
    /* line 45, /app/source-xampp-windows/stylesheets/_footer.scss */
    .footer .footer_social > *:not(:last-child) {
      margin-bottom: 0.5em; }
  /* line 50, /app/source-xampp-windows/stylesheets/_footer.scss */
  .footer .footer_links {
    list-style: none;
    margin: 0; }
    @media only screen and (min-width: 40.063em) {
      /* line 50, /app/source-xampp-windows/stylesheets/_footer.scss */
      .footer .footer_links {
        display: flex;
        align-items: baseline;
        justify-content: center;
        flex-wrap: wrap;
        column-gap: 1em; } }
    @media only screen and (min-width: 64.063em) {
      /* line 50, /app/source-xampp-windows/stylesheets/_footer.scss */
      .footer .footer_links {
        column-gap: 1.5em; } }

/* line 20, /app/source-xampp-windows/stylesheets/_header.scss */
.top-bar-section .dropdown {
  border-bottom: 2px solid #eeeeee; }
  /* line 23, /app/source-xampp-windows/stylesheets/_header.scss */
  .top-bar-section .dropdown li {
    border-bottom: 1px solid #eeeeee; }
    /* line 26, /app/source-xampp-windows/stylesheets/_header.scss */
    .top-bar-section .dropdown li:last-child {
      border-bottom: 0; }
    /* line 29, /app/source-xampp-windows/stylesheets/_header.scss */
    .top-bar-section .dropdown li a:not(.button):hover {
      background: #2a5d84; }
/* line 35, /app/source-xampp-windows/stylesheets/_header.scss */
.top-bar-section li.has-form {
  padding: 8px 15px 0; }
/* line 39, /app/source-xampp-windows/stylesheets/_header.scss */
.top-bar-section .visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px; }
@media (max-width: 1023px) {
  /* line 51, /app/source-xampp-windows/stylesheets/_header.scss */
  .top-bar-section > ul > li.item > a:not(.button) {
    padding: 12px 20px; }
  /* line 55, /app/source-xampp-windows/stylesheets/_header.scss */
  .top-bar-section li.has-form {
    background: #1f3c52; } }
@media screen and (min-width: 1024px) {
  /* line 61, /app/source-xampp-windows/stylesheets/_header.scss */
  .top-bar-section ul.dropdown > li a {
    background: #fff; }
  /* line 65, /app/source-xampp-windows/stylesheets/_header.scss */
  .top-bar-section ul.dropdown > li a:not(.button):hover {
    background: #f2f2f2;
    color: #5e8949; } }

/* line 73, /app/source-xampp-windows/stylesheets/_header.scss */
.top-bar.expanded .top-bar-section > ul > li a:not(.button):hover {
  background: #2a5d84; }

/* import foundation last */
/* line 287, /app/bower_components/foundation/scss/foundation/components/_global.scss */
meta.foundation-mq-small {
  font-family: "/only screen and (max-width: 40em)/";
  width: 0em; }

/* line 292, /app/bower_components/foundation/scss/foundation/components/_global.scss */
meta.foundation-mq-medium {
  font-family: "/only screen and (min-width:40.063em) and (max-width:64em)/";
  width: 40.063em; }

/* line 297, /app/bower_components/foundation/scss/foundation/components/_global.scss */
meta.foundation-mq-large {
  font-family: "/only screen and (min-width:64.063em)/";
  width: 64.063em; }

/* line 302, /app/bower_components/foundation/scss/foundation/components/_global.scss */
meta.foundation-mq-xlarge {
  font-family: "/only screen and (min-width:90.063em)/";
  width: 90.063em; }

/* line 307, /app/bower_components/foundation/scss/foundation/components/_global.scss */
meta.foundation-mq-xxlarge {
  font-family: "/only screen and (min-width:120.063em)/";
  width: 120.063em; }

/* line 317, /app/bower_components/foundation/scss/foundation/components/_global.scss */
*,
*:before,
*:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box; }

/* line 322, /app/bower_components/foundation/scss/foundation/components/_global.scss */
html,
body {
  font-size: 100%; }

/* line 325, /app/bower_components/foundation/scss/foundation/components/_global.scss */
body {
  background: #333333;
  color: #555555;
  padding: 0;
  margin: 0;
  font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  position: relative;
  cursor: default; }

/* line 338, /app/bower_components/foundation/scss/foundation/components/_global.scss */
a:hover {
  cursor: pointer; }

/* line 343, /app/bower_components/foundation/scss/foundation/components/_global.scss */
img,
object,
embed {
  max-width: 100%;
  height: auto; }

/* line 346, /app/bower_components/foundation/scss/foundation/components/_global.scss */
object,
embed {
  height: 100%; }

/* line 347, /app/bower_components/foundation/scss/foundation/components/_global.scss */
img {
  -ms-interpolation-mode: bicubic; }

/* line 353, /app/bower_components/foundation/scss/foundation/components/_global.scss */
#map_canvas img,
#map_canvas embed,
#map_canvas object,
.map_canvas img,
.map_canvas embed,
.map_canvas object {
  max-width: none !important; }

/* line 358, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.left {
  float: left !important; }

/* line 359, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.right, body[class$="about"] dl dd img {
  float: right !important; }

/* line 360, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.clearfix, #breaking div, body[class$="about"] dl dd {
  *zoom: 1; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .clearfix:before, #breaking div:before, body[class$="about"] dl dd:before, .clearfix:after, #breaking div:after, body[class$="about"] dl dd:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .clearfix:after, #breaking div:after, body[class$="about"] dl dd:after {
    clear: both; }

/* line 361, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.text-left {
  text-align: left !important; }

/* line 362, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.text-right {
  text-align: right !important; }

/* line 363, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.text-center {
  text-align: center !important; }

/* line 364, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.text-justify {
  text-align: justify !important; }

/* line 365, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.hide {
  display: none; }

/* line 371, /app/bower_components/foundation/scss/foundation/components/_global.scss */
.antialiased {
  -webkit-font-smoothing: antialiased; }

/* line 374, /app/bower_components/foundation/scss/foundation/components/_global.scss */
img {
  display: inline-block;
  vertical-align: middle; }

/* line 384, /app/bower_components/foundation/scss/foundation/components/_global.scss */
textarea {
  height: auto;
  min-height: 50px; }

/* line 387, /app/bower_components/foundation/scss/foundation/components/_global.scss */
select {
  width: 100%; }

/* line 190, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
.row, #stack-list, #theme-list li, ul#docs-list {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  margin-top: 0;
  margin-bottom: 0;
  max-width: 62.5rem;
  *zoom: 1; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .row:before, #stack-list:before, #theme-list li:before, ul#docs-list:before, .row:after, #stack-list:after, #theme-list li:after, ul#docs-list:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .row:after, #stack-list:after, #theme-list li:after, ul#docs-list:after {
    clear: both; }
  /* line 195, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .row.collapse > .column, .collapse#stack-list > .column, #theme-list li.collapse > .column, ul.collapse#docs-list > .column,
  .row.collapse > .columns,
  .collapse#stack-list > .columns,
  #theme-list li.collapse > .columns,
  ul.collapse#docs-list > .columns,
  #stack-list .row.collapse > li,
  .collapse#stack-list > li,
  #theme-list #stack-list li.collapse > li,
  #stack-list #theme-list li.collapse > li,
  #theme-list li.collapse > div,
  ul#docs-list.collapse > li {
    position: relative;
    padding-left: 0;
    padding-right: 0;
    float: right; }
  /* line 197, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .row.collapse .row, .collapse#stack-list .row, #theme-list li.collapse .row, ul.collapse#docs-list .row, .row.collapse #stack-list, .collapse#stack-list #stack-list, #theme-list li.collapse #stack-list, ul.collapse#docs-list #stack-list, .row.collapse #theme-list li, #theme-list .row.collapse li, .collapse#stack-list #theme-list li, #theme-list .collapse#stack-list li, #theme-list li.collapse li, ul.collapse#docs-list #theme-list li, #theme-list ul.collapse#docs-list li, .row.collapse ul#docs-list, .collapse#stack-list ul#docs-list, #theme-list li.collapse ul#docs-list, ul.collapse#docs-list ul#docs-list {
    margin-left: 0;
    margin-right: 0; }
  /* line 200, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .row .row, #stack-list .row, #theme-list li .row, ul#docs-list .row, .row #stack-list, #stack-list #stack-list, #theme-list li #stack-list, ul#docs-list #stack-list, .row #theme-list li, #theme-list .row li, #stack-list #theme-list li, #theme-list #stack-list li, #theme-list li li, ul#docs-list #theme-list li, #theme-list ul#docs-list li, .row ul#docs-list, #stack-list ul#docs-list, #theme-list li ul#docs-list, ul#docs-list ul#docs-list {
    width: auto;
    margin-right: -0.9375rem;
    margin-left: -0.9375rem;
    margin-top: 0;
    margin-bottom: 0;
    max-width: none;
    *zoom: 1; }
    /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
    .row .row:before, #stack-list .row:before, #theme-list li .row:before, ul#docs-list .row:before, .row #stack-list:before, #stack-list #stack-list:before, #theme-list li #stack-list:before, ul#docs-list #stack-list:before, .row #theme-list li:before, #theme-list .row li:before, #stack-list #theme-list li:before, #theme-list #stack-list li:before, #theme-list li li:before, ul#docs-list #theme-list li:before, #theme-list ul#docs-list li:before, .row ul#docs-list:before, #stack-list ul#docs-list:before, #theme-list li ul#docs-list:before, ul#docs-list ul#docs-list:before, .row .row:after, #stack-list .row:after, #theme-list li .row:after, ul#docs-list .row:after, .row #stack-list:after, #stack-list #stack-list:after, #theme-list li #stack-list:after, ul#docs-list #stack-list:after, .row #theme-list li:after, #theme-list .row li:after, #stack-list #theme-list li:after, #theme-list #stack-list li:after, #theme-list li li:after, ul#docs-list #theme-list li:after, #theme-list ul#docs-list li:after, .row ul#docs-list:after, #stack-list ul#docs-list:after, #theme-list li ul#docs-list:after, ul#docs-list ul#docs-list:after {
      content: " ";
      display: table; }
    /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
    .row .row:after, #stack-list .row:after, #theme-list li .row:after, ul#docs-list .row:after, .row #stack-list:after, #stack-list #stack-list:after, #theme-list li #stack-list:after, ul#docs-list #stack-list:after, .row #theme-list li:after, #theme-list .row li:after, #stack-list #theme-list li:after, #theme-list #stack-list li:after, #theme-list li li:after, ul#docs-list #theme-list li:after, #theme-list ul#docs-list li:after, .row ul#docs-list:after, #stack-list ul#docs-list:after, #theme-list li ul#docs-list:after, ul#docs-list ul#docs-list:after {
      clear: both; }
    /* line 201, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
    .row .row.collapse, #stack-list .row.collapse, #theme-list li .row.collapse, ul#docs-list .row.collapse, .row .collapse#stack-list, #stack-list .collapse#stack-list, #theme-list li .collapse#stack-list, ul#docs-list .collapse#stack-list, .row #theme-list li.collapse, #theme-list .row li.collapse, #stack-list #theme-list li.collapse, #theme-list #stack-list li.collapse, #theme-list li li.collapse, ul#docs-list #theme-list li.collapse, #theme-list ul#docs-list li.collapse, .row ul.collapse#docs-list, #stack-list ul.collapse#docs-list, #theme-list li ul.collapse#docs-list, ul#docs-list ul.collapse#docs-list {
      width: auto;
      margin: 0;
      max-width: none;
      *zoom: 1; }
      /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
      .row .row.collapse:before, #stack-list .row.collapse:before, #theme-list li .row.collapse:before, ul#docs-list .row.collapse:before, .row .collapse#stack-list:before, #stack-list .collapse#stack-list:before, #theme-list li .collapse#stack-list:before, ul#docs-list .collapse#stack-list:before, .row #theme-list li.collapse:before, #theme-list .row li.collapse:before, #stack-list #theme-list li.collapse:before, #theme-list #stack-list li.collapse:before, #theme-list li li.collapse:before, ul#docs-list #theme-list li.collapse:before, #theme-list ul#docs-list li.collapse:before, .row ul.collapse#docs-list:before, #stack-list ul.collapse#docs-list:before, #theme-list li ul.collapse#docs-list:before, ul#docs-list ul.collapse#docs-list:before, .row .row.collapse:after, #stack-list .row.collapse:after, #theme-list li .row.collapse:after, ul#docs-list .row.collapse:after, .row .collapse#stack-list:after, #stack-list .collapse#stack-list:after, #theme-list li .collapse#stack-list:after, ul#docs-list .collapse#stack-list:after, .row #theme-list li.collapse:after, #theme-list .row li.collapse:after, #stack-list #theme-list li.collapse:after, #theme-list #stack-list li.collapse:after, #theme-list li li.collapse:after, ul#docs-list #theme-list li.collapse:after, #theme-list ul#docs-list li.collapse:after, .row ul.collapse#docs-list:after, #stack-list ul.collapse#docs-list:after, #theme-list li ul.collapse#docs-list:after, ul#docs-list ul.collapse#docs-list:after {
        content: " ";
        display: table; }
      /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
      .row .row.collapse:after, #stack-list .row.collapse:after, #theme-list li .row.collapse:after, ul#docs-list .row.collapse:after, .row .collapse#stack-list:after, #stack-list .collapse#stack-list:after, #theme-list li .collapse#stack-list:after, ul#docs-list .collapse#stack-list:after, .row #theme-list li.collapse:after, #theme-list .row li.collapse:after, #stack-list #theme-list li.collapse:after, #theme-list #stack-list li.collapse:after, #theme-list li li.collapse:after, ul#docs-list #theme-list li.collapse:after, #theme-list ul#docs-list li.collapse:after, .row ul.collapse#docs-list:after, #stack-list ul.collapse#docs-list:after, #theme-list li ul.collapse#docs-list:after, ul#docs-list ul.collapse#docs-list:after {
        clear: both; }

/* line 206, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
.column,
.columns,
#stack-list li,
#theme-list li > div,
ul#docs-list > li {
  position: relative;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  width: 100%;
  float: right; }

@media only screen {
  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-1 {
    position: relative;
    right: 8.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-1 {
    position: relative;
    left: 8.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-2 {
    position: relative;
    right: 16.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-2 {
    position: relative;
    left: 16.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-3 {
    position: relative;
    right: 25%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-3 {
    position: relative;
    left: 25%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-4 {
    position: relative;
    right: 33.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-4 {
    position: relative;
    left: 33.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-5 {
    position: relative;
    right: 41.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-5 {
    position: relative;
    left: 41.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-6 {
    position: relative;
    right: 50%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-6 {
    position: relative;
    left: 50%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-7 {
    position: relative;
    right: 58.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-7 {
    position: relative;
    left: 58.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-8 {
    position: relative;
    right: 66.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-8 {
    position: relative;
    left: 66.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-9 {
    position: relative;
    right: 75%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-9 {
    position: relative;
    left: 75%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-10 {
    position: relative;
    right: 83.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-10 {
    position: relative;
    left: 83.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-push-11 {
    position: relative;
    right: 91.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-pull-11 {
    position: relative;
    left: 91.66667%;
    right: auto; }

  /* line 157, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column,
  .columns,
  #stack-list li,
  #theme-list li > div,
  ul#docs-list > li {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: right; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-1 {
    position: relative;
    width: 8.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-2 {
    position: relative;
    width: 16.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-3 {
    position: relative;
    width: 25%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-4 {
    position: relative;
    width: 33.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-5 {
    position: relative;
    width: 41.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-6, #stack-list li {
    position: relative;
    width: 50%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-7 {
    position: relative;
    width: 58.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-8 {
    position: relative;
    width: 66.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-9 {
    position: relative;
    width: 75%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-10 {
    position: relative;
    width: 83.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-11 {
    position: relative;
    width: 91.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-12 {
    position: relative;
    width: 100%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-0 {
    position: relative;
    margin-right: 0%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-1 {
    position: relative;
    margin-right: 8.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-2 {
    position: relative;
    margin-right: 16.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-3 {
    position: relative;
    margin-right: 25%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-4 {
    position: relative;
    margin-right: 33.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-5 {
    position: relative;
    margin-right: 41.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-6 {
    position: relative;
    margin-right: 50%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-7 {
    position: relative;
    margin-right: 58.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-8 {
    position: relative;
    margin-right: 66.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-9 {
    position: relative;
    margin-right: 75%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .small-offset-10 {
    position: relative;
    margin-right: 83.33333%; }

  /* line 168, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"]:last-child {
    float: left; }

  /* line 169, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"].end {
    float: right; }

  /* line 172, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.small-centered,
  .columns.small-centered,
  #stack-list li.small-centered,
  #theme-list li > div.small-centered,
  ul#docs-list > li.small-centered {
    position: relative;
    margin-right: auto;
    margin-left: auto;
    float: none !important; }

  /* line 175, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.small-uncentered,
  .columns.small-uncentered,
  #stack-list li.small-uncentered,
  #theme-list li > div.small-uncentered,
  ul#docs-list > li.small-uncentered {
    margin-right: 0;
    margin-left: 0;
    float: right !important; }

  /* line 182, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.small-uncentered.opposite,
  .columns.small-uncentered.opposite,
  #stack-list li.small-uncentered.opposite,
  #theme-list li > div.small-uncentered.opposite,
  ul#docs-list > li.small-uncentered.opposite {
    float: left !important; } }
@media only screen and (min-width: 40.063em) {
  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-1 {
    position: relative;
    right: 8.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-1 {
    position: relative;
    left: 8.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-2 {
    position: relative;
    right: 16.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-2 {
    position: relative;
    left: 16.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-3 {
    position: relative;
    right: 25%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-3 {
    position: relative;
    left: 25%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-4 {
    position: relative;
    right: 33.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-4 {
    position: relative;
    left: 33.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-5 {
    position: relative;
    right: 41.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-5 {
    position: relative;
    left: 41.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-6 {
    position: relative;
    right: 50%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-6 {
    position: relative;
    left: 50%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-7 {
    position: relative;
    right: 58.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-7 {
    position: relative;
    left: 58.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-8 {
    position: relative;
    right: 66.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-8 {
    position: relative;
    left: 66.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-9 {
    position: relative;
    right: 75%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-9 {
    position: relative;
    left: 75%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-10 {
    position: relative;
    right: 83.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-10 {
    position: relative;
    left: 83.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-push-11 {
    position: relative;
    right: 91.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-pull-11 {
    position: relative;
    left: 91.66667%;
    right: auto; }

  /* line 157, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column,
  .columns,
  #stack-list li,
  #theme-list li > div,
  ul#docs-list > li {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: right; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-1 {
    position: relative;
    width: 8.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-2, #theme-list li > div.theme-icon {
    position: relative;
    width: 16.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-3 {
    position: relative;
    width: 25%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-4 {
    position: relative;
    width: 33.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-5 {
    position: relative;
    width: 41.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-6 {
    position: relative;
    width: 50%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-7 {
    position: relative;
    width: 58.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-8 {
    position: relative;
    width: 66.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-9 {
    position: relative;
    width: 75%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-10, #theme-list li > div {
    position: relative;
    width: 83.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-11 {
    position: relative;
    width: 91.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-12 {
    position: relative;
    width: 100%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-0 {
    position: relative;
    margin-right: 0%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-1 {
    position: relative;
    margin-right: 8.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-2 {
    position: relative;
    margin-right: 16.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-3 {
    position: relative;
    margin-right: 25%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-4 {
    position: relative;
    margin-right: 33.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-5 {
    position: relative;
    margin-right: 41.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-6 {
    position: relative;
    margin-right: 50%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-7 {
    position: relative;
    margin-right: 58.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-8 {
    position: relative;
    margin-right: 66.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-9 {
    position: relative;
    margin-right: 75%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .medium-offset-10 {
    position: relative;
    margin-right: 83.33333%; }

  /* line 168, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"]:last-child {
    float: left; }

  /* line 169, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"].end {
    float: right; }

  /* line 172, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.medium-centered,
  .columns.medium-centered,
  #stack-list li.medium-centered,
  #theme-list li > div.medium-centered,
  ul#docs-list > li.medium-centered {
    position: relative;
    margin-right: auto;
    margin-left: auto;
    float: none !important; }

  /* line 175, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.medium-uncentered,
  .columns.medium-uncentered,
  #stack-list li.medium-uncentered,
  #theme-list li > div.medium-uncentered,
  ul#docs-list > li.medium-uncentered {
    margin-right: 0;
    margin-left: 0;
    float: right !important; }

  /* line 182, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.medium-uncentered.opposite,
  .columns.medium-uncentered.opposite,
  #stack-list li.medium-uncentered.opposite,
  #theme-list li > div.medium-uncentered.opposite,
  ul#docs-list > li.medium-uncentered.opposite {
    float: left !important; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-1 {
    position: relative;
    right: 8.33333%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-1 {
    position: relative;
    left: 8.33333%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-2 {
    position: relative;
    right: 16.66667%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-2 {
    position: relative;
    left: 16.66667%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-3 {
    position: relative;
    right: 25%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-3 {
    position: relative;
    left: 25%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-4 {
    position: relative;
    right: 33.33333%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-4 {
    position: relative;
    left: 33.33333%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-5 {
    position: relative;
    right: 41.66667%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-5 {
    position: relative;
    left: 41.66667%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-6 {
    position: relative;
    right: 50%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-6 {
    position: relative;
    left: 50%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-7 {
    position: relative;
    right: 58.33333%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-7 {
    position: relative;
    left: 58.33333%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-8 {
    position: relative;
    right: 66.66667%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-8 {
    position: relative;
    left: 66.66667%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-9 {
    position: relative;
    right: 75%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-9 {
    position: relative;
    left: 75%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-10 {
    position: relative;
    right: 83.33333%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-10 {
    position: relative;
    left: 83.33333%;
    right: auto; }

  /* line 216, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .push-11 {
    position: relative;
    right: 91.66667%;
    left: auto; }

  /* line 219, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .pull-11 {
    position: relative;
    left: 91.66667%;
    right: auto; } }
@media only screen and (min-width: 64.063em) {
  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-1 {
    position: relative;
    right: 8.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-1 {
    position: relative;
    left: 8.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-2 {
    position: relative;
    right: 16.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-2 {
    position: relative;
    left: 16.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-3 {
    position: relative;
    right: 25%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-3 {
    position: relative;
    left: 25%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-4 {
    position: relative;
    right: 33.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-4 {
    position: relative;
    left: 33.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-5 {
    position: relative;
    right: 41.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-5 {
    position: relative;
    left: 41.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-6 {
    position: relative;
    right: 50%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-6 {
    position: relative;
    left: 50%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-7 {
    position: relative;
    right: 58.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-7 {
    position: relative;
    left: 58.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-8 {
    position: relative;
    right: 66.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-8 {
    position: relative;
    left: 66.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-9 {
    position: relative;
    right: 75%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-9 {
    position: relative;
    left: 75%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-10 {
    position: relative;
    right: 83.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-10 {
    position: relative;
    left: 83.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-push-11 {
    position: relative;
    right: 91.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-pull-11 {
    position: relative;
    left: 91.66667%;
    right: auto; }

  /* line 157, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column,
  .columns,
  #stack-list li,
  #theme-list li > div,
  ul#docs-list > li {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: right; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-1 {
    position: relative;
    width: 8.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-2 {
    position: relative;
    width: 16.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-3, #stack-list li {
    position: relative;
    width: 25%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-4 {
    position: relative;
    width: 33.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-5 {
    position: relative;
    width: 41.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-6, ul#docs-list > li {
    position: relative;
    width: 50%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-7 {
    position: relative;
    width: 58.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-8 {
    position: relative;
    width: 66.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-9 {
    position: relative;
    width: 75%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-10 {
    position: relative;
    width: 83.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-11 {
    position: relative;
    width: 91.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-12 {
    position: relative;
    width: 100%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-0 {
    position: relative;
    margin-right: 0%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-1 {
    position: relative;
    margin-right: 8.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-2 {
    position: relative;
    margin-right: 16.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-3 {
    position: relative;
    margin-right: 25%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-4 {
    position: relative;
    margin-right: 33.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-5 {
    position: relative;
    margin-right: 41.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-6 {
    position: relative;
    margin-right: 50%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-7 {
    position: relative;
    margin-right: 58.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-8 {
    position: relative;
    margin-right: 66.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-9 {
    position: relative;
    margin-right: 75%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .large-offset-10 {
    position: relative;
    margin-right: 83.33333%; }

  /* line 168, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"]:last-child {
    float: left; }

  /* line 169, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"].end {
    float: right; }

  /* line 172, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.large-centered,
  .columns.large-centered,
  #stack-list li.large-centered,
  #theme-list li > div.large-centered,
  ul#docs-list > li.large-centered {
    position: relative;
    margin-right: auto;
    margin-left: auto;
    float: none !important; }

  /* line 175, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.large-uncentered,
  .columns.large-uncentered,
  #stack-list li.large-uncentered,
  #theme-list li > div.large-uncentered,
  ul#docs-list > li.large-uncentered {
    margin-right: 0;
    margin-left: 0;
    float: right !important; }

  /* line 182, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.large-uncentered.opposite,
  .columns.large-uncentered.opposite,
  #stack-list li.large-uncentered.opposite,
  #theme-list li > div.large-uncentered.opposite,
  ul#docs-list > li.large-uncentered.opposite {
    float: left !important; } }
@media only screen and (min-width: 90.063em) {
  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-1 {
    position: relative;
    right: 8.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-1 {
    position: relative;
    left: 8.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-2 {
    position: relative;
    right: 16.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-2 {
    position: relative;
    left: 16.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-3 {
    position: relative;
    right: 25%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-3 {
    position: relative;
    left: 25%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-4 {
    position: relative;
    right: 33.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-4 {
    position: relative;
    left: 33.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-5 {
    position: relative;
    right: 41.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-5 {
    position: relative;
    left: 41.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-6 {
    position: relative;
    right: 50%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-6 {
    position: relative;
    left: 50%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-7 {
    position: relative;
    right: 58.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-7 {
    position: relative;
    left: 58.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-8 {
    position: relative;
    right: 66.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-8 {
    position: relative;
    left: 66.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-9 {
    position: relative;
    right: 75%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-9 {
    position: relative;
    left: 75%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-10 {
    position: relative;
    right: 83.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-10 {
    position: relative;
    left: 83.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-push-11 {
    position: relative;
    right: 91.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-pull-11 {
    position: relative;
    left: 91.66667%;
    right: auto; }

  /* line 157, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column,
  .columns,
  #stack-list li,
  #theme-list li > div,
  ul#docs-list > li {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: right; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-1 {
    position: relative;
    width: 8.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-2 {
    position: relative;
    width: 16.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-3 {
    position: relative;
    width: 25%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-4 {
    position: relative;
    width: 33.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-5 {
    position: relative;
    width: 41.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-6 {
    position: relative;
    width: 50%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-7 {
    position: relative;
    width: 58.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-8 {
    position: relative;
    width: 66.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-9 {
    position: relative;
    width: 75%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-10 {
    position: relative;
    width: 83.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-11 {
    position: relative;
    width: 91.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-12 {
    position: relative;
    width: 100%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-0 {
    position: relative;
    margin-right: 0%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-1 {
    position: relative;
    margin-right: 8.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-2 {
    position: relative;
    margin-right: 16.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-3 {
    position: relative;
    margin-right: 25%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-4 {
    position: relative;
    margin-right: 33.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-5 {
    position: relative;
    margin-right: 41.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-6 {
    position: relative;
    margin-right: 50%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-7 {
    position: relative;
    margin-right: 58.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-8 {
    position: relative;
    margin-right: 66.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-9 {
    position: relative;
    margin-right: 75%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xlarge-offset-10 {
    position: relative;
    margin-right: 83.33333%; }

  /* line 168, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"]:last-child {
    float: left; }

  /* line 169, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"].end {
    float: right; }

  /* line 172, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.xlarge-centered,
  .columns.xlarge-centered,
  #stack-list li.xlarge-centered,
  #theme-list li > div.xlarge-centered,
  ul#docs-list > li.xlarge-centered {
    position: relative;
    margin-right: auto;
    margin-left: auto;
    float: none !important; }

  /* line 175, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.xlarge-uncentered,
  .columns.xlarge-uncentered,
  #stack-list li.xlarge-uncentered,
  #theme-list li > div.xlarge-uncentered,
  ul#docs-list > li.xlarge-uncentered {
    margin-right: 0;
    margin-left: 0;
    float: right !important; }

  /* line 182, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.xlarge-uncentered.opposite,
  .columns.xlarge-uncentered.opposite,
  #stack-list li.xlarge-uncentered.opposite,
  #theme-list li > div.xlarge-uncentered.opposite,
  ul#docs-list > li.xlarge-uncentered.opposite {
    float: left !important; } }
@media only screen and (min-width: 120.063em) {
  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-1 {
    position: relative;
    right: 8.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-1 {
    position: relative;
    left: 8.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-2 {
    position: relative;
    right: 16.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-2 {
    position: relative;
    left: 16.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-3 {
    position: relative;
    right: 25%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-3 {
    position: relative;
    left: 25%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-4 {
    position: relative;
    right: 33.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-4 {
    position: relative;
    left: 33.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-5 {
    position: relative;
    right: 41.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-5 {
    position: relative;
    left: 41.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-6 {
    position: relative;
    right: 50%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-6 {
    position: relative;
    left: 50%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-7 {
    position: relative;
    right: 58.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-7 {
    position: relative;
    left: 58.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-8 {
    position: relative;
    right: 66.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-8 {
    position: relative;
    left: 66.66667%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-9 {
    position: relative;
    right: 75%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-9 {
    position: relative;
    left: 75%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-10 {
    position: relative;
    right: 83.33333%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-10 {
    position: relative;
    left: 83.33333%;
    right: auto; }

  /* line 148, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-push-11 {
    position: relative;
    right: 91.66667%;
    left: auto; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-pull-11 {
    position: relative;
    left: 91.66667%;
    right: auto; }

  /* line 157, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column,
  .columns,
  #stack-list li,
  #theme-list li > div,
  ul#docs-list > li {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: right; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-1 {
    position: relative;
    width: 8.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-2 {
    position: relative;
    width: 16.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-3 {
    position: relative;
    width: 25%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-4 {
    position: relative;
    width: 33.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-5 {
    position: relative;
    width: 41.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-6 {
    position: relative;
    width: 50%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-7 {
    position: relative;
    width: 58.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-8 {
    position: relative;
    width: 66.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-9 {
    position: relative;
    width: 75%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-10 {
    position: relative;
    width: 83.33333%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-11 {
    position: relative;
    width: 91.66667%; }

  /* line 161, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-12 {
    position: relative;
    width: 100%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-0 {
    position: relative;
    margin-right: 0%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-1 {
    position: relative;
    margin-right: 8.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-2 {
    position: relative;
    margin-right: 16.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-3 {
    position: relative;
    margin-right: 25%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-4 {
    position: relative;
    margin-right: 33.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-5 {
    position: relative;
    margin-right: 41.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-6 {
    position: relative;
    margin-right: 50%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-7 {
    position: relative;
    margin-right: 58.33333%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-8 {
    position: relative;
    margin-right: 66.66667%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-9 {
    position: relative;
    margin-right: 75%; }

  /* line 165, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .xxlarge-offset-10 {
    position: relative;
    margin-right: 83.33333%; }

  /* line 168, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"]:last-child {
    float: left; }

  /* line 169, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  [class*="column"] + [class*="column"].end {
    float: right; }

  /* line 172, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.xxlarge-centered,
  .columns.xxlarge-centered,
  #stack-list li.xxlarge-centered,
  #theme-list li > div.xxlarge-centered,
  ul#docs-list > li.xxlarge-centered {
    position: relative;
    margin-right: auto;
    margin-left: auto;
    float: none !important; }

  /* line 175, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.xxlarge-uncentered,
  .columns.xxlarge-uncentered,
  #stack-list li.xxlarge-uncentered,
  #theme-list li > div.xxlarge-uncentered,
  ul#docs-list > li.xxlarge-uncentered {
    margin-right: 0;
    margin-left: 0;
    float: right !important; }

  /* line 182, /app/bower_components/foundation/scss/foundation/components/_grid.scss */
  .column.xxlarge-uncentered.opposite,
  .columns.xxlarge-uncentered.opposite,
  #stack-list li.xxlarge-uncentered.opposite,
  #theme-list li > div.xxlarge-uncentered.opposite,
  ul#docs-list > li.xxlarge-uncentered.opposite {
    float: left !important; } }
/* line 23, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
.accordion {
  *zoom: 1;
  margin-bottom: 0; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .accordion:before, .accordion:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .accordion:after {
    clear: both; }
  /* line 25, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
  .accordion dd {
    display: block;
    margin-bottom: 0 !important; }
    /* line 28, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
    .accordion dd.active a {
      background: #e7e7e7; }
    /* line 29, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
    .accordion dd > a {
      background: #efefef;
      color: #222222;
      padding: 1rem;
      display: block;
      font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
      font-size: 1rem; }
      /* line 36, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
      .accordion dd > a:hover {
        background: #e2e2e2; }
  /* line 39, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
  .accordion .content, .accordion ul#downloads > li .notes, ul#downloads > li .accordion .notes, .accordion ul#downloads > li table ul[id$="-download"], ul#downloads > li table .accordion ul[id$="-download"], .accordion #download > div ul[id$="-download"], #download > div .accordion ul[id$="-download"] {
    display: none;
    padding: 0.9375rem; }
    /* line 42, /app/bower_components/foundation/scss/foundation/components/_accordion.scss */
    .accordion .content.active, .accordion ul#downloads > li .active.notes, ul#downloads > li .accordion .active.notes, .accordion ul#downloads > li table ul.active[id$="-download"], ul#downloads > li table .accordion ul.active[id$="-download"], .accordion #download > div ul.active[id$="-download"], #download > div .accordion ul.active[id$="-download"] {
      display: block;
      background: white; }

/* line 102, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
.alert-box {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-weight: normal;
  margin-bottom: 1.25rem;
  position: relative;
  padding: 0.875rem 1.5rem 0.875rem 0.875rem;
  font-size: 0.8125rem;
  background-color: #008cba;
  border-color: #0079a1;
  color: white; }
  /* line 105, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box .close {
    font-size: 1.375rem;
    padding: 9px 6px 4px;
    line-height: 0;
    position: absolute;
    top: 50%;
    margin-top: -0.6875rem;
    left: 0.25rem;
    color: #333333;
    opacity: 0.3; }
    /* line 87, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
    .alert-box .close:hover, .alert-box .close:focus {
      opacity: 0.5; }
  /* line 107, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }
  /* line 108, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.round {
    -webkit-border-radius: 1000px;
    border-radius: 1000px; }
  /* line 110, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.success {
    background-color: #5e8949;
    border-color: #537840;
    color: white; }
  /* line 111, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.alert {
    background-color: #f04124;
    border-color: #ea2f10;
    color: white; }
  /* line 112, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.secondary {
    background-color: #e7e7e7;
    border-color: #dadada;
    color: #4e4e4e; }
  /* line 113, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.warning {
    background-color: #f08a24;
    border-color: #ea7d10;
    color: white; }
  /* line 114, /app/bower_components/foundation/scss/foundation/components/_alert-boxes.scss */
  .alert-box.info {
    background-color: #a0d3e8;
    border-color: #8bc9e3;
    color: #4e4e4e; }

/* line 69, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
[class*="block-grid-"] {
  display: block;
  padding: 0;
  margin: 0 -0.625rem;
  *zoom: 1; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  [class*="block-grid-"]:before, [class*="block-grid-"]:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  [class*="block-grid-"]:after {
    clear: both; }
  /* line 35, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  [class*="block-grid-"] > li {
    display: inline;
    height: auto;
    float: right;
    padding: 0 0.625rem 1.25rem; }

@media only screen {
  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-1 > li {
    width: 100%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-1 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-1 > li:nth-of-type(1n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-2 > li {
    width: 50%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-2 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-2 > li:nth-of-type(2n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-3 > li {
    width: 33.33333%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-3 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-3 > li:nth-of-type(3n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-4 > li {
    width: 25%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-4 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-4 > li:nth-of-type(4n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-5 > li {
    width: 20%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-5 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-5 > li:nth-of-type(5n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-6 > li {
    width: 16.66667%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-6 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-6 > li:nth-of-type(6n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-7 > li {
    width: 14.28571%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-7 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-7 > li:nth-of-type(7n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-8 > li {
    width: 12.5%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-8 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-8 > li:nth-of-type(8n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-9 > li {
    width: 11.11111%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-9 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-9 > li:nth-of-type(9n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-10 > li {
    width: 10%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-10 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-10 > li:nth-of-type(10n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-11 > li {
    width: 9.09091%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-11 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-11 > li:nth-of-type(11n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .small-block-grid-12 > li {
    width: 8.33333%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-12 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .small-block-grid-12 > li:nth-of-type(12n+1) {
      clear: both; } }
@media only screen and (min-width: 40.063em) {
  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-1 > li {
    width: 100%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-1 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-1 > li:nth-of-type(1n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-2 > li {
    width: 50%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-2 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-2 > li:nth-of-type(2n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-3 > li {
    width: 33.33333%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-3 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-3 > li:nth-of-type(3n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-4 > li {
    width: 25%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-4 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-4 > li:nth-of-type(4n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-5 > li {
    width: 20%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-5 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-5 > li:nth-of-type(5n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-6 > li {
    width: 16.66667%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-6 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-6 > li:nth-of-type(6n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-7 > li {
    width: 14.28571%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-7 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-7 > li:nth-of-type(7n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-8 > li {
    width: 12.5%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-8 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-8 > li:nth-of-type(8n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-9 > li {
    width: 11.11111%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-9 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-9 > li:nth-of-type(9n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-10 > li {
    width: 10%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-10 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-10 > li:nth-of-type(10n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-11 > li {
    width: 9.09091%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-11 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-11 > li:nth-of-type(11n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .medium-block-grid-12 > li {
    width: 8.33333%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-12 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .medium-block-grid-12 > li:nth-of-type(12n+1) {
      clear: both; } }
@media only screen and (min-width: 64.063em) {
  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-1 > li {
    width: 100%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-1 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-1 > li:nth-of-type(1n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-2 > li {
    width: 50%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-2 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-2 > li:nth-of-type(2n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-3 > li {
    width: 33.33333%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-3 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-3 > li:nth-of-type(3n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-4 > li {
    width: 25%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-4 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-4 > li:nth-of-type(4n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-5 > li {
    width: 20%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-5 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-5 > li:nth-of-type(5n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-6 > li {
    width: 16.66667%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-6 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-6 > li:nth-of-type(6n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-7 > li {
    width: 14.28571%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-7 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-7 > li:nth-of-type(7n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-8 > li {
    width: 12.5%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-8 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-8 > li:nth-of-type(8n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-9 > li {
    width: 11.11111%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-9 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-9 > li:nth-of-type(9n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-10 > li {
    width: 10%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-10 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-10 > li:nth-of-type(10n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-11 > li {
    width: 9.09091%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-11 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-11 > li:nth-of-type(11n+1) {
      clear: both; }

  /* line 44, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
  .large-block-grid-12 > li {
    width: 8.33333%;
    padding: 0 0.625rem 1.25rem; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-12 > li:nth-of-type(n) {
      clear: none; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_block-grid.scss */
    .large-block-grid-12 > li:nth-of-type(12n+1) {
      clear: both; } }
/* line 114, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
.breadcrumbs {
  display: block;
  padding: 0.5625rem 0.875rem 0.5625rem;
  overflow: hidden;
  margin-right: 0;
  list-style: none;
  border-style: solid;
  border-width: 1px;
  background-color: #f4f4f4;
  border-color: #dadada;
  -webkit-border-radius: 3px;
  border-radius: 3px; }
  /* line 118, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
  .breadcrumbs > * {
    margin: 0;
    float: right;
    font-size: 0.6875rem;
    text-transform: uppercase; }
    /* line 62, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
    .breadcrumbs > *:hover a, .breadcrumbs > *:focus a {
      text-decoration: underline; }
    /* line 65, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
    .breadcrumbs > * a,
    .breadcrumbs > * span {
      text-transform: uppercase;
      color: #008cba; }
    /* line 71, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
    .breadcrumbs > *.current {
      cursor: default;
      color: #333333; }
      /* line 74, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
      .breadcrumbs > *.current a {
        cursor: default;
        color: #333333; }
      /* line 80, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
      .breadcrumbs > *.current:hover, .breadcrumbs > *.current:hover a, .breadcrumbs > *.current:focus, .breadcrumbs > *.current:focus a {
        text-decoration: none; }
    /* line 84, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
    .breadcrumbs > *.unavailable {
      color: #999999; }
      /* line 86, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
      .breadcrumbs > *.unavailable a {
        color: #999999; }
      /* line 91, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
      .breadcrumbs > *.unavailable:hover, .breadcrumbs > *.unavailable:hover a, .breadcrumbs > *.unavailable:focus,
      .breadcrumbs > *.unavailable a:focus {
        text-decoration: none;
        color: #999999;
        cursor: default; }
    /* line 98, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
    .breadcrumbs > *:before {
      content: "/";
      color: #aaaaaa;
      margin: 0 0.75rem;
      position: relative;
      top: 1px; }
    /* line 106, /app/bower_components/foundation/scss/foundation/components/_breadcrumbs.scss */
    .breadcrumbs > *:first-child:before {
      content: " ";
      margin: 0; }

/* line 197, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
button, .button {
  cursor: pointer;
  font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-decoration: none;
  text-align: center;
  display: inline-block;
  padding-top: 1rem;
  padding-left: 2rem;
  padding-bottom: 1.0625rem;
  padding-right: 2rem;
  font-size: 1rem;
  /*     @else                            { font-size: $padding - rem-calc(2); } */
  background-color: #008cba;
  border-color: #0079a1;
  color: white;
  -webkit-transition: background-color 300ms ease-out;
  -moz-transition: background-color 300ms ease-out;
  transition: background-color 300ms ease-out;
  padding-top: 1.0625rem;
  padding-bottom: 1rem;
  -webkit-appearance: none;
  border: none;
  font-weight: normal !important; }
  /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button:hover, button:focus, .button:hover, .button:focus {
    background-color: #0079a1; }
  /* line 153, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button:hover, button:focus, .button:hover, .button:focus {
    color: white; }
  /* line 205, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.secondary, .button.secondary {
    background-color: #e7e7e7;
    border-color: #dadada;
    color: #333333; }
    /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.secondary:hover, button.secondary:focus, .button.secondary:hover, .button.secondary:focus {
      background-color: #dadada; }
    /* line 148, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.secondary:hover, button.secondary:focus, .button.secondary:hover, .button.secondary:focus {
      color: #333333; }
  /* line 206, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.success, .button.success {
    background-color: #5e8949;
    border-color: #537840;
    color: white; }
    /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.success:hover, button.success:focus, .button.success:hover, .button.success:focus {
      background-color: #537840; }
    /* line 153, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.success:hover, button.success:focus, .button.success:hover, .button.success:focus {
      color: white; }
  /* line 207, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.alert, .button.alert {
    background-color: #f04124;
    border-color: #ea2f10;
    color: white; }
    /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.alert:hover, button.alert:focus, .button.alert:hover, .button.alert:focus {
      background-color: #ea2f10; }
    /* line 153, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.alert:hover, button.alert:focus, .button.alert:hover, .button.alert:focus {
      color: white; }
  /* line 209, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.large, .button.large {
    padding-top: 1.125rem;
    padding-left: 2.25rem;
    padding-bottom: 1.1875rem;
    padding-right: 2.25rem;
    font-size: 1.25rem;
    /*     @else                            { font-size: $padding - rem-calc(2); } */ }
  /* line 210, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.small, .button.small {
    padding-top: 0.875rem;
    padding-left: 1.75rem;
    padding-bottom: 0.9375rem;
    padding-right: 1.75rem;
    font-size: 0.8125rem;
    /*     @else                            { font-size: $padding - rem-calc(2); } */ }
  /* line 211, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.tiny, .button.tiny {
    padding-top: 0.625rem;
    padding-left: 1.25rem;
    padding-bottom: 0.6875rem;
    padding-right: 1.25rem;
    font-size: 0.6875rem;
    /*     @else                            { font-size: $padding - rem-calc(2); } */ }
  /* line 212, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.expand, .button.expand {
    padding-right: 0;
    padding-left: 0;
    width: 100%; }
  /* line 214, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.left-align, .button.left-align {
    text-align: left;
    text-indent: 0.75rem; }
  /* line 215, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.right-align, .button.right-align {
    text-align: right;
    padding-right: 0.75rem; }
  /* line 217, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.radius, .button.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }
  /* line 218, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.round, .button.round {
    -webkit-border-radius: 1000px;
    border-radius: 1000px; }
  /* line 220, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button.disabled, button[disabled], .button.disabled, .button[disabled] {
    background-color: #008cba;
    border-color: #0079a1;
    color: white;
    cursor: default;
    opacity: 0.7;
    -webkit-box-shadow: none;
    box-shadow: none; }
    /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
      background-color: #0079a1; }
    /* line 153, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
      color: white; }
    /* line 166, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
      background-color: #008cba; }
    /* line 221, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.disabled.secondary, button[disabled].secondary, .button.disabled.secondary, .button[disabled].secondary {
      background-color: #e7e7e7;
      border-color: #dadada;
      color: #333333;
      cursor: default;
      opacity: 0.7;
      -webkit-box-shadow: none;
      box-shadow: none; }
      /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.secondary:hover, button.disabled.secondary:focus, button[disabled].secondary:hover, button[disabled].secondary:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
        background-color: #dadada; }
      /* line 148, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.secondary:hover, button.disabled.secondary:focus, button[disabled].secondary:hover, button[disabled].secondary:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
        color: #333333; }
      /* line 166, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.secondary:hover, button.disabled.secondary:focus, button[disabled].secondary:hover, button[disabled].secondary:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
        background-color: #e7e7e7; }
    /* line 222, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.disabled.success, button[disabled].success, .button.disabled.success, .button[disabled].success {
      background-color: #5e8949;
      border-color: #537840;
      color: white;
      cursor: default;
      opacity: 0.7;
      -webkit-box-shadow: none;
      box-shadow: none; }
      /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.success:hover, button.disabled.success:focus, button[disabled].success:hover, button[disabled].success:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button[disabled].success:hover, .button[disabled].success:focus {
        background-color: #537840; }
      /* line 153, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.success:hover, button.disabled.success:focus, button[disabled].success:hover, button[disabled].success:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button[disabled].success:hover, .button[disabled].success:focus {
        color: white; }
      /* line 166, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.success:hover, button.disabled.success:focus, button[disabled].success:hover, button[disabled].success:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button[disabled].success:hover, .button[disabled].success:focus {
        background-color: #5e8949; }
    /* line 223, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
    button.disabled.alert, button[disabled].alert, .button.disabled.alert, .button[disabled].alert {
      background-color: #f04124;
      border-color: #ea2f10;
      color: white;
      cursor: default;
      opacity: 0.7;
      -webkit-box-shadow: none;
      box-shadow: none; }
      /* line 142, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.alert:hover, button.disabled.alert:focus, button[disabled].alert:hover, button[disabled].alert:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button[disabled].alert:hover, .button[disabled].alert:focus {
        background-color: #ea2f10; }
      /* line 153, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.alert:hover, button.disabled.alert:focus, button[disabled].alert:hover, button[disabled].alert:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button[disabled].alert:hover, .button[disabled].alert:focus {
        color: white; }
      /* line 166, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
      button.disabled.alert:hover, button.disabled.alert:focus, button[disabled].alert:hover, button[disabled].alert:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button[disabled].alert:hover, .button[disabled].alert:focus {
        background-color: #f04124; }

@media only screen and (min-width: 40.063em) {
  /* line 228, /app/bower_components/foundation/scss/foundation/components/_buttons.scss */
  button, .button {
    display: inline-block; } }
/* line 80, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
.button-group {
  list-style: none;
  margin: 0;
  *zoom: 1; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .button-group:before, .button-group:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .button-group:after {
    clear: both; }
  /* line 82, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group > * {
    margin: 0;
    float: right; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group > * > button, .button-group > * .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 45, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group > *:first-child {
      margin-right: 0; }
  /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.radius > * > button, .button-group.radius > * .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5); }
  /* line 63, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.radius > *:first-child, .button-group.radius > *:first-child > a, .button-group.radius > *:first-child > button, .button-group.radius > *:first-child > .button {
    -moz-border-radius-topright: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-top-right-radius: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px; }
  /* line 67, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.radius > *:last-child, .button-group.radius > *:last-child > a, .button-group.radius > *:last-child > button, .button-group.radius > *:last-child > .button {
    -moz-border-radius-bottomleft: 3px;
    -moz-border-radius-topleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px; }
  /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.round > * > button, .button-group.round > * .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5); }
  /* line 63, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.round > *:first-child, .button-group.round > *:first-child > a, .button-group.round > *:first-child > button, .button-group.round > *:first-child > .button {
    -moz-border-radius-topright: 1000px;
    -moz-border-radius-bottomright: 1000px;
    -webkit-border-top-right-radius: 1000px;
    -webkit-border-bottom-right-radius: 1000px;
    border-top-right-radius: 1000px;
    border-bottom-right-radius: 1000px; }
  /* line 67, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.round > *:last-child, .button-group.round > *:last-child > a, .button-group.round > *:last-child > button, .button-group.round > *:last-child > .button {
    -moz-border-radius-bottomleft: 1000px;
    -moz-border-radius-topleft: 1000px;
    -webkit-border-bottom-left-radius: 1000px;
    -webkit-border-top-left-radius: 1000px;
    border-bottom-left-radius: 1000px;
    border-top-left-radius: 1000px; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-2 li {
    width: 50%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-2 li > button, .button-group.even-2 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-2 li button, .button-group.even-2 li .button {
      width: 100%; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-3 li {
    width: 33.33333%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-3 li > button, .button-group.even-3 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-3 li button, .button-group.even-3 li .button {
      width: 100%; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-4 li {
    width: 25%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-4 li > button, .button-group.even-4 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-4 li button, .button-group.even-4 li .button {
      width: 100%; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-5 li {
    width: 20%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-5 li > button, .button-group.even-5 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-5 li button, .button-group.even-5 li .button {
      width: 100%; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-6 li {
    width: 16.66667%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-6 li > button, .button-group.even-6 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-6 li button, .button-group.even-6 li .button {
      width: 100%; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-7 li {
    width: 14.28571%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-7 li > button, .button-group.even-7 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-7 li button, .button-group.even-7 li .button {
      width: 100%; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-group.even-8 li {
    width: 12.5%; }
    /* line 35, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-8 li > button, .button-group.even-8 li .button {
      border-left: 1px solid;
      border-color: rgba(255, 255, 255, 0.5); }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-group.even-8 li button, .button-group.even-8 li .button {
      width: 100%; }

/* line 92, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
.button-bar {
  *zoom: 1; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .button-bar:before, .button-bar:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .button-bar:after {
    clear: both; }
  /* line 94, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
  .button-bar .button-group {
    float: right;
    margin-left: 0.625rem; }
    /* line 28, /app/bower_components/foundation/scss/foundation/components/_button-groups.scss */
    .button-bar .button-group div {
      overflow: hidden; }

/* Clearing Styles */
/* line 40, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
[data-clearing] {
  *zoom: 1;
  margin-bottom: 0;
  margin-right: 0;
  list-style: none; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  [data-clearing]:before, [data-clearing]:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  [data-clearing]:after {
    clear: both; }
  /* line 46, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  [data-clearing] li {
    float: right;
    margin-left: 10px; }

/* line 52, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.clearing-blackout {
  background: #333333;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: 998; }
  /* line 61, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-blackout .clearing-close {
    display: block; }

/* line 64, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.clearing-container {
  position: relative;
  z-index: 998;
  height: 100%;
  overflow: hidden;
  margin: 0; }

/* line 72, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.visible-img {
  height: 95%;
  position: relative; }
  /* line 76, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .visible-img img {
    position: absolute;
    right: 50%;
    top: 50%;
    margin-right: -50%;
    max-height: 100%;
    max-width: 100%; }

/* line 86, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.clearing-caption {
  color: #cccccc;
  font-size: 0.875em;
  line-height: 1.3;
  margin-bottom: 0;
  text-align: center;
  bottom: 0;
  background: #333333;
  width: 100%;
  padding: 10px 30px 20px;
  position: absolute;
  right: 0; }

/* line 100, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.clearing-close {
  z-index: 999;
  padding-right: 20px;
  padding-top: 10px;
  font-size: 30px;
  line-height: 1;
  color: #cccccc;
  display: none; }
  /* line 110, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-close:hover, .clearing-close:focus {
    color: #ccc; }

/* line 113, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.clearing-assembled .clearing-container {
  height: 100%; }
  /* line 114, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul {
    display: none; }

/* line 118, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
.clearing-feature li {
  display: none; }
  /* line 120, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-feature li.clearing-featured-img {
    display: block; }

@media only screen and (min-width: 40.063em) {
  /* line 128, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-main-prev,
  .clearing-main-next {
    position: absolute;
    height: 100%;
    width: 40px;
    top: 0; }
    /* line 133, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
    .clearing-main-prev > span,
    .clearing-main-next > span {
      position: absolute;
      top: 50%;
      display: block;
      width: 0;
      height: 0;
      border: solid 12px; }
      /* line 140, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
      .clearing-main-prev > span:hover,
      .clearing-main-next > span:hover {
        opacity: 0.8; }

  /* line 143, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-main-prev {
    right: 0; }
    /* line 145, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
    .clearing-main-prev > span {
      right: 5px;
      border-color: transparent;
      border-left-color: #cccccc; }

  /* line 151, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-main-next {
    left: 0; }
    /* line 153, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
    .clearing-main-next > span {
      border-color: transparent;
      border-right-color: #cccccc; }

  /* line 160, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-main-prev.disabled,
  .clearing-main-next.disabled {
    opacity: 0.3; }

  /* line 164, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel {
    background: rgba(51, 51, 51, 0.8);
    height: 120px;
    margin-top: 10px;
    text-align: center; }
    /* line 170, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
    .clearing-assembled .clearing-container .carousel > ul {
      display: inline-block;
      z-index: 999;
      height: 100%;
      position: relative;
      float: none; }
      /* line 177, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
      .clearing-assembled .clearing-container .carousel > ul li {
        display: block;
        width: 120px;
        min-height: inherit;
        float: right;
        overflow: hidden;
        margin-left: 0;
        padding: 0;
        position: relative;
        cursor: pointer;
        opacity: 0.4; }
        /* line 190, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
        .clearing-assembled .clearing-container .carousel > ul li.fix-height img {
          height: 100%;
          max-width: none; }
        /* line 196, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
        .clearing-assembled .clearing-container .carousel > ul li a.th {
          border: none;
          -webkit-box-shadow: none;
          box-shadow: none;
          display: block; }
        /* line 205, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
        .clearing-assembled .clearing-container .carousel > ul li img {
          cursor: pointer !important;
          width: 100% !important; }
        /* line 210, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
        .clearing-assembled .clearing-container .carousel > ul li.visible {
          opacity: 1; }
        /* line 211, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
        .clearing-assembled .clearing-container .carousel > ul li:hover {
          opacity: 0.8; }
  /* line 216, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .visible-img {
    background: #333333;
    overflow: hidden;
    height: 85%; }

  /* line 223, /app/bower_components/foundation/scss/foundation/components/_clearing.scss */
  .clearing-close {
    position: absolute;
    top: 10px;
    left: 20px;
    padding-right: 0;
    padding-top: 0; } }
@media only screen and (max-width: 40em) {
  /* line 135, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown, ul#downloads > li .notes, ul#downloads > li table ul[id$="-download"], #download > div ul[id$="-download"] {
    max-width: 100%;
    right: 0; } }
/* Foundation Dropdowns */
/* line 142, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
.f-dropdown, ul#downloads > li .notes, ul#downloads > li table ul[id$="-download"], #download > div ul[id$="-download"] {
  position: absolute;
  top: -9999px;
  list-style: none;
  margin-right: 0;
  width: 100%;
  max-height: none;
  height: auto;
  background: white;
  border: solid 1px #cccccc;
  font-size: 16px;
  z-index: 99;
  margin-top: 2px;
  max-width: 200px; }
  /* line 54, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown > *:first-child, ul#downloads > li .notes > *:first-child, ul#downloads > li table ul[id$="-download"] > *:first-child, #download > div ul[id$="-download"] > *:first-child {
    margin-top: 0; }
  /* line 55, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown > *:last-child, ul#downloads > li .notes > *:last-child, ul#downloads > li table ul[id$="-download"] > *:last-child, #download > div ul[id$="-download"] > *:last-child {
    margin-bottom: 0; }
  /* line 80, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown:before, ul#downloads > li .notes:before, ul#downloads > li table ul[id$="-download"]:before, #download > div ul[id$="-download"]:before {
    content: "";
    display: block;
    width: 0;
    height: 0;
    border: inset 6px;
    border-color: transparent transparent white transparent;
    border-bottom-style: solid;
    position: absolute;
    top: -12px;
    right: 10px;
    z-index: 99; }
  /* line 87, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown:after, ul#downloads > li .notes:after, ul#downloads > li table ul[id$="-download"]:after, #download > div ul[id$="-download"]:after {
    content: "";
    display: block;
    width: 0;
    height: 0;
    border: inset 7px;
    border-color: transparent transparent #cccccc transparent;
    border-bottom-style: solid;
    position: absolute;
    top: -14px;
    right: 9px;
    z-index: 98; }
  /* line 95, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.right:before, ul#downloads > li .right.notes:before, ul#downloads > li body[class$="about"] dl dd img.notes:before, body[class$="about"] dl dd ul#downloads > li img.notes:before, ul#downloads > li table ul.right[id$="-download"]:before, #download > div ul.right[id$="-download"]:before, body[class$="about"] dl dd img.f-dropdown:before {
    left: auto;
    right: 10px; }
  /* line 99, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.right:after, ul#downloads > li .right.notes:after, ul#downloads > li body[class$="about"] dl dd img.notes:after, body[class$="about"] dl dd ul#downloads > li img.notes:after, ul#downloads > li table ul.right[id$="-download"]:after, #download > div ul.right[id$="-download"]:after, body[class$="about"] dl dd img.f-dropdown:after {
    left: auto;
    right: 9px; }
  /* line 146, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown li, ul#downloads > li .notes li, ul#downloads > li table ul[id$="-download"] li, #download > div ul[id$="-download"] li {
    font-size: 0.875rem;
    cursor: pointer;
    line-height: 1.125rem;
    margin: 0; }
    /* line 122, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
    .f-dropdown li:hover, ul#downloads > li .notes li:hover, ul#downloads > li table ul[id$="-download"] li:hover, #download > div ul[id$="-download"] li:hover, .f-dropdown li:focus, ul#downloads > li .notes li:focus, ul#downloads > li table ul[id$="-download"] li:focus, #download > div ul[id$="-download"] li:focus {
      background: #eeeeee; }
    /* line 124, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
    .f-dropdown li a, ul#downloads > li .notes li a, ul#downloads > li table ul[id$="-download"] li a, #download > div ul[id$="-download"] li a {
      display: block;
      padding: 0.5rem;
      color: #555555; }
  /* line 149, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.content, ul#downloads > li .notes, ul#downloads > li table ul[id$="-download"], #download > div ul[id$="-download"] {
    position: absolute;
    top: -9999px;
    list-style: none;
    margin-right: 0;
    padding: 1.25rem;
    width: 100%;
    height: auto;
    max-height: none;
    background: white;
    border: solid 1px #cccccc;
    font-size: 16px;
    z-index: 99;
    max-width: 200px; }
    /* line 54, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
    .f-dropdown.content > *:first-child, ul#downloads > li .notes > *:first-child, ul#downloads > li table ul[id$="-download"] > *:first-child, #download > div ul[id$="-download"] > *:first-child {
      margin-top: 0; }
    /* line 55, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
    .f-dropdown.content > *:last-child, ul#downloads > li .notes > *:last-child, ul#downloads > li table ul[id$="-download"] > *:last-child, #download > div ul[id$="-download"] > *:last-child {
      margin-bottom: 0; }
  /* line 152, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.tiny, ul#downloads > li .tiny.notes, ul#downloads > li table ul.tiny[id$="-download"], #download > div ul.tiny[id$="-download"] {
    max-width: 200px; }
  /* line 153, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.small, ul#downloads > li .small.notes, ul#downloads > li table ul.small[id$="-download"], #download > div ul.small[id$="-download"] {
    max-width: 300px; }
  /* line 154, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.medium, ul#downloads > li .medium.notes, ul#downloads > li table ul.medium[id$="-download"], #download > div ul.medium[id$="-download"] {
    max-width: 500px; }
  /* line 155, /app/bower_components/foundation/scss/foundation/components/_dropdown.scss */
  .f-dropdown.large, ul#downloads > li .large.notes, ul#downloads > li table ul.large[id$="-download"], #download > div ul.large[id$="-download"] {
    max-width: 800px; }

/* line 118, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
.dropdown.button {
  position: relative;
  padding-left: 3.5625rem; }
  /* line 58, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button:before {
    position: absolute;
    content: "";
    width: 0;
    height: 0;
    display: block;
    border-style: solid;
    border-color: white transparent transparent transparent;
    top: 50%; }
  /* line 93, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button:before {
    border-width: 0.375rem;
    left: 1.40625rem;
    margin-top: -0.15625rem; }
  /* line 112, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button:before {
    border-color: white transparent transparent transparent; }
  /* line 119, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button.tiny {
    padding-left: 2.625rem; }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
    .dropdown.button.tiny:before {
      border-width: 0.375rem;
      left: 1.125rem;
      margin-top: -0.125rem; }
    /* line 112, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
    .dropdown.button.tiny:before {
      border-color: white transparent transparent transparent; }
  /* line 120, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button.small {
    padding-left: 3.0625rem; }
    /* line 83, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
    .dropdown.button.small:before {
      border-width: 0.4375rem;
      left: 1.3125rem;
      margin-top: -0.15625rem; }
    /* line 112, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
    .dropdown.button.small:before {
      border-color: white transparent transparent transparent; }
  /* line 121, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button.large {
    padding-left: 3.625rem; }
    /* line 103, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
    .dropdown.button.large:before {
      border-width: 0.3125rem;
      left: 1.71875rem;
      margin-top: -0.15625rem; }
    /* line 112, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
    .dropdown.button.large:before {
      border-color: white transparent transparent transparent; }
  /* line 122, /app/bower_components/foundation/scss/foundation/components/_dropdown-buttons.scss */
  .dropdown.button.secondary:before {
    border-color: #333333 transparent transparent transparent; }

/* line 45, /app/bower_components/foundation/scss/foundation/components/_flex-video.scss */
.flex-video {
  position: relative;
  padding-top: 1.5625rem;
  padding-bottom: 67.5%;
  height: 0;
  margin-bottom: 1rem;
  overflow: hidden; }
  /* line 28, /app/bower_components/foundation/scss/foundation/components/_flex-video.scss */
  .flex-video.widescreen {
    padding-bottom: 57.25%; }
  /* line 29, /app/bower_components/foundation/scss/foundation/components/_flex-video.scss */
  .flex-video.vimeo {
    padding-top: 0; }
  /* line 34, /app/bower_components/foundation/scss/foundation/components/_flex-video.scss */
  .flex-video iframe,
  .flex-video object,
  .flex-video embed,
  .flex-video video {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%; }

/* Standard Forms */
/* line 300, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
form {
  margin: 0 0 1rem; }

/* Using forms within rows, we need to set some defaults */
/* line 75, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
form .row .row, form #stack-list .row, form #theme-list li .row, #theme-list form li .row, form ul#docs-list .row, form .row #stack-list, form #stack-list #stack-list, form #theme-list li #stack-list, #theme-list form li #stack-list, form ul#docs-list #stack-list, form .row #theme-list li, #theme-list form .row li, form #stack-list #theme-list li, #theme-list form #stack-list li, form #theme-list li li, #theme-list form li li, form ul#docs-list #theme-list li, #theme-list form ul#docs-list li, form .row ul#docs-list, form #stack-list ul#docs-list, form #theme-list li ul#docs-list, #theme-list form li ul#docs-list, form ul#docs-list ul#docs-list {
  margin: 0 -0.5rem; }
  /* line 78, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  form .row .row .column, form #stack-list .row .column, form #theme-list li .row .column, #theme-list form li .row .column, form ul#docs-list .row .column, form .row #stack-list .column, form #stack-list #stack-list .column, form #theme-list li #stack-list .column, #theme-list form li #stack-list .column, form ul#docs-list #stack-list .column, form .row #theme-list li .column, #theme-list form .row li .column, form #stack-list #theme-list li .column, #theme-list form #stack-list li .column, form #theme-list li li .column, #theme-list form li li .column, form ul#docs-list #theme-list li .column, #theme-list form ul#docs-list li .column, form .row ul#docs-list .column, form #stack-list ul#docs-list .column, form #theme-list li ul#docs-list .column, #theme-list form li ul#docs-list .column, form ul#docs-list ul#docs-list .column,
  form .row .row .columns,
  form #stack-list .row .columns,
  form #theme-list li .row .columns,
  #theme-list form li .row .columns,
  form ul#docs-list .row .columns,
  form .row #stack-list .columns,
  form #stack-list #stack-list .columns,
  form #theme-list li #stack-list .columns,
  #theme-list form li #stack-list .columns,
  form ul#docs-list #stack-list .columns,
  form .row #theme-list li .columns,
  #theme-list form .row li .columns,
  form #stack-list #theme-list li .columns,
  #theme-list form #stack-list li .columns,
  form #theme-list li li .columns,
  #theme-list form li li .columns,
  form ul#docs-list #theme-list li .columns,
  #theme-list form ul#docs-list li .columns,
  form .row ul#docs-list .columns,
  form #stack-list ul#docs-list .columns,
  form #theme-list li ul#docs-list .columns,
  #theme-list form li ul#docs-list .columns,
  form ul#docs-list ul#docs-list .columns,
  #stack-list form .row .row li,
  form #stack-list .row li,
  #stack-list form #theme-list li .row li,
  #stack-list #theme-list form li .row li,
  #stack-list form ul#docs-list .row li,
  form .row #stack-list li,
  form #stack-list #stack-list li,
  form #theme-list li #stack-list li,
  #theme-list form li #stack-list li,
  form ul#docs-list #stack-list li,
  #stack-list form .row #theme-list li li,
  #stack-list #theme-list form .row li li,
  form #stack-list #theme-list li li,
  #theme-list form #stack-list li li,
  #stack-list form #theme-list li li li,
  #stack-list #theme-list form li li li,
  #stack-list form ul#docs-list #theme-list li li,
  #stack-list #theme-list form ul#docs-list li li,
  #stack-list form .row ul#docs-list li,
  form #stack-list ul#docs-list li,
  #stack-list form #theme-list li ul#docs-list li,
  #stack-list #theme-list form li ul#docs-list li,
  #stack-list form ul#docs-list ul#docs-list li,
  form .row #theme-list li > div,
  #theme-list form .row li > div,
  form #stack-list #theme-list li > div,
  #theme-list form #stack-list li > div,
  form #theme-list li li > div,
  #theme-list form li li > div,
  form ul#docs-list #theme-list li > div,
  #theme-list form ul#docs-list li > div,
  form .row ul#docs-list > li,
  form #theme-list li ul#docs-list > li,
  #theme-list form li ul#docs-list > li,
  form ul#docs-list ul#docs-list > li {
    padding: 0 0.5rem; }
  /* line 81, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  form .row .row.collapse, form #stack-list .row.collapse, form #theme-list li .row.collapse, #theme-list form li .row.collapse, form ul#docs-list .row.collapse, form .row .collapse#stack-list, form #stack-list .collapse#stack-list, form #theme-list li .collapse#stack-list, #theme-list form li .collapse#stack-list, form ul#docs-list .collapse#stack-list, form .row #theme-list li.collapse, #theme-list form .row li.collapse, form #stack-list #theme-list li.collapse, #theme-list form #stack-list li.collapse, form #theme-list li li.collapse, #theme-list form li li.collapse, form ul#docs-list #theme-list li.collapse, #theme-list form ul#docs-list li.collapse, form .row ul.collapse#docs-list, form #stack-list ul.collapse#docs-list, form #theme-list li ul.collapse#docs-list, #theme-list form li ul.collapse#docs-list, form ul#docs-list ul.collapse#docs-list {
    margin: 0; }
    /* line 84, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
    form .row .row.collapse .column, form #stack-list .row.collapse .column, form #theme-list li .row.collapse .column, #theme-list form li .row.collapse .column, form ul#docs-list .row.collapse .column, form .row .collapse#stack-list .column, form #stack-list .collapse#stack-list .column, form #theme-list li .collapse#stack-list .column, #theme-list form li .collapse#stack-list .column, form ul#docs-list .collapse#stack-list .column, form .row #theme-list li.collapse .column, #theme-list form .row li.collapse .column, form #stack-list #theme-list li.collapse .column, #theme-list form #stack-list li.collapse .column, form #theme-list li li.collapse .column, #theme-list form li li.collapse .column, form ul#docs-list #theme-list li.collapse .column, #theme-list form ul#docs-list li.collapse .column, form .row ul.collapse#docs-list .column, form #stack-list ul.collapse#docs-list .column, form #theme-list li ul.collapse#docs-list .column, #theme-list form li ul.collapse#docs-list .column, form ul#docs-list ul.collapse#docs-list .column,
    form .row .row.collapse .columns,
    form #stack-list .row.collapse .columns,
    form #theme-list li .row.collapse .columns,
    #theme-list form li .row.collapse .columns,
    form ul#docs-list .row.collapse .columns,
    form .row .collapse#stack-list .columns,
    form #stack-list .collapse#stack-list .columns,
    form #theme-list li .collapse#stack-list .columns,
    #theme-list form li .collapse#stack-list .columns,
    form ul#docs-list .collapse#stack-list .columns,
    form .row #theme-list li.collapse .columns,
    #theme-list form .row li.collapse .columns,
    form #stack-list #theme-list li.collapse .columns,
    #theme-list form #stack-list li.collapse .columns,
    form #theme-list li li.collapse .columns,
    #theme-list form li li.collapse .columns,
    form ul#docs-list #theme-list li.collapse .columns,
    #theme-list form ul#docs-list li.collapse .columns,
    form .row ul.collapse#docs-list .columns,
    form #stack-list ul.collapse#docs-list .columns,
    form #theme-list li ul.collapse#docs-list .columns,
    #theme-list form li ul.collapse#docs-list .columns,
    form ul#docs-list ul.collapse#docs-list .columns,
    form .row .row.collapse #stack-list li,
    #stack-list form .row .row.collapse li,
    form #stack-list .row.collapse li,
    form #theme-list li .row.collapse #stack-list li,
    #stack-list form #theme-list li .row.collapse li,
    #theme-list form li .row.collapse #stack-list li,
    #stack-list #theme-list form li .row.collapse li,
    form ul#docs-list .row.collapse #stack-list li,
    #stack-list form ul#docs-list .row.collapse li,
    form .row .collapse#stack-list li,
    form .collapse#stack-list .collapse#stack-list li,
    form #theme-list li .collapse#stack-list li,
    #theme-list form li .collapse#stack-list li,
    form ul#docs-list .collapse#stack-list li,
    form .row #theme-list li.collapse #stack-list li,
    #stack-list form .row #theme-list li.collapse li,
    #theme-list form .row li.collapse #stack-list li,
    #stack-list #theme-list form .row li.collapse li,
    form #stack-list #theme-list li.collapse li,
    #theme-list form #stack-list li.collapse li,
    form #theme-list li li.collapse #stack-list li,
    #stack-list form #theme-list li li.collapse li,
    #theme-list form li li.collapse #stack-list li,
    #stack-list #theme-list form li li.collapse li,
    form ul#docs-list #theme-list li.collapse #stack-list li,
    #stack-list form ul#docs-list #theme-list li.collapse li,
    #theme-list form ul#docs-list li.collapse #stack-list li,
    #stack-list #theme-list form ul#docs-list li.collapse li,
    form .row ul.collapse#docs-list #stack-list li,
    #stack-list form .row ul.collapse#docs-list li,
    form #stack-list ul.collapse#docs-list li,
    form #theme-list li ul.collapse#docs-list #stack-list li,
    #stack-list form #theme-list li ul.collapse#docs-list li,
    #theme-list form li ul.collapse#docs-list #stack-list li,
    #stack-list #theme-list form li ul.collapse#docs-list li,
    form ul#docs-list ul.collapse#docs-list #stack-list li,
    #stack-list form ul#docs-list ul.collapse#docs-list li,
    form .row .row.collapse #theme-list li > div,
    #theme-list form .row .row.collapse li > div,
    form #stack-list .row.collapse #theme-list li > div,
    #theme-list form #stack-list .row.collapse li > div,
    form #theme-list li .row.collapse li > div,
    #theme-list form li .row.collapse li > div,
    form ul#docs-list .row.collapse #theme-list li > div,
    #theme-list form ul#docs-list .row.collapse li > div,
    form .row .collapse#stack-list #theme-list li > div,
    #theme-list form .row .collapse#stack-list li > div,
    form #stack-list .collapse#stack-list #theme-list li > div,
    #theme-list form #stack-list .collapse#stack-list li > div,
    form #theme-list li .collapse#stack-list li > div,
    #theme-list form li .collapse#stack-list li > div,
    form ul#docs-list .collapse#stack-list #theme-list li > div,
    #theme-list form ul#docs-list .collapse#stack-list li > div,
    form .row #theme-list li.collapse li > div,
    #theme-list form .row li.collapse li > div,
    form #stack-list #theme-list li.collapse li > div,
    #theme-list form #stack-list li.collapse li > div,
    form #theme-list li li.collapse li > div,
    #theme-list form li li.collapse li > div,
    form ul#docs-list #theme-list li.collapse li > div,
    #theme-list form ul#docs-list li.collapse li > div,
    form .row ul.collapse#docs-list #theme-list li > div,
    #theme-list form .row ul.collapse#docs-list li > div,
    form #stack-list ul.collapse#docs-list #theme-list li > div,
    #theme-list form #stack-list ul.collapse#docs-list li > div,
    form #theme-list li ul.collapse#docs-list li > div,
    #theme-list form li ul.collapse#docs-list li > div,
    form ul#docs-list ul.collapse#docs-list #theme-list li > div,
    #theme-list form ul#docs-list ul.collapse#docs-list li > div,
    form .row .row.collapse ul#docs-list > li,
    form #theme-list li .row.collapse ul#docs-list > li,
    #theme-list form li .row.collapse ul#docs-list > li,
    form ul#docs-list .row.collapse ul#docs-list > li,
    form #stack-list .collapse#stack-list ul#docs-list > li,
    form .row #theme-list li.collapse ul#docs-list > li,
    #theme-list form .row li.collapse ul#docs-list > li,
    form #theme-list li li.collapse ul#docs-list > li,
    #theme-list form li li.collapse ul#docs-list > li,
    form ul#docs-list #theme-list li.collapse ul#docs-list > li,
    #theme-list form ul#docs-list li.collapse ul#docs-list > li,
    form .row ul.collapse#docs-list ul#docs-list > li,
    form #theme-list li ul.collapse#docs-list ul#docs-list > li,
    #theme-list form li ul.collapse#docs-list ul#docs-list > li,
    form ul#docs-list ul.collapse#docs-list ul#docs-list > li {
      padding: 0; }
    /* line 85, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
    form .row .row.collapse input, form #stack-list .row.collapse input, form #theme-list li .row.collapse input, #theme-list form li .row.collapse input, form ul#docs-list .row.collapse input, form .row .collapse#stack-list input, form #stack-list .collapse#stack-list input, form #theme-list li .collapse#stack-list input, #theme-list form li .collapse#stack-list input, form ul#docs-list .collapse#stack-list input, form .row #theme-list li.collapse input, #theme-list form .row li.collapse input, form #stack-list #theme-list li.collapse input, #theme-list form #stack-list li.collapse input, form #theme-list li li.collapse input, #theme-list form li li.collapse input, form ul#docs-list #theme-list li.collapse input, #theme-list form ul#docs-list li.collapse input, form .row ul.collapse#docs-list input, form #stack-list ul.collapse#docs-list input, form #theme-list li ul.collapse#docs-list input, #theme-list form li ul.collapse#docs-list input, form ul#docs-list ul.collapse#docs-list input {
      -moz-border-radius-bottomleft: 0;
      -moz-border-radius-topleft: 0;
      -webkit-border-bottom-left-radius: 0;
      -webkit-border-top-left-radius: 0; }
/* line 97, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
form .row input.column, form #stack-list input.column, form #theme-list li input.column, #theme-list form li input.column, form ul#docs-list input.column,
form .row input.columns,
form #stack-list input.columns,
form #theme-list li input.columns,
#theme-list form li input.columns,
form ul#docs-list input.columns,
form .row textarea.column,
form #stack-list textarea.column,
form #theme-list li textarea.column,
#theme-list form li textarea.column,
form ul#docs-list textarea.column,
form .row textarea.columns,
form #stack-list textarea.columns,
form #theme-list li textarea.columns,
#theme-list form li textarea.columns,
form ul#docs-list textarea.columns {
  padding-right: 0.5rem; }

/* Label Styles */
/* line 306, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
label {
  font-size: 0.875rem;
  color: #4d4d4d;
  cursor: pointer;
  display: block;
  font-weight: normal;
  margin-bottom: 0.5rem;
  /* Styles for required inputs */ }
  /* line 307, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  label.right {
    float: none;
    text-align: right; }
  /* line 308, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  label.inline {
    margin: 0 0 1rem 0;
    padding: 0.625rem 0; }
  /* line 310, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  label small {
    text-transform: capitalize;
    color: #666666; }

/* line 316, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
select {
  -webkit-appearance: none !important;
  background: #fafafa url("data:image/svg+xml;base64, PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iM3B4IiB2aWV3Qm94PSIwIDAgNiAzIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA2IDMiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwb2x5Z29uIHBvaW50cz0iNS45OTIsMCAyLjk5MiwzIC0wLjAwOCwwICIvPjwvc3ZnPg==") no-repeat;
  background-position-x: 97%;
  background-position-y: center;
  border: 1px solid #cccccc;
  padding: 0.5rem;
  font-size: 0.875rem;
  -webkit-border-radius: 0;
  border-radius: 0; }
  /* line 327, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  select.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }
  /* line 328, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  select:hover {
    background: #f2f2f2 url("data:image/svg+xml;base64, PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMHB4IiB5PSIwcHgiIHdpZHRoPSI2cHgiIGhlaWdodD0iM3B4IiB2aWV3Qm94PSIwIDAgNiAzIiBlbmFibGUtYmFja2dyb3VuZD0ibmV3IDAgMCA2IDMiIHhtbDpzcGFjZT0icHJlc2VydmUiPjxwb2x5Z29uIHBvaW50cz0iNS45OTIsMCAyLjk5MiwzIC0wLjAwOCwwICIvPjwvc3ZnPg==") no-repeat;
    background-position-x: 97%;
    background-position-y: center;
    border-color: #999999; }

@-moz-document url-prefix() {
  /* line 338, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  select {
    background: #fafafa; }

  /* line 339, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  select:hover {
    background: #f2f2f2; } }

/* Attach elements to the beginning or end of an input */
/* line 343, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.prefix,
.postfix {
  display: block;
  position: relative;
  z-index: 2;
  text-align: center;
  width: 100%;
  padding-top: 0;
  padding-bottom: 0;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  font-size: 0.875rem;
  height: 2.3125rem;
  line-height: 2.3125rem; }

/* Adjust padding, alignment and radius if pre/post element is a button */
/* line 346, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.postfix.button {
  padding-right: 0;
  padding-left: 0;
  padding-top: 0;
  padding-bottom: 0;
  text-align: center;
  line-height: 2.125rem;
  border: none; }

/* line 347, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.prefix.button {
  padding-right: 0;
  padding-left: 0;
  padding-top: 0;
  padding-bottom: 0;
  text-align: center;
  line-height: 2.125rem;
  border: none; }

/* line 349, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.prefix.button.radius {
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-border-radius-bottomleft: 3px;
  -moz-border-radius-topleft: 3px;
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px; }

/* line 350, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.postfix.button.radius {
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-border-radius-topright: 3px;
  -moz-border-radius-bottomright: 3px;
  -webkit-border-top-right-radius: 3px;
  -webkit-border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px; }

/* line 351, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.prefix.button.round {
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-border-radius-bottomleft: 1000px;
  -moz-border-radius-topleft: 1000px;
  -webkit-border-bottom-left-radius: 1000px;
  -webkit-border-top-left-radius: 1000px;
  border-bottom-left-radius: 1000px;
  border-top-left-radius: 1000px; }

/* line 352, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.postfix.button.round {
  -webkit-border-radius: 0;
  border-radius: 0;
  -moz-border-radius-topright: 1000px;
  -moz-border-radius-bottomright: 1000px;
  -webkit-border-top-right-radius: 1000px;
  -webkit-border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px; }

/* Separate prefix and postfix styles when on span or label so buttons keep their own */
/* line 355, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
span.prefix, label.prefix {
  background: #f2f2f2;
  border-color: #d9d9d9;
  border-left: none;
  color: #333333; }
  /* line 356, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  span.prefix.radius, label.prefix.radius {
    -webkit-border-radius: 0;
    border-radius: 0;
    -moz-border-radius-bottomleft: 3px;
    -moz-border-radius-topleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px; }

/* line 358, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
span.postfix, label.postfix {
  background: #f2f2f2;
  border-color: #cccccc;
  border-right: none;
  color: #333333; }
  /* line 359, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  span.postfix.radius, label.postfix.radius {
    -webkit-border-radius: 0;
    border-radius: 0;
    -moz-border-radius-topright: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-top-right-radius: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px; }

/* Input groups will automatically style first and last elements of the group */
/* line 365, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.input-group.radius > *:first-child, .input-group.radius > *:first-child * {
  -moz-border-radius-topright: 3px;
  -moz-border-radius-bottomright: 3px;
  -webkit-border-top-right-radius: 3px;
  -webkit-border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px; }
/* line 368, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.input-group.radius > *:last-child, .input-group.radius > *:last-child * {
  -moz-border-radius-bottomleft: 3px;
  -moz-border-radius-topleft: 3px;
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px; }
/* line 373, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.input-group.round > *:first-child, .input-group.round > *:first-child * {
  -moz-border-radius-topright: 1000px;
  -moz-border-radius-bottomright: 1000px;
  -webkit-border-top-right-radius: 1000px;
  -webkit-border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px; }
/* line 376, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.input-group.round > *:last-child, .input-group.round > *:last-child * {
  -moz-border-radius-bottomleft: 1000px;
  -moz-border-radius-topleft: 1000px;
  -webkit-border-bottom-left-radius: 1000px;
  -webkit-border-top-left-radius: 1000px;
  border-bottom-left-radius: 1000px;
  border-top-left-radius: 1000px; }

/* We use this to get basic styling on all basic form elements */
/* line 396, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
input[type="text"],
input[type="password"],
input[type="date"],
input[type="datetime"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
input[type="email"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="time"],
input[type="url"],
textarea {
  -webkit-appearance: none;
  -webkit-border-radius: 0;
  border-radius: 0;
  background-color: white;
  font-family: inherit;
  border: 1px solid #cccccc;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.75);
  display: block;
  font-size: 0.875rem;
  margin: 0 0 1rem 0;
  padding: 0.5rem;
  height: 2.3125rem;
  width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: -webkit-box-shadow 0.45s, border-color 0.45s ease-in-out;
  -moz-transition: -moz-box-shadow 0.45s, border-color 0.45s ease-in-out;
  transition: box-shadow 0.45s, border-color 0.45s ease-in-out; }
  /* line 182, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  input[type="text"]:focus,
  input[type="password"]:focus,
  input[type="date"]:focus,
  input[type="datetime"]:focus,
  input[type="datetime-local"]:focus,
  input[type="month"]:focus,
  input[type="week"]:focus,
  input[type="email"]:focus,
  input[type="number"]:focus,
  input[type="search"]:focus,
  input[type="tel"]:focus,
  input[type="time"]:focus,
  input[type="url"]:focus,
  textarea:focus {
    -webkit-box-shadow: 0 0 5px #999999;
    -moz-box-shadow: 0 0 5px #999999;
    box-shadow: 0 0 5px #999999;
    border-color: #999999; }
  /* line 123, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  input[type="text"]:focus,
  input[type="password"]:focus,
  input[type="date"]:focus,
  input[type="datetime"]:focus,
  input[type="datetime-local"]:focus,
  input[type="month"]:focus,
  input[type="week"]:focus,
  input[type="email"]:focus,
  input[type="number"]:focus,
  input[type="search"]:focus,
  input[type="tel"]:focus,
  input[type="time"]:focus,
  input[type="url"]:focus,
  textarea:focus {
    background: #fafafa;
    border-color: #999999;
    outline: none; }
  /* line 130, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  input[type="text"][disabled],
  input[type="password"][disabled],
  input[type="date"][disabled],
  input[type="datetime"][disabled],
  input[type="datetime-local"][disabled],
  input[type="month"][disabled],
  input[type="week"][disabled],
  input[type="email"][disabled],
  input[type="number"][disabled],
  input[type="search"][disabled],
  input[type="tel"][disabled],
  input[type="time"][disabled],
  input[type="url"][disabled],
  textarea[disabled] {
    background-color: #dddddd; }

/* Adjust margin for form elements below */
/* line 410, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
input[type="file"],
input[type="checkbox"],
input[type="radio"],
select {
  margin: 0 0 1rem 0; }

/* line 415, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
input[type="checkbox"] + label,
input[type="radio"] + label {
  display: inline-block;
  margin-right: 0.5rem;
  margin-left: 1rem;
  margin-bottom: 0;
  vertical-align: baseline; }

/* Normalize file input width */
/* line 424, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
input[type="file"] {
  width: 100%; }

/* We add basic fieldset styling */
/* line 429, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
fieldset {
  border: solid 1px #dddddd;
  padding: 1.25rem;
  margin: 1.125rem 0; }
  /* line 247, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
  fieldset legend {
    font-weight: bold;
    background: white;
    padding: 0 0.1875rem;
    margin: 0;
    margin-right: -0.1875rem; }

/* Error Handling */
/* line 436, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
[data-abide] .error small.error, [data-abide] span.error, [data-abide] small.error {
  display: block;
  padding: 0.375rem 0.5625rem 0.5625rem;
  margin-top: -1px;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  font-weight: normal;
  font-style: italic;
  background: #f04124;
  color: white; }
/* line 439, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
[data-abide] span.error, [data-abide] small.error {
  display: none; }

/* line 441, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
span.error, small.error {
  display: block;
  padding: 0.375rem 0.5625rem 0.5625rem;
  margin-top: -1px;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  font-weight: normal;
  font-style: italic;
  background: #f04124;
  color: white; }

/* line 447, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.error input,
.error textarea,
.error select {
  margin-bottom: 0; }
/* line 452, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.error label,
.error label.error {
  color: #f04124; }
/* line 457, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.error > small,
.error small.error {
  display: block;
  padding: 0.375rem 0.5625rem 0.5625rem;
  margin-top: -1px;
  margin-bottom: 1rem;
  font-size: 0.75rem;
  font-weight: normal;
  font-style: italic;
  background: #f04124;
  color: white; }
/* line 461, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
.error span.error-message {
  display: block; }

/* line 467, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
input.error,
textarea.error {
  margin-bottom: 0; }

/* line 470, /app/bower_components/foundation/scss/foundation/components/_forms.scss */
label.error {
  color: #f04124; }

/* line 48, /app/bower_components/foundation/scss/foundation/components/_inline-lists.scss */
.inline-list {
  margin: 0 auto 1.0625rem auto;
  margin-right: -1.375rem;
  margin-left: 0;
  padding: 0;
  list-style: none;
  overflow: hidden; }
  /* line 37, /app/bower_components/foundation/scss/foundation/components/_inline-lists.scss */
  .inline-list > li {
    list-style: none;
    float: right;
    margin-right: 1.375rem;
    display: block; }
    /* line 42, /app/bower_components/foundation/scss/foundation/components/_inline-lists.scss */
    .inline-list > li > * {
      display: block; }

/* Foundation Joyride */
/* line 44, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-list {
  display: none; }

/* Default styles for the container */
/* line 47, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-tip-guide {
  display: none;
  position: absolute;
  background: #333333;
  color: white;
  z-index: 101;
  top: 0;
  right: 2.5%;
  font-family: inherit;
  font-weight: normal;
  width: 95%; }

/* line 60, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.lt-ie9 .joyride-tip-guide {
  max-width: 800px;
  right: 50%;
  margin-right: -400px; }

/* line 66, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-content-wrapper {
  width: 100%;
  padding: 1.125rem 1.25rem 1.5rem; }
  /* line 71, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-content-wrapper .button {
    margin-bottom: 0 !important; }

/* Add a little css triangle pip, older browser just miss out on the fanciness of it */
/* line 76, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-tip-guide .joyride-nub {
  display: block;
  position: absolute;
  right: 22px;
  width: 0;
  height: 0;
  border: 10px solid #333333; }
  /* line 84, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.top {
    border-top-style: solid;
    border-color: #333333;
    border-top-color: transparent !important;
    border-right-color: transparent !important;
    border-left-color: transparent !important;
    top: -20px; }
  /* line 92, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.bottom {
    border-bottom-style: solid;
    border-color: #333333 !important;
    border-bottom-color: transparent !important;
    border-right-color: transparent !important;
    border-left-color: transparent !important;
    bottom: -20px; }
  /* line 101, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.right, .joyride-tip-guide body[class$="about"] dl dd img.joyride-nub, body[class$="about"] dl dd .joyride-tip-guide img.joyride-nub {
    right: -20px; }
  /* line 102, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.left {
    left: -20px; }

/* Typography */
/* line 112, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-tip-guide h1,
.joyride-tip-guide h2,
.joyride-tip-guide h3,
.joyride-tip-guide h4,
.joyride-tip-guide h5,
.joyride-tip-guide h6 {
  line-height: 1.25;
  margin: 0;
  font-weight: bold;
  color: white; }

/* line 118, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-tip-guide p {
  margin: 0 0 1.125rem 0;
  font-size: 0.875rem;
  line-height: 1.3; }

/* line 124, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-timer-indicator-wrap {
  width: 50px;
  height: 3px;
  border: solid 1px #555555;
  position: absolute;
  left: 1.0625rem;
  bottom: 1rem; }

/* line 132, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-timer-indicator {
  display: block;
  width: 0;
  height: inherit;
  background: #666666; }

/* line 139, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-close-tip {
  position: absolute;
  left: 12px;
  top: 10px;
  color: #777777 !important;
  text-decoration: none;
  font-size: 24px;
  font-weight: normal;
  line-height: .5 !important; }
  /* line 150, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-close-tip:hover, .joyride-close-tip:focus {
    color: #eee !important; }

/* line 153, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-modal-bg {
  position: fixed;
  height: 100%;
  width: 100%;
  background: transparent;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: none;
  top: 0;
  right: 0;
  cursor: pointer; }

/* line 166, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-expose-wrapper {
  background-color: #ffffff;
  position: absolute;
  border-radius: 3px;
  z-index: 102;
  -moz-box-shadow: 0 0 30px #ffffff;
  -webkit-box-shadow: 0 0 15px #ffffff;
  box-shadow: 0 0 15px #ffffff; }

/* line 178, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
.joyride-expose-cover {
  background: transparent;
  border-radius: 3px;
  position: absolute;
  z-index: 9999;
  top: 0;
  left: 0; }

/* Styles for screens that are atleast 768px; */
@media only screen and (min-width: 40.063em) {
  /* line 190, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
  .joyride-tip-guide {
    width: 300px;
    right: inherit; }
    /* line 192, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
    .joyride-tip-guide .joyride-nub.bottom {
      border-color: #333333 !important;
      border-bottom-color: transparent !important;
      border-right-color: transparent !important;
      border-left-color: transparent !important;
      bottom: -20px; }
    /* line 199, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
    .joyride-tip-guide .joyride-nub.right, .joyride-tip-guide body[class$="about"] dl dd img.joyride-nub, body[class$="about"] dl dd .joyride-tip-guide img.joyride-nub {
      border-color: #333333 !important;
      border-top-color: transparent !important;
      border-right-color: transparent !important;
      border-bottom-color: transparent !important;
      top: 22px;
      left: auto;
      right: -20px; }
    /* line 207, /app/bower_components/foundation/scss/foundation/components/_joyride.scss */
    .joyride-tip-guide .joyride-nub.left {
      border-color: #333333 !important;
      border-top-color: transparent !important;
      border-left-color: transparent !important;
      border-bottom-color: transparent !important;
      top: 22px;
      left: -20px;
      right: auto; } }
/* line 52, /app/bower_components/foundation/scss/foundation/components/_keystrokes.scss */
.keystroke,
kbd {
  background-color: #ededed;
  border-color: #dbdbdb;
  color: #222222;
  border-style: solid;
  border-width: 1px;
  margin: 0;
  font-family: "Consolas", "Menlo", "Courier", monospace;
  font-size: 0.875rem;
  padding: 0.125rem 0.25rem 0;
  -webkit-border-radius: 3px;
  border-radius: 3px; }

/* line 87, /app/bower_components/foundation/scss/foundation/components/_labels.scss */
.label {
  font-weight: normal;
  font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
  text-align: center;
  text-decoration: none;
  line-height: 1;
  white-space: nowrap;
  display: inline-block;
  position: relative;
  margin-bottom: inherit;
  padding: 0.25rem 0.5rem 0.375rem;
  font-size: 0.6875rem;
  background-color: #008cba;
  color: white; }
  /* line 92, /app/bower_components/foundation/scss/foundation/components/_labels.scss */
  .label.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }
  /* line 93, /app/bower_components/foundation/scss/foundation/components/_labels.scss */
  .label.round {
    -webkit-border-radius: 1000px;
    border-radius: 1000px; }
  /* line 95, /app/bower_components/foundation/scss/foundation/components/_labels.scss */
  .label.alert {
    background-color: #f04124;
    color: white; }
  /* line 96, /app/bower_components/foundation/scss/foundation/components/_labels.scss */
  .label.success {
    background-color: #5e8949;
    color: white; }
  /* line 97, /app/bower_components/foundation/scss/foundation/components/_labels.scss */
  .label.secondary {
    background-color: #e7e7e7;
    color: #333333; }

/* line 14, /app/bower_components/foundation/scss/foundation/components/_magellan.scss */
[data-magellan-expedition] {
  background: white;
  z-index: 50;
  min-width: 100%;
  padding: 10px; }
  /* line 20, /app/bower_components/foundation/scss/foundation/components/_magellan.scss */
  [data-magellan-expedition] .sub-nav {
    margin-bottom: 0; }
    /* line 22, /app/bower_components/foundation/scss/foundation/components/_magellan.scss */
    [data-magellan-expedition] .sub-nav dd {
      margin-bottom: 0; }
    /* line 23, /app/bower_components/foundation/scss/foundation/components/_magellan.scss */
    [data-magellan-expedition] .sub-nav .active {
      line-height: 1.8em; }

@-webkit-keyframes rotate {
  /* line 45, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  from {
    -webkit-transform: rotate(0deg); }

  /* line 46, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  to {
    -webkit-transform: rotate(360deg); } }

@-moz-keyframes rotate {
  /* line 49, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  from {
    -moz-transform: rotate(0deg); }

  /* line 50, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  to {
    -moz-transform: rotate(360deg); } }

@-o-keyframes rotate {
  /* line 53, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  from {
    -o-transform: rotate(0deg); }

  /* line 54, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  to {
    -o-transform: rotate(360deg); } }

@keyframes rotate {
  /* line 58, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  from {
    transform: rotate(0deg); }

  /* line 59, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  to {
    transform: rotate(360deg); } }

/* Orbit Graceful Loading */
/* line 63, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.slideshow-wrapper {
  position: relative; }
  /* line 66, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .slideshow-wrapper ul {
    list-style-type: none;
    margin: 0; }
    /* line 73, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .slideshow-wrapper ul li,
    .slideshow-wrapper ul li .orbit-caption {
      display: none; }
    /* line 76, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .slideshow-wrapper ul li:first-child {
      display: block; }
  /* line 79, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .slideshow-wrapper .orbit-container {
    background-color: transparent; }
    /* line 82, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .slideshow-wrapper .orbit-container li {
      display: block; }
      /* line 84, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
      .slideshow-wrapper .orbit-container li .orbit-caption {
        display: block; }

/* line 90, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.preloader {
  display: block;
  width: 40px;
  height: 40px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -20px;
  margin-left: -20px;
  border: solid 3px;
  border-color: #555 #fff;
  -webkit-border-radius: 1000px;
  border-radius: 1000px;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  -moz-animation-name: rotate;
  -moz-animation-duration: 1.5s;
  -moz-animation-iteration-count: infinite;
  -moz-animation-timing-function: linear;
  -o-animation-name: rotate;
  -o-animation-duration: 1.5s;
  -o-animation-iteration-count: infinite;
  -o-animation-timing-function: linear;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear; }

/* line 122, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.orbit-container {
  overflow: hidden;
  width: 100%;
  position: relative;
  background: none; }
  /* line 128, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container .orbit-slides-container {
    list-style: none;
    margin: 0;
    padding: 0;
    position: relative; }
    /* line 134, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-slides-container img {
      display: block;
      max-width: 100%; }
    /* line 136, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-slides-container > * {
      position: absolute;
      top: 0;
      width: 100%;
      margin-right: 100%; }
      /* line 147, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
      .orbit-container .orbit-slides-container > *:first-child {
        margin-right: 0%; }
      /* line 156, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
      .orbit-container .orbit-slides-container > * .orbit-caption {
        position: absolute;
        bottom: 0;
        background-color: rgba(51, 51, 51, 0.8);
        color: white;
        width: 100%;
        padding: 10px 14px;
        font-size: 0.875rem; }
  /* line 173, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container .orbit-slide-number {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 12px;
    color: white;
    background: rgba(0, 0, 0, 0);
    z-index: 10; }
    /* line 178, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-slide-number span {
      font-weight: 700;
      padding: 0.3125rem; }
  /* line 184, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container .orbit-timer {
    position: absolute;
    top: 12px;
    left: 10px;
    height: 6px;
    width: 100px;
    z-index: 10; }
    /* line 191, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-timer .orbit-progress {
      height: 3px;
      background-color: rgba(255, 255, 255, 0.3);
      display: block;
      width: 0%;
      position: relative;
      right: 20px;
      top: 5px; }
    /* line 204, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-timer > span {
      display: none;
      position: absolute;
      top: 0px;
      left: 0;
      width: 11px;
      height: 14px;
      border: solid 4px #fff;
      border-top: none;
      border-bottom: none; }
    /* line 218, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-timer.paused > span {
      left: -4px;
      top: 0px;
      width: 11px;
      height: 14px;
      border: inset 8px;
      border-right-style: solid;
      border-color: transparent transparent transparent #fff; }
      /* line 226, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
      .orbit-container .orbit-timer.paused > span.dark {
        border-color: transparent transparent transparent #333; }
  /* line 235, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container:hover .orbit-timer > span {
    display: block; }
  /* line 239, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container .orbit-prev,
  .orbit-container .orbit-next {
    position: absolute;
    top: 45%;
    margin-top: -25px;
    width: 36px;
    height: 60px;
    line-height: 50px;
    color: white;
    text-indent: -9999px !important;
    z-index: 10; }
    /* line 250, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-prev:hover,
    .orbit-container .orbit-next:hover {
      background-color: rgba(0, 0, 0, 0.3); }
    /* line 254, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-prev > span,
    .orbit-container .orbit-next > span {
      position: absolute;
      top: 50%;
      margin-top: -10px;
      display: block;
      width: 0;
      height: 0;
      border: inset 10px; }
  /* line 264, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container .orbit-prev {
    right: 0; }
    /* line 265, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-prev > span {
      border-left-style: solid;
      border-color: transparent;
      border-left-color: white; }
    /* line 270, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-prev:hover > span {
      border-left-color: white; }
  /* line 274, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-container .orbit-next {
    left: 0; }
    /* line 275, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-next > span {
      border-color: transparent;
      border-right-style: solid;
      border-right-color: white;
      right: 50%;
      margin-right: -4px; }
    /* line 282, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-container .orbit-next:hover > span {
      border-right-color: white; }

/* line 288, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.orbit-bullets-container {
  text-align: center; }

/* line 289, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.orbit-bullets {
  margin: 0 auto 30px auto;
  overflow: hidden;
  position: relative;
  top: 10px;
  float: none;
  text-align: center;
  display: inline-block; }
  /* line 298, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-bullets li {
    display: block;
    width: 0.5625rem;
    height: 0.5625rem;
    background: #cccccc;
    float: right;
    margin-left: 6px;
    -webkit-border-radius: 1000px;
    border-radius: 1000px; }
    /* line 307, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-bullets li.active {
      background: #999999; }
    /* line 311, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
    .orbit-bullets li:last-child {
      margin-left: 0; }

/* line 318, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.touch .orbit-container .orbit-prev,
.touch .orbit-container .orbit-next {
  display: none; }
/* line 321, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
.touch .orbit-bullets {
  display: none; }

@media only screen and (min-width: 40.063em) {
  /* line 330, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .touch .orbit-container .orbit-prev,
  .touch .orbit-container .orbit-next {
    display: inherit; }
  /* line 333, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .touch .orbit-bullets {
    display: block; } }
@media only screen and (max-width: 40em) {
  /* line 340, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-stack-on-small .orbit-slides-container {
    height: auto !important; }
  /* line 341, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-stack-on-small .orbit-slides-container > * {
    position: relative;
    margin-left: 0% !important; }
  /* line 348, /app/bower_components/foundation/scss/foundation/components/_orbit.scss */
  .orbit-stack-on-small .orbit-timer,
  .orbit-stack-on-small .orbit-next,
  .orbit-stack-on-small .orbit-prev,
  .orbit-stack-on-small .orbit-bullets {
    display: none; } }
/* line 132, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
ul.pagination {
  display: block;
  height: 1.5rem;
  margin-right: -0.3125rem; }
  /* line 95, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
  ul.pagination li {
    height: 1.5rem;
    color: #222222;
    font-size: 0.875rem;
    margin-right: 0.3125rem; }
    /* line 101, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
    ul.pagination li a {
      display: block;
      padding: 0.0625rem 0.625rem 0.0625rem;
      color: #999999;
      -webkit-border-radius: 3px;
      border-radius: 3px; }
    /* line 109, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
    ul.pagination li:hover a,
    ul.pagination li a:focus {
      background: #e6e6e6; }
    /* line 47, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
    ul.pagination li.unavailable a {
      cursor: default;
      color: #999999; }
    /* line 52, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
    ul.pagination li.unavailable:hover a, ul.pagination li.unavailable a:focus {
      background: transparent; }
    /* line 60, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
    ul.pagination li.current a {
      background: #008cba;
      color: white;
      font-weight: bold;
      cursor: default; }
      /* line 67, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
      ul.pagination li.current a:hover, ul.pagination li.current a:focus {
        background: #008cba; }
  /* line 119, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
  ul.pagination li {
    float: right;
    display: block; }

/* Pagination centred wrapper */
/* line 137, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
.pagination-centered {
  text-align: center; }
  /* line 119, /app/bower_components/foundation/scss/foundation/components/_pagination.scss */
  .pagination-centered ul.pagination li {
    float: none;
    display: inline-block; }

/* Panels */
/* line 70, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
.panel, p code, ul#downloads > li {
  border-style: solid;
  border-width: 1px;
  border-color: #d9d9d9;
  margin-bottom: 1.25rem;
  padding: 1.25rem;
  background: #f2f2f2; }
  /* line 48, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
  .panel > :first-child, p code > :first-child, ul#downloads > li > :first-child {
    margin-top: 0; }
  /* line 49, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
  .panel > :last-child, p code > :last-child, ul#downloads > li > :last-child {
    margin-bottom: 0; }
  /* line 54, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
  .panel h1, p code h1, ul#downloads > li h1, .panel h2, p code h2, ul#downloads > li h2, .panel h3, p code h3, ul#downloads > li h3, .panel h4, p code h4, ul#downloads > li h4, .panel h5, p code h5, ul#downloads > li h5, .panel h6, p code h6, ul#downloads > li h6, .panel p, p code p, ul#downloads > li p {
    color: #333333; }
  /* line 58, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
  .panel h1, p code h1, ul#downloads > li h1, .panel h2, p code h2, ul#downloads > li h2, .panel h3, p code h3, ul#downloads > li h3, .panel h4, p code h4, ul#downloads > li h4, .panel h5, p code h5, ul#downloads > li h5, .panel h6, p code h6, ul#downloads > li h6 {
    line-height: 1;
    margin-bottom: 0.625rem; }
    /* line 60, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
    .panel h1.subheader, p code h1.subheader, ul#downloads > li h1.subheader, .panel h2.subheader, p code h2.subheader, ul#downloads > li h2.subheader, .panel h3.subheader, p code h3.subheader, ul#downloads > li h3.subheader, .panel h4.subheader, p code h4.subheader, ul#downloads > li h4.subheader, .panel h5.subheader, p code h5.subheader, ul#downloads > li h5.subheader, .panel h6.subheader, p code h6.subheader, ul#downloads > li h6.subheader {
      line-height: 1.4; }
  /* line 72, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
  .panel.callout, p code.callout, ul#downloads > li.callout {
    border-style: solid;
    border-width: 1px;
    border-color: #baeeff;
    margin-bottom: 1.25rem;
    padding: 1.25rem;
    background: #edfbff; }
    /* line 48, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
    .panel.callout > :first-child, p code.callout > :first-child, ul#downloads > li.callout > :first-child {
      margin-top: 0; }
    /* line 49, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
    .panel.callout > :last-child, p code.callout > :last-child, ul#downloads > li.callout > :last-child {
      margin-bottom: 0; }
    /* line 54, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
    .panel.callout h1, p code.callout h1, ul#downloads > li.callout h1, .panel.callout h2, p code.callout h2, ul#downloads > li.callout h2, .panel.callout h3, p code.callout h3, ul#downloads > li.callout h3, .panel.callout h4, p code.callout h4, ul#downloads > li.callout h4, .panel.callout h5, p code.callout h5, ul#downloads > li.callout h5, .panel.callout h6, p code.callout h6, ul#downloads > li.callout h6, .panel.callout p, p code.callout p, ul#downloads > li.callout p {
      color: #333333; }
    /* line 58, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
    .panel.callout h1, p code.callout h1, ul#downloads > li.callout h1, .panel.callout h2, p code.callout h2, ul#downloads > li.callout h2, .panel.callout h3, p code.callout h3, ul#downloads > li.callout h3, .panel.callout h4, p code.callout h4, ul#downloads > li.callout h4, .panel.callout h5, p code.callout h5, ul#downloads > li.callout h5, .panel.callout h6, p code.callout h6, ul#downloads > li.callout h6 {
      line-height: 1;
      margin-bottom: 0.625rem; }
      /* line 60, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
      .panel.callout h1.subheader, p code.callout h1.subheader, ul#downloads > li.callout h1.subheader, .panel.callout h2.subheader, p code.callout h2.subheader, ul#downloads > li.callout h2.subheader, .panel.callout h3.subheader, p code.callout h3.subheader, ul#downloads > li.callout h3.subheader, .panel.callout h4.subheader, p code.callout h4.subheader, ul#downloads > li.callout h4.subheader, .panel.callout h5.subheader, p code.callout h5.subheader, ul#downloads > li.callout h5.subheader, .panel.callout h6.subheader, p code.callout h6.subheader, ul#downloads > li.callout h6.subheader {
        line-height: 1.4; }
    /* line 74, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
    .panel.callout a, p code.callout a, ul#downloads > li.callout a {
      color: #008cba; }
  /* line 79, /app/bower_components/foundation/scss/foundation/components/_panels.scss */
  .panel.radius, p code.radius, ul#downloads > li.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }

/* Pricing Tables */
/* line 135, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
.pricing-table {
  border: solid 1px #dddddd;
  margin-right: 0;
  margin-bottom: 1.25rem; }
  /* line 64, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
  .pricing-table * {
    list-style: none;
    line-height: 1; }
  /* line 138, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
  .pricing-table .title {
    background-color: #333333;
    padding: 0.9375rem 1.25rem;
    text-align: center;
    color: #eeeeee;
    font-weight: normal;
    font-size: 1rem;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif; }
  /* line 139, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
  .pricing-table .price {
    background-color: #f6f6f6;
    padding: 0.9375rem 1.25rem;
    text-align: center;
    color: #333333;
    font-weight: normal;
    font-size: 2rem;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif; }
  /* line 140, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
  .pricing-table .description {
    background-color: white;
    padding: 0.9375rem;
    text-align: center;
    color: #777777;
    font-size: 0.75rem;
    font-weight: normal;
    line-height: 1.4;
    border-bottom: dotted 1px #dddddd; }
  /* line 141, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
  .pricing-table .bullet-item {
    background-color: white;
    padding: 0.9375rem;
    text-align: center;
    color: #333333;
    font-size: 0.875rem;
    font-weight: normal;
    border-bottom: dotted 1px #dddddd; }
  /* line 142, /app/bower_components/foundation/scss/foundation/components/_pricing-tables.scss */
  .pricing-table .cta-button {
    background-color: white;
    text-align: center;
    padding: 1.25rem 1.25rem 0; }

/* Progress Bar */
/* line 53, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
.progress {
  background-color: #f6f6f6;
  height: 1.5625rem;
  border: 1px solid #cccccc;
  padding: 0.125rem;
  margin-bottom: 0.625rem; }
  /* line 57, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
  .progress .meter {
    background: #008cba;
    height: 100%;
    display: block; }
  /* line 60, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
  .progress.secondary .meter {
    background: #e7e7e7;
    height: 100%;
    display: block; }
  /* line 61, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
  .progress.success .meter {
    background: #5e8949;
    height: 100%;
    display: block; }
  /* line 62, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
  .progress.alert .meter {
    background: #f04124;
    height: 100%;
    display: block; }
  /* line 64, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
  .progress.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }
    /* line 65, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
    .progress.radius .meter {
      -webkit-border-radius: 2px;
      border-radius: 2px; }
  /* line 68, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
  .progress.round {
    -webkit-border-radius: 1000px;
    border-radius: 1000px; }
    /* line 69, /app/bower_components/foundation/scss/foundation/components/_progress-bars.scss */
    .progress.round .meter {
      -webkit-border-radius: 999px;
      border-radius: 999px; }

/* line 138, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
.reveal-modal-bg {
  position: fixed;
  height: 100%;
  width: 100%;
  background: black;
  background: rgba(0, 0, 0, 0.45);
  z-index: 98;
  display: none;
  top: 0;
  right: 0; }

/* line 140, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
.reveal-modal {
  visibility: hidden;
  display: none;
  position: absolute;
  right: 50%;
  z-index: 99;
  height: auto;
  margin-right: -40%;
  width: 80%;
  background-color: white;
  padding: 1.25rem;
  border: solid 1px #666666;
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  top: 50px; }
  /* line 71, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
  .reveal-modal .column,
  .reveal-modal .columns,
  .reveal-modal #stack-list li,
  #stack-list .reveal-modal li,
  .reveal-modal #theme-list li > div,
  #theme-list .reveal-modal li > div,
  .reveal-modal ul#docs-list > li {
    min-width: 0; }
  /* line 74, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
  .reveal-modal > :first-child {
    margin-top: 0; }
  /* line 75, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
  .reveal-modal > :last-child {
    margin-bottom: 0; }
  /* line 144, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
  .reveal-modal .close-reveal-modal {
    font-size: 1.375rem;
    line-height: 1;
    position: absolute;
    top: 0.5rem;
    left: 0.6875rem;
    color: #aaaaaa;
    font-weight: bold;
    cursor: pointer; }

@media only screen and (min-width: 40.063em) {
  /* line 149, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
  .reveal-modal {
    padding: 1.875rem;
    top: 6.25rem; }
    /* line 152, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
    .reveal-modal.tiny {
      margin-right: -15%;
      width: 30%; }
    /* line 153, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
    .reveal-modal.small {
      margin-right: -20%;
      width: 40%; }
    /* line 154, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
    .reveal-modal.medium {
      margin-right: -30%;
      width: 60%; }
    /* line 155, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
    .reveal-modal.large {
      margin-right: -35%;
      width: 70%; }
    /* line 156, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
    .reveal-modal.xlarge {
      margin-right: -47.5%;
      width: 95%; } }
@media print {
  /* line 162, /app/bower_components/foundation/scss/foundation/components/_reveal.scss */
  .reveal-modal {
    background: #fff !important; } }
/* line 81, /app/bower_components/foundation/scss/foundation/components/_side-nav.scss */
.side-nav {
  display: block;
  margin: 0;
  padding: 0.875rem 0;
  list-style-type: none;
  list-style-position: inside;
  font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif; }
  /* line 54, /app/bower_components/foundation/scss/foundation/components/_side-nav.scss */
  .side-nav li {
    margin: 0 0 0.4375rem 0;
    font-size: 0.875rem; }
    /* line 58, /app/bower_components/foundation/scss/foundation/components/_side-nav.scss */
    .side-nav li a {
      display: block;
      color: #008cba; }
    /* line 63, /app/bower_components/foundation/scss/foundation/components/_side-nav.scss */
    .side-nav li.active > a:first-child {
      color: #4d4d4d;
      font-weight: normal;
      font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif; }
    /* line 69, /app/bower_components/foundation/scss/foundation/components/_side-nav.scss */
    .side-nav li.divider {
      border-top: 1px solid;
      height: 0;
      padding: 0;
      list-style: none;
      border-top-color: #e6e6e6; }

/* line 169, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
.split.button {
  position: relative;
  padding-left: 5.0625rem; }
  /* line 72, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button span {
    display: block;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    border-right: solid 1px; }
    /* line 81, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
    .split.button span:before {
      position: absolute;
      content: "";
      width: 0;
      height: 0;
      display: block;
      border-style: inset;
      top: 50%;
      right: 50%; }
    /* line 93, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
    .split.button span:active {
      background-color: rgba(0, 0, 0, 0.1); }
  /* line 99, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button span {
    border-right-color: rgba(255, 255, 255, 0.5); }
  /* line 136, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button span {
    width: 3.09375rem; }
    /* line 137, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
    .split.button span:before {
      border-top-style: solid;
      border-width: 0.375rem;
      top: 48%;
      margin-right: -0.375rem; }
  /* line 162, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button span:before {
    border-color: white transparent transparent transparent; }
  /* line 99, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.secondary span {
    border-right-color: rgba(255, 255, 255, 0.5); }
  /* line 162, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.secondary span:before {
    border-color: white transparent transparent transparent; }
  /* line 99, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.alert span {
    border-right-color: rgba(255, 255, 255, 0.5); }
  /* line 99, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.success span {
    border-right-color: rgba(255, 255, 255, 0.5); }
  /* line 175, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.tiny {
    padding-left: 3.75rem; }
    /* line 108, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
    .split.button.tiny span {
      width: 2.25rem; }
      /* line 109, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
      .split.button.tiny span:before {
        border-top-style: solid;
        border-width: 0.375rem;
        top: 48%;
        margin-right: -0.375rem; }
  /* line 176, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.small {
    padding-left: 4.375rem; }
    /* line 122, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
    .split.button.small span {
      width: 2.625rem; }
      /* line 123, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
      .split.button.small span:before {
        border-top-style: solid;
        border-width: 0.4375rem;
        top: 48%;
        margin-right: -0.375rem; }
  /* line 177, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.large {
    padding-left: 5.5rem; }
    /* line 150, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
    .split.button.large span {
      width: 3.4375rem; }
      /* line 151, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
      .split.button.large span:before {
        border-top-style: solid;
        border-width: 0.3125rem;
        top: 48%;
        margin-right: -0.375rem; }
  /* line 178, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.expand {
    padding-left: 2rem; }
  /* line 162, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.secondary span:before {
    border-color: #333333 transparent transparent transparent; }
  /* line 182, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.radius span {
    -moz-border-radius-bottomleft: 3px;
    -moz-border-radius-topleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px; }
  /* line 183, /app/bower_components/foundation/scss/foundation/components/_split-buttons.scss */
  .split.button.round span {
    -moz-border-radius-bottomleft: 1000px;
    -moz-border-radius-topleft: 1000px;
    -webkit-border-bottom-left-radius: 1000px;
    -webkit-border-top-left-radius: 1000px;
    border-bottom-left-radius: 1000px;
    border-top-left-radius: 1000px; }

/* line 116, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
.sub-nav {
  display: block;
  width: auto;
  overflow: hidden;
  margin: -0.25rem 0 1.125rem;
  padding-top: 0.25rem;
  margin-left: 0;
  margin-right: -0.75rem; }
  /* line 63, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
  .sub-nav dt {
    text-transform: uppercase; }
  /* line 69, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
  .sub-nav dt,
  .sub-nav dd,
  .sub-nav li {
    float: right;
    display: inline;
    margin-right: 1rem;
    margin-bottom: 0.625rem;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
    font-weight: normal;
    font-size: 0.875rem;
    color: #999999; }
    /* line 79, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
    .sub-nav dt a,
    .sub-nav dd a,
    .sub-nav li a {
      text-decoration: none;
      color: #999999; }
      /* line 82, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
      .sub-nav dt a:hover,
      .sub-nav dd a:hover,
      .sub-nav li a:hover {
        color: #0079a1; }
    /* line 87, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
    .sub-nav dt.active a,
    .sub-nav dd.active a,
    .sub-nav li.active a {
      -webkit-border-radius: 3px;
      border-radius: 3px;
      font-weight: normal;
      background: #008cba;
      padding: 0.1875rem 1rem;
      cursor: default;
      color: white; }
      /* line 94, /app/bower_components/foundation/scss/foundation/components/_sub-nav.scss */
      .sub-nav dt.active a:hover,
      .sub-nav dd.active a:hover,
      .sub-nav li.active a:hover {
        background: #0079a1; }

/* line 287, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
div.switch {
  position: relative;
  padding: 0;
  display: block;
  overflow: hidden;
  border-style: solid;
  border-width: 1px;
  margin-bottom: 1.25rem;
  height: 2.25rem;
  background: white;
  border-color: #cccccc; }
  /* line 71, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch label {
    position: relative;
    right: 0;
    z-index: 2;
    float: right;
    width: 50%;
    height: 100%;
    margin: 0;
    font-weight: bold;
    text-align: right;
    -webkit-transition: all 0.1s ease-out;
    -moz-transition: all 0.1s ease-out;
    transition: all 0.1s ease-out; }
  /* line 88, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input {
    position: absolute;
    z-index: 3;
    opacity: 0;
    width: 100%;
    height: 100%;
    -moz-appearance: none; }
    /* line 98, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch input:hover, div.switch input:focus {
      cursor: pointer; }
  /* line 104, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch span:last-child {
    position: absolute;
    top: -1px;
    right: -1px;
    z-index: 1;
    display: block;
    padding: 0;
    border-width: 1px;
    border-style: solid;
    -webkit-transition: all 0.1s ease-out;
    -moz-transition: all 0.1s ease-out;
    transition: all 0.1s ease-out; }
  /* line 119, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:not(:checked) + label {
    opacity: 0; }
  /* line 122, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:checked {
    display: none !important; }
  /* line 123, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input {
    right: 0;
    display: block !important; }
  /* line 127, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:first-of-type + label,
  div.switch input:first-of-type + span + label {
    right: -50%; }
  /* line 129, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:first-of-type:checked + label,
  div.switch input:first-of-type:checked + span + label {
    right: 0%; }
  /* line 133, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:last-of-type + label,
  div.switch input:last-of-type + span + label {
    left: -50%;
    right: auto;
    text-align: left; }
  /* line 135, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:last-of-type:checked + label,
  div.switch input:last-of-type:checked + span + label {
    left: 0%;
    right: auto; }
  /* line 138, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch span.custom {
    display: none !important; }
  /* line 150, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  form.custom div.switch .hidden-field {
    margin-left: auto;
    position: absolute;
    visibility: visible; }
  /* line 169, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch label {
    padding: 0;
    line-height: 2.3rem;
    font-size: 0.875rem; }
  /* line 177, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch input:first-of-type:checked ~ span:last-child {
    right: 100%;
    margin-right: -2.1875rem; }
  /* line 183, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch span:last-child {
    width: 2.25rem;
    height: 2.25rem; }
  /* line 208, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch span:last-child {
    border-color: #b3b3b3;
    background: white;
    background: -moz-linear-gradient(top, white 0%, #f2f2f2 100%);
    background: -webkit-linear-gradient(top, white 0%, #f2f2f2 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #f2f2f2 100%);
    -webkit-box-shadow: 2px 0 10px 0 rgba(0, 0, 0, 0.07), 1000px 0 0 1000px #e6efe2, -2px 0 10px 0 rgba(0, 0, 0, 0.07), -1000px 0 0 1000px whitesmoke;
    box-shadow: 2px 0 10px 0 rgba(0, 0, 0, 0.07), 1000px 0 0 980px #e6efe2, -2px 0 10px 0 rgba(0, 0, 0, 0.07), -1000px 0 0 1000px whitesmoke; }
  /* line 232, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch:hover span:last-child, div.switch:focus span:last-child {
    background: white;
    background: -moz-linear-gradient(top, white 0%, #e6e6e6 100%);
    background: -webkit-linear-gradient(top, white 0%, #e6e6e6 100%);
    background: linear-gradient(to bottom, #ffffff 0%, #e6e6e6 100%); }
  /* line 242, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch:active {
    background: transparent; }
  /* line 291, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch.large {
    height: 2.75rem; }
    /* line 169, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.large label {
      padding: 0;
      line-height: 2.3rem;
      font-size: 1.0625rem; }
    /* line 177, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.large input:first-of-type:checked ~ span:last-child {
      right: 100%;
      margin-right: -2.6875rem; }
    /* line 183, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.large span:last-child {
      width: 2.75rem;
      height: 2.75rem; }
  /* line 294, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch.small {
    height: 1.75rem; }
    /* line 169, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.small label {
      padding: 0;
      line-height: 2.1rem;
      font-size: 0.75rem; }
    /* line 177, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.small input:first-of-type:checked ~ span:last-child {
      right: 100%;
      margin-right: -1.6875rem; }
    /* line 183, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.small span:last-child {
      width: 1.75rem;
      height: 1.75rem; }
  /* line 297, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch.tiny {
    height: 1.375rem; }
    /* line 169, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.tiny label {
      padding: 0;
      line-height: 1.9rem;
      font-size: 0.6875rem; }
    /* line 177, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.tiny input:first-of-type:checked ~ span:last-child {
      right: 100%;
      margin-right: -1.3125rem; }
    /* line 183, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.tiny span:last-child {
      width: 1.375rem;
      height: 1.375rem; }
  /* line 300, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch.radius {
    -webkit-border-radius: 4px;
    border-radius: 4px; }
    /* line 301, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.radius span:last-child {
      -webkit-border-radius: 3px;
      border-radius: 3px; }
  /* line 305, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  div.switch.round {
    -webkit-border-radius: 1000px;
    border-radius: 1000px; }
    /* line 306, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.round span:last-child {
      -webkit-border-radius: 999px;
      border-radius: 999px; }
    /* line 307, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
    div.switch.round label {
      padding: 0 0.5625rem; }

@-webkit-keyframes webkitSiblingBugfix {
  /* line 312, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  from {
    position: relative; }

  /* line 312, /app/bower_components/foundation/scss/foundation/components/_switch.scss */
  to {
    position: relative; } }

/* line 89, /app/bower_components/foundation/scss/foundation/components/_tables.scss */
table {
  background: white;
  margin-bottom: 1.25rem;
  border: solid 1px #dddddd; }
  /* line 51, /app/bower_components/foundation/scss/foundation/components/_tables.scss */
  table thead,
  table tfoot {
    background: whitesmoke;
    font-weight: bold; }
    /* line 57, /app/bower_components/foundation/scss/foundation/components/_tables.scss */
    table thead tr th,
    table thead tr td,
    table tfoot tr th,
    table tfoot tr td {
      padding: 0.5rem 0.625rem 0.625rem;
      font-size: 0.875rem;
      color: #222222;
      text-align: right; }
  /* line 68, /app/bower_components/foundation/scss/foundation/components/_tables.scss */
  table tr th,
  table tr td {
    padding: 0.5625rem 0.625rem;
    font-size: 0.875rem;
    color: #222222; }
  /* line 76, /app/bower_components/foundation/scss/foundation/components/_tables.scss */
  table tr.even, table tr.alt, table tr:nth-of-type(even) {
    background: #f9f9f9; }
  /* line 83, /app/bower_components/foundation/scss/foundation/components/_tables.scss */
  table thead tr th,
  table tfoot tr th,
  table tbody tr td,
  table tr td,
  table tfoot tr td {
    display: table-cell;
    line-height: 1.125rem; }

/* line 23, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
.tabs {
  *zoom: 1;
  margin-bottom: 0 !important; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .tabs:before, .tabs:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .tabs:after {
    clear: both; }
  /* line 26, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs dd {
    position: relative;
    margin-bottom: 0 !important;
    top: 1px;
    float: right; }
    /* line 31, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
    .tabs dd > a {
      display: block;
      background: #efefef;
      color: #222222;
      padding-top: 1rem;
      padding-left: 2rem;
      padding-bottom: 1.0625rem;
      padding-right: 2rem;
      font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
      font-size: 1rem; }
      /* line 41, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
      .tabs dd > a:hover {
        background: #e2e2e2; }
    /* line 43, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
    .tabs dd.active a {
      background: #fff; }
  /* line 47, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs.radius dd:first-child a {
    -moz-border-radius-topright: 3px;
    -moz-border-radius-bottomright: 3px;
    -webkit-border-top-right-radius: 3px;
    -webkit-border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px; }
  /* line 50, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs.radius dd:last-child a {
    -moz-border-radius-bottomleft: 3px;
    -moz-border-radius-topleft: 3px;
    -webkit-border-bottom-left-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px; }
  /* line 54, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs.vertical dd {
    position: inherit;
    float: none;
    display: block;
    top: auto; }

/* line 63, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
.tabs-content {
  *zoom: 1;
  margin-bottom: 1.5rem; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .tabs-content:before, .tabs-content:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .tabs-content:after {
    clear: both; }
  /* line 66, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs-content > .content, ul#downloads > li .tabs-content > .notes, ul#downloads > li table .tabs-content > ul[id$="-download"], #download > div .tabs-content > ul[id$="-download"] {
    display: none;
    float: right;
    padding: 0.9375rem 0; }
    /* line 70, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
    .tabs-content > .content.active, ul#downloads > li .tabs-content > .active.notes, ul#downloads > li table .tabs-content > ul.active[id$="-download"], #download > div .tabs-content > ul.active[id$="-download"] {
      display: block; }
    /* line 71, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
    .tabs-content > .content.contained, ul#downloads > li .tabs-content > .contained.notes, ul#downloads > li table .tabs-content > ul.contained[id$="-download"], #download > div .tabs-content > ul.contained[id$="-download"] {
      padding: 0.9375rem; }
  /* line 73, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs-content.vertical {
    display: block; }
    /* line 75, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
    .tabs-content.vertical > .content, ul#downloads > li .tabs-content.vertical > .notes, ul#downloads > li table .tabs-content.vertical > ul[id$="-download"], #download > div .tabs-content.vertical > ul[id$="-download"] {
      padding: 0 0.9375rem; }

@media only screen and (min-width: 40.063em) {
  /* line 80, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs.vertical {
    width: 20%;
    float: right;
    margin-bottom: 1.25rem; }

  /* line 87, /app/bower_components/foundation/scss/foundation/components/_tabs.scss */
  .tabs-content.vertical {
    width: 80%;
    float: right;
    margin-right: -1px; } }
/* Image Thumbnails */
/* line 62, /app/bower_components/foundation/scss/foundation/components/_thumbs.scss */
.th {
  line-height: 0;
  display: inline-block;
  border: solid 4px white;
  -webkit-box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
  -webkit-transition: all 200ms ease-out;
  -moz-transition: all 200ms ease-out;
  transition: all 200ms ease-out; }
  /* line 49, /app/bower_components/foundation/scss/foundation/components/_thumbs.scss */
  .th:hover, .th:focus {
    -webkit-box-shadow: 0 0 6px 1px rgba(0, 140, 186, 0.5);
    box-shadow: 0 0 6px 1px rgba(0, 140, 186, 0.5); }
  /* line 66, /app/bower_components/foundation/scss/foundation/components/_thumbs.scss */
  .th.radius {
    -webkit-border-radius: 3px;
    border-radius: 3px; }

/* line 68, /app/bower_components/foundation/scss/foundation/components/_thumbs.scss */
a.th {
  display: inline-block;
  max-width: 100%; }

/* Tooltips */
/* line 32, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
.has-tip {
  border-bottom: dotted 1px #cccccc;
  cursor: help;
  font-weight: bold;
  color: #333333; }
  /* line 39, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .has-tip:hover, .has-tip:focus {
    border-bottom: dotted 1px #003f54;
    color: #008cba; }
  /* line 45, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .has-tip.tip-left, .has-tip.tip-right {
    float: none !important; }

/* line 48, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
.tooltip {
  display: none;
  position: absolute;
  z-index: 999;
  font-weight: normal;
  font-size: 0.875rem;
  line-height: 1.3;
  padding: 0.75rem;
  max-width: 85%;
  right: 50%;
  width: 100%;
  color: white;
  background: #333333;
  -webkit-border-radius: 3px;
  border-radius: 3px; }
  /* line 63, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip > .nub {
    display: block;
    right: 5px;
    position: absolute;
    width: 0;
    height: 0;
    border: solid 5px;
    border-color: transparent transparent #333333 transparent;
    top: -10px; }
  /* line 74, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip.opened {
    color: #008cba !important;
    border-bottom: dotted 1px #003f54 !important; }

/* line 80, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
.tap-to-close {
  display: block;
  font-size: 0.625rem;
  color: #777777;
  font-weight: normal; }

@media only screen and (min-width: 40.063em) {
  /* line 89, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip > .nub {
    border-color: transparent transparent #333333 transparent;
    top: -10px; }
  /* line 93, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip.tip-top > .nub {
    border-color: #333333 transparent transparent transparent;
    top: auto;
    bottom: -10px; }
  /* line 100, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip.tip-left, .tooltip.tip-right {
    float: none !important; }
  /* line 102, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip.tip-left > .nub {
    border-color: transparent transparent transparent #333333;
    right: -10px;
    left: auto;
    top: 50%;
    margin-top: -5px; }
  /* line 109, /app/bower_components/foundation/scss/foundation/components/_tooltips.scss */
  .tooltip.tip-right > .nub {
    border-color: transparent #333333 transparent transparent;
    right: auto;
    left: -10px;
    top: 50%;
    margin-top: -5px; } }
/* line 111, /app/bower_components/foundation/scss/foundation/components/_type.scss */
p.lead {
  font-size: 1.21875rem;
  line-height: 1.6; }

/* line 116, /app/bower_components/foundation/scss/foundation/components/_type.scss */
.subheader {
  line-height: 1.4;
  color: #6f6f6f;
  font-weight: 300;
  margin-top: 0.2rem;
  margin-bottom: 0.5rem; }

/* Typography resets */
/* line 145, /app/bower_components/foundation/scss/foundation/components/_type.scss */
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
  direction: rtl; }

/* Default Link Styles */
/* line 152, /app/bower_components/foundation/scss/foundation/components/_type.scss */
a {
  color: #5e8949;
  text-decoration: none;
  line-height: inherit; }
  /* line 158, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  a:hover, a:focus {
    color: #0079a1; }
  /* line 160, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  a img {
    border: none; }

/* Default paragraph styles */
/* line 164, /app/bower_components/foundation/scss/foundation/components/_type.scss */
p {
  font-family: inherit;
  font-weight: normal;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.25rem;
  text-rendering: optimizeLegibility; }
  /* line 174, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  p aside {
    font-size: 0.875rem;
    line-height: 1.35;
    font-style: italic; }

/* Default header styles */
/* line 182, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h1, h2, h3, h4, h5, h6 {
  font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
  font-weight: 300;
  font-style: normal;
  color: #222222;
  text-rendering: optimizeLegibility;
  margin-top: 0.2rem;
  margin-bottom: 0.5rem;
  line-height: 1.4; }
  /* line 192, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h1 small, h2 small, h3 small, h4 small, h5 small, h6 small {
    font-size: 60%;
    color: #6f6f6f;
    line-height: 0; }

/* line 199, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h1 {
  font-size: 2.125rem; }

/* line 200, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h2 {
  font-size: 1.6875rem; }

/* line 201, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h3 {
  font-size: 1.375rem; }

/* line 202, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h4 {
  font-size: 1.125rem; }

/* line 203, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h5 {
  font-size: 1.125rem; }

/* line 204, /app/bower_components/foundation/scss/foundation/components/_type.scss */
h6 {
  font-size: 1rem; }

/* line 208, /app/bower_components/foundation/scss/foundation/components/_type.scss */
hr {
  border: solid #dddddd;
  border-width: 1px 0 0;
  clear: both;
  margin: 1.25rem 0 1.1875rem;
  height: 0; }

/* Helpful Typography Defaults */
/* line 218, /app/bower_components/foundation/scss/foundation/components/_type.scss */
em,
i {
  font-style: italic;
  line-height: inherit; }

/* line 224, /app/bower_components/foundation/scss/foundation/components/_type.scss */
strong,
b {
  font-weight: bold;
  line-height: inherit; }

/* line 229, /app/bower_components/foundation/scss/foundation/components/_type.scss */
small {
  font-size: 60%;
  line-height: inherit; }

/* line 234, /app/bower_components/foundation/scss/foundation/components/_type.scss */
code {
  font-family: Consolas, "Liberation Mono", Courier, monospace;
  font-weight: bold;
  color: #bb260d; }

/* Lists */
/* line 243, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul,
ol,
dl {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.25rem;
  list-style-position: outside;
  font-family: inherit; }

/* line 251, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul {
  margin-right: 1.1rem; }
  /* line 253, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  ul.no-bullet, ul.social, ul#downloads, ul#docs-list {
    margin-right: 0; }
    /* line 257, /app/bower_components/foundation/scss/foundation/components/_type.scss */
    ul.no-bullet li ul, ul.social li ul, ul#downloads li ul, ul#docs-list li ul,
    ul.no-bullet li ol,
    ul.social li ol,
    ul#downloads li ol,
    ul#docs-list li ol {
      margin-right: 1.25rem;
      margin-bottom: 0;
      list-style: none; }

/* Unordered Lists */
/* line 270, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul li ul,
ul li ol {
  margin-right: 1.25rem;
  margin-bottom: 0;
  font-size: 1rem;
  /* Override nested font-size change */ }
/* line 279, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul.square li ul, ul.circle li ul, ul.disc li ul {
  list-style: inherit; }
/* line 282, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul.square {
  list-style-type: square;
  margin-right: 1.1rem; }
/* line 283, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul.circle {
  list-style-type: circle;
  margin-right: 1.1rem; }
/* line 284, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul.disc {
  list-style-type: disc;
  margin-right: 1.1rem; }
/* line 285, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ul.no-bullet, ul.social, ul#downloads, ul#docs-list {
  list-style: none; }

/* Ordered Lists */
/* line 289, /app/bower_components/foundation/scss/foundation/components/_type.scss */
ol {
  margin-right: 1.4rem; }
  /* line 293, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  ol li ul,
  ol li ol {
    margin-right: 1.25rem;
    margin-bottom: 0; }

/* Definition Lists */
/* line 302, /app/bower_components/foundation/scss/foundation/components/_type.scss */
dl dt {
  margin-bottom: 0.3rem;
  font-weight: bold; }
/* line 306, /app/bower_components/foundation/scss/foundation/components/_type.scss */
dl dd {
  margin-bottom: 0.75rem; }

/* Abbreviations */
/* line 311, /app/bower_components/foundation/scss/foundation/components/_type.scss */
abbr,
acronym {
  text-transform: uppercase;
  font-size: 90%;
  color: #555555;
  border-bottom: 1px dotted #dddddd;
  cursor: help; }

/* line 318, /app/bower_components/foundation/scss/foundation/components/_type.scss */
abbr {
  text-transform: none; }

/* Blockquotes */
/* line 323, /app/bower_components/foundation/scss/foundation/components/_type.scss */
blockquote {
  margin: 0 0 1.25rem;
  padding: 0.5625rem 1.25rem 0 1.1875rem;
  border-right: 1px solid #dddddd; }
  /* line 328, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  blockquote cite {
    display: block;
    font-size: 0.8125rem;
    color: #555555; }
    /* line 332, /app/bower_components/foundation/scss/foundation/components/_type.scss */
    blockquote cite:before {
      content: "\2014 \0020"; }
    /* line 337, /app/bower_components/foundation/scss/foundation/components/_type.scss */
    blockquote cite a,
    blockquote cite a:visited {
      color: #555555; }

/* line 343, /app/bower_components/foundation/scss/foundation/components/_type.scss */
blockquote,
blockquote p {
  line-height: 1.6;
  color: #6f6f6f; }

/* Microformats */
/* line 349, /app/bower_components/foundation/scss/foundation/components/_type.scss */
.vcard {
  display: inline-block;
  margin: 0 0 1.25rem 0;
  border: 1px solid #dddddd;
  padding: 0.625rem 0.75rem; }
  /* line 355, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .vcard li {
    margin: 0;
    display: block; }
  /* line 359, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .vcard .fn {
    font-weight: bold;
    font-size: 0.9375rem; }

/* line 366, /app/bower_components/foundation/scss/foundation/components/_type.scss */
.vevent .summary {
  font-weight: bold; }
/* line 368, /app/bower_components/foundation/scss/foundation/components/_type.scss */
.vevent abbr {
  cursor: default;
  text-decoration: none;
  font-weight: bold;
  border: none;
  padding: 0 0.0625rem; }

@media only screen and (min-width: 40.063em) {
  /* line 379, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.4; }

  /* line 380, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h1 {
    font-size: 2.75rem; }

  /* line 381, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h2 {
    font-size: 2.3125rem; }

  /* line 382, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h3 {
    font-size: 1.6875rem; }

  /* line 383, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h4 {
    font-size: 1.4375rem; } }
/*
 * Print styles.
 *
 * Inlined to avoid required HTTP connection: www.phpied.com/delay-loading-your-print-css/
 * Credit to Paul Irish and HTML5 Boilerplate (html5boilerplate.com)
*/
/* line 394, /app/bower_components/foundation/scss/foundation/components/_type.scss */
.print-only {
  display: none !important; }

@media print {
  /* line 396, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  * {
    background: transparent !important;
    color: #000 !important;
    /* Black prints faster: h5bp.com/s */
    box-shadow: none !important;
    text-shadow: none !important; }

  /* line 404, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  a,
  a:visited {
    text-decoration: underline; }

  /* line 405, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  a[href]:after {
    content: " (" attr(href) ")"; }

  /* line 407, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  abbr[title]:after {
    content: " (" attr(title) ")"; }

  /* line 412, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: ""; }

  /* line 415, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid; }

  /* line 420, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  thead {
    display: table-header-group;
    /* h5bp.com/t */ }

  /* line 423, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  tr,
  img {
    page-break-inside: avoid; }

  /* line 425, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  img {
    max-width: 100% !important; }

  @page {
    margin: 0.5cm; }

  /* line 431, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3; }

  /* line 437, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  h2,
  h3 {
    page-break-after: avoid; }

  /* line 439, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .hide-on-print {
    display: none !important; }

  /* line 440, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .print-only {
    display: block !important; }

  /* line 441, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .hide-for-print {
    display: none !important; }

  /* line 442, /app/bower_components/foundation/scss/foundation/components/_type.scss */
  .show-for-print {
    display: inherit !important; } }
/* line 77, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
meta.foundation-mq-topbar {
  font-family: "/screen and (min-width: 1024px)/";
  width: 1024px; }

/* Wrapped around .top-bar to contain to grid width */
/* line 86, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
.contain-to-grid {
  width: 100%;
  background: #2a5d84; }
  /* line 90, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .contain-to-grid .top-bar {
    margin-bottom: 0; }

/* line 94, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
.fixed {
  width: 100%;
  right: 0;
  position: fixed;
  top: 0;
  z-index: 99; }
  /* line 101, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .fixed.expanded:not(.top-bar) {
    overflow-y: auto;
    height: auto;
    width: 100%;
    max-height: 100%; }
    /* line 107, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .fixed.expanded:not(.top-bar) .title-area {
      position: fixed;
      width: 100%;
      z-index: 99; }
    /* line 113, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .fixed.expanded:not(.top-bar) .top-bar-section {
      z-index: 98;
      margin-top: 60px; }

/* line 120, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
.top-bar {
  overflow: hidden;
  height: 60px;
  line-height: 60px;
  position: relative;
  background: #2a5d84;
  margin-bottom: 0; }
  /* line 129, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar ul {
    margin-bottom: 0;
    list-style: none; }
  /* line 134, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar .row, .top-bar #stack-list, .top-bar #theme-list li, #theme-list .top-bar li, .top-bar ul#docs-list {
    max-width: none; }
  /* line 137, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar form,
  .top-bar input {
    margin-bottom: 0; }
  /* line 139, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar input {
    height: auto;
    padding-top: .35rem;
    padding-bottom: .35rem;
    font-size: 0.75rem; }
  /* line 141, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar .button {
    padding-top: .45rem;
    padding-bottom: .35rem;
    margin-bottom: 0;
    font-size: 0.75rem; }
  /* line 152, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar .title-area {
    position: relative;
    margin: 0; }
  /* line 157, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar .name {
    height: 60px;
    margin: 0;
    font-size: 16px; }
    /* line 162, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar .name h1 {
      line-height: 60px;
      font-size: 1em;
      margin: 0; }
      /* line 166, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar .name h1 a {
        font-weight: normal;
        color: white;
        width: 50%;
        display: block;
        padding: 0 20px; }
  /* line 177, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar .toggle-topbar {
    position: absolute;
    left: 0;
    top: 0; }
    /* line 182, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar .toggle-topbar a {
      color: white;
      text-transform: uppercase;
      font-size: 0.8125rem;
      font-weight: bold;
      position: relative;
      display: block;
      padding: 0 20px;
      height: 60px;
      line-height: 60px; }
    /* line 195, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar .toggle-topbar.menu-icon {
      left: 20px;
      top: 50%;
      margin-top: -16px;
      padding-right: 40px; }
      /* line 201, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar .toggle-topbar.menu-icon a {
        text-indent: -48px;
        width: 34px;
        height: 34px;
        line-height: 33px;
        padding: 0;
        color: white; }
        /* line 209, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
        .top-bar .toggle-topbar.menu-icon a span {
          position: absolute;
          left: 0;
          display: block;
          width: 16px;
          height: 0;
          -webkit-box-shadow: 0 10px 0 1px white, 0 16px 0 1px white, 0 22px 0 1px white;
          box-shadow: 0 10px 0 1px white, 0 16px 0 1px white, 0 22px 0 1px white; }
  /* line 230, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar.expanded {
    height: auto;
    background: transparent; }
    /* line 234, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar.expanded .title-area {
      background: #2a5d84; }
    /* line 237, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar.expanded .toggle-topbar a {
      color: #888888; }
      /* line 238, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar.expanded .toggle-topbar a span {
        -webkit-box-shadow: 0 10px 0 1px #888888, 0 16px 0 1px #888888, 0 22px 0 1px #888888;
        box-shadow: 0 10px 0 1px #888888, 0 16px 0 1px #888888, 0 22px 0 1px #888888; }

/* line 258, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
.top-bar-section {
  right: 0;
  position: relative;
  width: auto;
  -webkit-transition: right 300ms ease-out;
  -moz-transition: right 300ms ease-out;
  transition: right 300ms ease-out; }
  /* line 264, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section ul {
    width: 100%;
    height: auto;
    display: block;
    background: #1f3c52;
    font-size: 16px;
    margin: 0; }
  /* line 274, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section .divider,
  .top-bar-section [role="separator"] {
    border-top: solid 1px #1e425d;
    clear: both;
    height: 1px;
    width: 100%; }
  /* line 282, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section ul li > a {
    display: block;
    width: 100%;
    color: white;
    padding: 12px 0 12px 0;
    padding-right: 20px;
    font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
    font-size: 0.8125rem;
    font-weight: normal;
    background: #1f3c52; }
    /* line 293, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul li > a.button {
      background: #008cba;
      font-size: 0.8125rem;
      padding-right: 20px;
      padding-left: 20px; }
      /* line 298, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section ul li > a.button:hover {
        background: #006687; }
    /* line 302, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul li > a.button.secondary {
      background: #e7e7e7; }
      /* line 304, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section ul li > a.button.secondary:hover {
        background: #cecece; }
    /* line 308, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul li > a.button.success {
      background: #5e8949; }
      /* line 310, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section ul li > a.button.success:hover {
        background: #476837; }
    /* line 314, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul li > a.button.alert {
      background: #f04124; }
      /* line 316, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section ul li > a.button.alert:hover {
        background: #d32a0e; }
  /* line 324, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section ul li:hover > a {
    background: #1f3c52;
    color: white; }
  /* line 331, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section ul li.active > a {
    background: #1f3c52;
    color: white; }
    /* line 334, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul li.active > a:hover {
      background: #0079a1; }
  /* line 341, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section .has-form {
    padding: 20px; }
  /* line 344, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown {
    position: relative; }
    /* line 348, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-dropdown > a:after {
      content: "";
      display: block;
      width: 0;
      height: 0;
      border: inset 5px;
      border-color: transparent rgba(255, 255, 255, 0.4) transparent transparent;
      border-right-style: solid;
      margin-left: 20px;
      margin-top: -4.5px;
      position: absolute;
      top: 50%;
      left: 0; }
    /* line 360, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-dropdown.moved {
      position: static; }
      /* line 361, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .has-dropdown.moved > .dropdown {
        display: block; }
  /* line 368, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown {
    position: absolute;
    right: 100%;
    top: 0;
    display: none;
    z-index: 99; }
    /* line 375, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .dropdown li {
      width: 100%;
      height: auto; }
      /* line 379, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .dropdown li a {
        font-weight: normal;
        padding: 8px 20px; }
        /* line 382, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
        .top-bar-section .dropdown li a.parent-link {
          font-weight: normal; }
      /* line 387, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .dropdown li.title h5 {
        margin-bottom: 0; }
        /* line 388, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
        .top-bar-section .dropdown li.title h5 a {
          color: white;
          line-height: 30px;
          display: block; }
    /* line 396, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .dropdown label {
      padding: 8px 20px 2px;
      margin-bottom: 0;
      text-transform: uppercase;
      color: #777777;
      font-weight: bold;
      font-size: 0.625rem; }

/* line 407, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
.js-generated {
  display: block; }

@media screen and (min-width: 1024px) {
  /* line 412, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar {
    background: #2a5d84;
    *zoom: 1;
    overflow: visible; }
    /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
    .top-bar:before, .top-bar:after {
      content: " ";
      display: table; }
    /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
    .top-bar:after {
      clear: both; }
    /* line 417, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar .toggle-topbar {
      display: none; }
    /* line 419, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar .title-area {
      float: right; }
    /* line 420, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar .name h1 a {
      width: auto; }
    /* line 423, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar input,
    .top-bar .button {
      font-size: 0.875rem;
      position: relative;
      top: 7px; }
    /* line 429, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar.expanded {
      background: #2a5d84; }

  /* line 432, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .contain-to-grid .top-bar {
    max-width: 62.5rem;
    margin: 0 auto;
    margin-bottom: 0; }

  /* line 438, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .top-bar-section {
    -webkit-transition: none 0 0;
    -moz-transition: none 0 0;
    transition: none 0 0;
    right: 0 !important; }
    /* line 442, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul {
      width: auto;
      height: auto !important;
      display: inline; }
      /* line 447, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section ul li {
        float: right; }
        /* line 449, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
        .top-bar-section ul li .js-generated {
          display: none; }
    /* line 455, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section li.hover > a:not(.button) {
      background: #1f3c52;
      color: white; }
    /* line 460, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section li a:not(.button) {
      padding: 0 20px;
      line-height: 60px;
      background: #2a5d84; }
      /* line 464, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section li a:not(.button):hover {
        background: #1f3c52; }
    /* line 472, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-dropdown > a {
      padding-left: 40px !important; }
      /* line 474, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .has-dropdown > a:after {
        content: "";
        display: block;
        width: 0;
        height: 0;
        border: inset 5px;
        border-color: rgba(255, 255, 255, 0.4) transparent transparent transparent;
        border-top-style: solid;
        margin-top: -2.5px;
        top: 30px; }
    /* line 483, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-dropdown.moved {
      position: relative; }
      /* line 484, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .has-dropdown.moved > .dropdown {
        display: none; }
    /* line 488, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-dropdown.hover > .dropdown, .top-bar-section .has-dropdown.not-click:hover > .dropdown {
      display: block; }
    /* line 495, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-dropdown .dropdown li.has-dropdown > a:after {
      border: none;
      content: "\00bb";
      top: 1rem;
      margin-top: -2px;
      left: 5px; }
    /* line 507, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .dropdown {
      right: 0;
      top: auto;
      background: transparent;
      min-width: 100%; }
      /* line 514, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .dropdown li a {
        color: #555555;
        line-height: 1;
        white-space: nowrap;
        padding: 12px 20px;
        background: #1f3c52; }
      /* line 522, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .dropdown li label {
        white-space: nowrap;
        background: #333333; }
      /* line 528, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section .dropdown li .dropdown {
        right: 100%;
        top: 0; }
    /* line 536, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section > ul > .divider, .top-bar-section > ul > [role="separator"] {
      border-bottom: none;
      border-top: none;
      border-left: solid 1px #3678ab;
      clear: none;
      height: 60px;
      width: 0; }
    /* line 545, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section .has-form {
      background: #2a5d84;
      padding: 0 20px;
      height: 60px; }
    /* line 553, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
    .top-bar-section ul.right li .dropdown {
      left: auto;
      right: 0; }
      /* line 557, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
      .top-bar-section ul.right li .dropdown li .dropdown {
        right: 100%; }

  /* line 567, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .no-js .top-bar-section ul li:hover > a {
    background: #1f3c52;
    color: white; }
  /* line 573, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .no-js .top-bar-section ul li:active > a {
    background: #1f3c52;
    color: white; }
  /* line 581, /app/bower_components/foundation/scss/foundation/components/_top-bar.scss */
  .no-js .top-bar-section .has-dropdown:hover > .dropdown {
    display: block; } }
/* line 67, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.off-canvas-wrap, .inner-wrap, nav.tab-bar, .left-off-canvas-menu, .left-off-canvas-menu *, .right-off-canvas-menu, .move-right a.exit-off-canvas, .move-left a.exit-off-canvas {
  -webkit-backface-visibility: hidden; }

/* line 73, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.off-canvas-wrap, .inner-wrap {
  position: relative;
  width: 100%; }

/* line 79, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.left-off-canvas-menu, .right-off-canvas-menu {
  width: 250px;
  top: 0;
  bottom: 0;
  height: 100%;
  position: absolute;
  overflow-y: auto;
  background: #333333;
  z-index: 1001;
  box-sizing: content-box; }

/* line 150, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
section.left-small, section.right-small {
  width: 2.8125rem;
  height: 2.8125rem;
  position: absolute;
  top: 0; }

/* line 270, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.off-canvas-wrap {
  overflow: hidden; }

/* line 271, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.inner-wrap {
  *zoom: 1;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease; }
  /* line 165, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .inner-wrap:before, .inner-wrap:after {
    content: " ";
    display: table; }
  /* line 166, /app/bower_components/foundation/scss/foundation/components/_global.scss */
  .inner-wrap:after {
    clear: both; }

/* line 273, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
nav.tab-bar {
  background: #333333;
  color: white;
  height: 2.8125rem;
  line-height: 2.8125rem;
  position: relative; }
  /* line 139, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  nav.tab-bar h1, nav.tab-bar h2, nav.tab-bar h3, nav.tab-bar h4, nav.tab-bar h5, nav.tab-bar h6 {
    color: white;
    font-weight: bold;
    line-height: 2.8125rem;
    margin: 0; }
  /* line 145, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  nav.tab-bar h1, nav.tab-bar h2, nav.tab-bar h3, nav.tab-bar h4 {
    font-size: 1.125rem; }

/* line 275, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
section.left-small {
  border-right: solid 1px #1a1a1a;
  box-shadow: 1px 0 0 #4d4d4d;
  left: 0; }

/* line 276, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
section.right-small {
  border-left: solid 1px #4d4d4d;
  box-shadow: -1px 0 0 #1a1a1a;
  right: 0; }

/* line 278, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
section.tab-bar-section {
  padding: 0 0.625rem;
  position: absolute;
  text-align: center;
  height: 2.8125rem;
  top: 0; }
  @media only screen and (min-width: 40.063em) {
    /* line 278, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
    section.tab-bar-section {
      text-align: left; } }
  /* line 182, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  section.tab-bar-section.left {
    left: 0;
    right: 2.8125rem; }
  /* line 186, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  section.tab-bar-section.right {
    left: 2.8125rem;
    right: 0; }
  /* line 190, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  section.tab-bar-section.middle {
    left: 2.8125rem;
    right: 2.8125rem; }

/* line 282, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
a.menu-icon {
  text-indent: 2.1875rem;
  width: 2.8125rem;
  height: 2.8125rem;
  display: block;
  line-height: 2.0625rem;
  padding: 0;
  color: white;
  position: relative; }
  /* line 293, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  a.menu-icon span {
    position: absolute;
    display: block;
    width: 1rem;
    height: 0;
    left: 0.8125rem;
    top: 0.3125rem;
    -webkit-box-shadow: 0 10px 0 1px white, 0 16px 0 1px white, 0 22px 0 1px white;
    box-shadow: 0 10px 0 1px white, 0 16px 0 1px white, 0 22px 0 1px white; }
  /* line 313, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  a.menu-icon:hover span {
    -webkit-box-shadow: 0 10px 0 1px #b3b3b3, 0 16px 0 1px #b3b3b3, 0 22px 0 1px #b3b3b3;
    box-shadow: 0 10px 0 1px #b3b3b3, 0 16px 0 1px #b3b3b3, 0 22px 0 1px #b3b3b3; }

/* line 325, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.left-off-canvas-menu {
  -webkit-transform: translate3d(-100%, 0, 0);
  -moz-transform: translate3d(-100%, 0, 0);
  -ms-transform: translate3d(-100%, 0, 0);
  -o-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0); }

/* line 326, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.right-off-canvas-menu {
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  -ms-transform: translate3d(100%, 0, 0);
  -o-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
  right: 0; }

/* line 328, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
ul.off-canvas-list {
  list-style-type: none;
  padding: 0;
  margin: 0; }
  /* line 204, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  ul.off-canvas-list li label {
    padding: 0.3rem 0.9375rem;
    color: #999999;
    text-transform: uppercase;
    font-weight: bold;
    background: #444444;
    border-top: 1px solid #5e5e5e;
    border-bottom: none;
    margin: 0; }
  /* line 214, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
  ul.off-canvas-list li a {
    display: block;
    padding: 0.66667rem;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: 1px solid #262626; }

/* line 334, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.move-right > .inner-wrap {
  -webkit-transform: translate3d(250px, 0, 0);
  -moz-transform: translate3d(250px, 0, 0);
  -ms-transform: translate3d(250px, 0, 0);
  -o-transform: translate3d(250px, 0, 0);
  transform: translate3d(250px, 0, 0); }
/* line 337, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.move-right a.exit-off-canvas {
  transition: background 300ms ease;
  cursor: pointer;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  display: block;
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1002; }
  @media only screen and (min-width: 40.063em) {
    /* line 244, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
    .move-right a.exit-off-canvas:hover {
      background: rgba(255, 255, 255, 0.05); } }

/* line 341, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.move-left > .inner-wrap {
  -webkit-transform: translate3d(-250px, 0, 0);
  -moz-transform: translate3d(-250px, 0, 0);
  -ms-transform: translate3d(-250px, 0, 0);
  -o-transform: translate3d(-250px, 0, 0);
  transform: translate3d(-250px, 0, 0); }
/* line 345, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.move-left a.exit-off-canvas {
  transition: background 300ms ease;
  cursor: pointer;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  display: block;
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1002; }
  @media only screen and (min-width: 40.063em) {
    /* line 244, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
    .move-left a.exit-off-canvas:hover {
      background: rgba(255, 255, 255, 0.05); } }

/* line 353, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.lt-ie10 .left-off-canvas-menu {
  left: -250px; }
/* line 354, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.lt-ie10 .right-off-canvas-menu {
  right: -250px; }
/* line 357, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.lt-ie10 .move-left > .inner-wrap {
  right: 250px; }
/* line 358, /app/bower_components/foundation/scss/foundation/components/_offcanvas.scss */
.lt-ie10 .move-right > .inner-wrap {
  left: 250px; }

/* Foundation Visibility HTML Classes */
/* line 27, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.show-for-small,
.show-for-small-only,
.show-for-medium-down,
.show-for-large-down,
.hide-for-medium,
.hide-for-medium-up,
.hide-for-medium-only,
.hide-for-large,
.hide-for-large-up,
.hide-for-large-only,
.hide-for-xlarge,
.hide-for-xlarge-up,
.hide-for-xlarge-only,
.hide-for-xxlarge-up,
.hide-for-xxlarge-only {
  display: inherit !important; }

/* line 43, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.hide-for-small,
.hide-for-small-only,
.hide-for-medium-down,
.show-for-medium,
.show-for-medium-up,
.show-for-medium-only,
.hide-for-large-down,
.show-for-large,
.show-for-large-up,
.show-for-large-only,
.show-for-xlarge,
.show-for-xlarge-up,
.show-for-xlarge-only,
.show-for-xxlarge-up,
.show-for-xxlarge-only {
  display: none !important; }

/* Specific visibility for tables */
/* line 61, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
table.show-for-small, table.show-for-small-only, table.show-for-medium-down, table.show-for-large-down, table.hide-for-medium, table.hide-for-medium-up, table.hide-for-medium-only, table.hide-for-large, table.hide-for-large-up, table.hide-for-large-only, table.hide-for-xlarge, table.hide-for-xlarge-up, table.hide-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
  display: table; }

/* line 78, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
thead.show-for-small, thead.show-for-small-only, thead.show-for-medium-down, thead.show-for-large-down, thead.hide-for-medium, thead.hide-for-medium-up, thead.hide-for-medium-only, thead.hide-for-large, thead.hide-for-large-up, thead.hide-for-large-only, thead.hide-for-xlarge, thead.hide-for-xlarge-up, thead.hide-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
  display: table-header-group !important; }

/* line 95, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
tbody.show-for-small, tbody.show-for-small-only, tbody.show-for-medium-down, tbody.show-for-large-down, tbody.hide-for-medium, tbody.hide-for-medium-up, tbody.hide-for-medium-only, tbody.hide-for-large, tbody.hide-for-large-up, tbody.hide-for-large-only, tbody.hide-for-xlarge, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
  display: table-row-group !important; }

/* line 112, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
tr.show-for-small, tr.show-for-small-only, tr.show-for-medium-down, tr.show-for-large-down, tr.hide-for-medium, tr.hide-for-medium-up, tr.hide-for-medium-only, tr.hide-for-large, tr.hide-for-large-up, tr.hide-for-large-only, tr.hide-for-xlarge, tr.hide-for-xlarge-up, tr.hide-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
  display: table-row !important; }

/* line 126, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
td.show-for-small, td.show-for-small-only, td.show-for-medium-down
td.show-for-large-down, td.hide-for-medium, td.hide-for-medium-up, td.hide-for-large, td.hide-for-large-up, td.hide-for-xlarge
td.hide-for-xlarge-up, td.hide-for-xxlarge-up,
th.show-for-small,
th.show-for-small-only,
th.show-for-medium-down
th.show-for-large-down,
th.hide-for-medium,
th.hide-for-medium-up,
th.hide-for-large,
th.hide-for-large-up,
th.hide-for-xlarge
th.hide-for-xlarge-up,
th.hide-for-xxlarge-up {
  display: table-cell !important; }

/* Medium Displays: 641px and up */
@media only screen and (min-width: 40.063em) {
  /* line 144, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .hide-for-small,
  .hide-for-small-only,
  .show-for-medium,
  .show-for-medium-down,
  .show-for-medium-up,
  .show-for-medium-only,
  .hide-for-large,
  .hide-for-large-up,
  .hide-for-large-only,
  .hide-for-xlarge,
  .hide-for-xlarge-up,
  .hide-for-xlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge-only {
    display: inherit !important; }

  /* line 160, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .show-for-small,
  .show-for-small-only,
  .hide-for-medium,
  .hide-for-medium-down,
  .hide-for-medium-up,
  .hide-for-medium-only,
  .hide-for-large-down,
  .show-for-large,
  .show-for-large-up,
  .show-for-large-only,
  .show-for-xlarge,
  .show-for-xlarge-up,
  .show-for-xlarge-only,
  .show-for-xxlarge-up,
  .show-for-xxlarge-only {
    display: none !important; }

  /* Specific visibility for tables */
  /* line 177, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  table.hide-for-small, table.hide-for-small-only, table.show-for-medium, table.show-for-medium-down, table.show-for-medium-up, table.show-for-medium-only, table.hide-for-large, table.hide-for-large-up, table.hide-for-large-only, table.hide-for-xlarge, table.hide-for-xlarge-up, table.hide-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table; }

  /* line 193, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  thead.hide-for-small, thead.hide-for-small-only, thead.show-for-medium, thead.show-for-medium-down, thead.show-for-medium-up, thead.show-for-medium-only, thead.hide-for-large, thead.hide-for-large-up, thead.hide-for-large-only, thead.hide-for-xlarge, thead.hide-for-xlarge-up, thead.hide-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important; }

  /* line 209, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tbody.hide-for-small, tbody.hide-for-small-only, tbody.show-for-medium, tbody.show-for-medium-down, tbody.show-for-medium-up, tbody.show-for-medium-only, tbody.hide-for-large, tbody.hide-for-large-up, tbody.hide-for-large-only, tbody.hide-for-xlarge, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important; }

  /* line 225, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tr.hide-for-small, tr.hide-for-small-only, tr.show-for-medium, tr.show-for-medium-down, tr.show-for-medium-up, tr.show-for-medium-only, tr.hide-for-large, tr.hide-for-large-up, tr.hide-for-large-only, tr.hide-for-xlarge, tr.hide-for-xlarge-up, tr.hide-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important; }

  /* line 242, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  td.hide-for-small, td.hide-for-small-only, td.show-for-medium, td.show-for-medium-down, td.show-for-medium-up, td.show-for-medium-only, td.hide-for-large, td.hide-for-large-up, td.hide-for-large-only, td.hide-for-xlarge, td.hide-for-xlarge-up, td.hide-for-xlarge-only, td.hide-for-xxlarge-up, td.hide-for-xxlarge-only,
  th.hide-for-small,
  th.hide-for-small-only,
  th.show-for-medium,
  th.show-for-medium-down,
  th.show-for-medium-up,
  th.show-for-medium-only,
  th.hide-for-large,
  th.hide-for-large-up,
  th.hide-for-large-only,
  th.hide-for-xlarge,
  th.hide-for-xlarge-up,
  th.hide-for-xlarge-only,
  th.hide-for-xxlarge-up,
  th.hide-for-xxlarge-only {
    display: table-cell !important; } }
/* Large Displays: 1024px and up */
@media only screen and (min-width: 64.063em) {
  /* line 261, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .hide-for-small,
  .hide-for-small-only,
  .hide-for-medium,
  .hide-for-medium-down,
  .hide-for-medium-only,
  .show-for-medium-up,
  .show-for-large,
  .show-for-large-up,
  .show-for-large-only,
  .hide-for-xlarge,
  .hide-for-xlarge-up,
  .hide-for-xlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge-only {
    display: inherit !important; }

  /* line 274, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .show-for-small-only,
  .show-for-medium,
  .show-for-medium-down,
  .show-for-medium-only,
  .hide-for-large,
  .hide-for-large-up,
  .hide-for-large-only,
  .show-for-xlarge,
  .show-for-xlarge-up,
  .show-for-xlarge-only,
  .show-for-xxlarge-up,
  .show-for-xxlarge-only {
    display: none !important; }

  /* Specific visilbity for tables */
  /* line 291, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  table.hide-for-small, table.hide-for-small-only, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-medium-only, table.show-for-medium-up, table.show-for-large, table.show-for-large-up, table.show-for-large-only, table.hide-for-xlarge, table.hide-for-xlarge-up, table.hide-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table; }

  /* line 307, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  thead.hide-for-small, thead.hide-for-small-only, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.show-for-large, thead.show-for-large-up, thead.show-for-large-only, thead.hide-for-xlarge, thead.hide-for-xlarge-up, thead.hide-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important; }

  /* line 323, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tbody.hide-for-small, tbody.hide-for-small-only, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.show-for-large, tbody.show-for-large-up, tbody.show-for-large-only, tbody.hide-for-xlarge, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important; }

  /* line 339, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tr.hide-for-small, tr.hide-for-small-only, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.show-for-large, tr.show-for-large-up, tr.show-for-large-only, tr.hide-for-xlarge, tr.hide-for-xlarge-up, tr.hide-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important; }

  /* line 356, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  td.hide-for-small, td.hide-for-small-only, td.hide-for-medium, td.hide-for-medium-down, td.hide-for-medium-only, td.show-for-medium-up, td.show-for-large, td.show-for-large-up, td.show-for-large-only, td.hide-for-xlarge, td.hide-for-xlarge-up, td.hide-for-xlarge-only, td.hide-for-xxlarge-up, td.hide-for-xxlarge-only,
  th.hide-for-small,
  th.hide-for-small-only,
  th.hide-for-medium,
  th.hide-for-medium-down,
  th.hide-for-medium-only,
  th.show-for-medium-up,
  th.show-for-large,
  th.show-for-large-up,
  th.show-for-large-only,
  th.hide-for-xlarge,
  th.hide-for-xlarge-up,
  th.hide-for-xlarge-only,
  th.hide-for-xxlarge-up,
  th.hide-for-xxlarge-only {
    display: table-cell !important; } }
/* X-Large Displays: 1441 and up */
@media only screen and (min-width: 90.063em) {
  /* line 373, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .hide-for-small,
  .hide-for-small-only,
  .hide-for-medium,
  .hide-for-medium-down,
  .hide-for-medium-only,
  .show-for-medium-up,
  .show-for-large-up,
  .show-for-xlarge,
  .show-for-xlarge-up,
  .show-for-xlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge-only {
    display: inherit !important; }

  /* line 386, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .show-for-small-only,
  .show-for-medium,
  .show-for-medium-down,
  .show-for-medium-only,
  .show-for-large,
  .show-for-large-only,
  .show-for-large-down,
  .hide-for-xlarge,
  .hide-for-xlarge-up,
  .hide-for-xlarge-only,
  .show-for-xxlarge-up,
  .show-for-xxlarge-only {
    display: none !important; }

  /* Specific visilbity for tables */
  /* line 401, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  table.hide-for-small, table.hide-for-small-only, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-medium-only, table.show-for-medium-up, table.show-for-large-up, table.show-for-xlarge, table.show-for-xlarge-up, table.show-for-xlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge-only {
    display: table; }

  /* line 415, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  thead.hide-for-small, thead.hide-for-small-only, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.show-for-large-up, thead.show-for-xlarge, thead.show-for-xlarge-up, thead.show-for-xlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge-only {
    display: table-header-group !important; }

  /* line 429, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tbody.hide-for-small, tbody.hide-for-small-only, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.show-for-large-up, tbody.show-for-xlarge, tbody.show-for-xlarge-up, tbody.show-for-xlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge-only {
    display: table-row-group !important; }

  /* line 443, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tr.hide-for-small, tr.hide-for-small-only, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.show-for-large-up, tr.show-for-xlarge, tr.show-for-xlarge-up, tr.show-for-xlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge-only {
    display: table-row !important; }

  /* line 458, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  td.hide-for-small, td.hide-for-small-only, td.hide-for-medium, td.hide-for-medium-down, td.hide-for-medium-only, td.show-for-medium-up, td.show-for-large-up, td.show-for-xlarge, td.show-for-xlarge-up, td.show-for-xlarge-only, td.hide-for-xxlarge-up, td.hide-for-xxlarge-only,
  th.hide-for-small,
  th.hide-for-small-only,
  th.hide-for-medium,
  th.hide-for-medium-down,
  th.hide-for-medium-only,
  th.show-for-medium-up,
  th.show-for-large-up,
  th.show-for-xlarge,
  th.show-for-xlarge-up,
  th.show-for-xlarge-only,
  th.hide-for-xxlarge-up,
  th.hide-for-xxlarge-only {
    display: table-cell !important; } }
/* XX-Large Displays: 1920 and up */
@media only screen and (min-width: 120.063em) {
  /* line 473, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .hide-for-small,
  .hide-for-small-only,
  .hide-for-medium,
  .hide-for-medium-down,
  .hide-for-medium-only,
  .show-for-medium-up,
  .show-for-large-up,
  .show-for-xlarge-up,
  .show-for-xxlarge-up,
  .show-for-xxlarge-only {
    display: inherit !important; }

  /* line 485, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .show-for-small-only,
  .show-for-medium,
  .show-for-medium-down,
  .show-for-medium-only,
  .show-for-large,
  .show-for-large-only,
  .show-for-large-down,
  .hide-for-xlarge,
  .show-for-xlarge-only,
  .hide-for-xxlarge-up,
  .hide-for-xxlarge-only {
    display: none !important; }

  /* Specific visilbity for tables */
  /* line 498, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  table.hide-for-small, table.hide-for-small-only, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-medium-only, table.show-for-medium-up, table.show-for-large-up, table.show-for-xlarge-up, table.show-for-xxlarge-up, table.show-for-xxlarge-only {
    display: table; }

  /* line 510, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  thead.hide-for-small, thead.hide-for-small-only, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.show-for-large-up, thead.show-for-xlarge-up, thead.show-for-xxlarge-up, thead.show-for-xxlarge-only {
    display: table-header-group !important; }

  /* line 522, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tbody.hide-for-small, tbody.hide-for-small-only, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.show-for-large-up, tbody.show-for-xlarge-up, tbody.show-for-xxlarge-up, tbody.show-for-xxlarge-only {
    display: table-row-group !important; }

  /* line 534, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tr.hide-for-small, tr.hide-for-small-only, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.show-for-large-up, tr.show-for-xlarge-up, tr.show-for-xxlarge-up, tr.show-for-xxlarge-only {
    display: table-row !important; }

  /* line 547, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  td.hide-for-small, td.hide-for-small-only, td.hide-for-medium, td.hide-for-medium-down, td.hide-for-medium-only, td.show-for-medium-up, td.show-for-large-up, td.show-for-xlarge-up, td.show-for-xxlarge-up, td.show-for-xxlarge-only,
  th.hide-for-small,
  th.hide-for-small-only,
  th.hide-for-medium,
  th.hide-for-medium-down,
  th.hide-for-medium-only,
  th.show-for-medium-up,
  th.show-for-large-up,
  th.show-for-xlarge-up,
  th.show-for-xxlarge-up,
  th.show-for-xxlarge-only {
    display: table-cell !important; } }
/* Orientation targeting */
/* line 554, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.show-for-landscape,
.hide-for-portrait {
  display: inherit !important; }

/* line 556, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.hide-for-landscape,
.show-for-portrait {
  display: none !important; }

/* Specific visilbity for tables */
/* line 561, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
table.hide-for-landscape, table.show-for-portrait {
  display: table; }

/* line 565, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
thead.hide-for-landscape, thead.show-for-portrait {
  display: table-header-group !important; }

/* line 569, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
tbody.hide-for-landscape, tbody.show-for-portrait {
  display: table-row-group !important; }

/* line 573, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
tr.hide-for-landscape, tr.show-for-portrait {
  display: table-row !important; }

/* line 578, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
td.hide-for-landscape, td.show-for-portrait,
th.hide-for-landscape,
th.show-for-portrait {
  display: table-cell !important; }

@media only screen and (orientation: landscape) {
  /* line 583, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .show-for-landscape,
  .hide-for-portrait {
    display: inherit !important; }

  /* line 585, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .hide-for-landscape,
  .show-for-portrait {
    display: none !important; }

  /* Specific visilbity for tables */
  /* line 590, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  table.show-for-landscape, table.hide-for-portrait {
    display: table; }

  /* line 594, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  thead.show-for-landscape, thead.hide-for-portrait {
    display: table-header-group !important; }

  /* line 598, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tbody.show-for-landscape, tbody.hide-for-portrait {
    display: table-row-group !important; }

  /* line 602, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tr.show-for-landscape, tr.hide-for-portrait {
    display: table-row !important; }

  /* line 607, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  td.show-for-landscape, td.hide-for-portrait,
  th.show-for-landscape,
  th.hide-for-portrait {
    display: table-cell !important; } }
@media only screen and (orientation: portrait) {
  /* line 613, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .show-for-portrait,
  .hide-for-landscape {
    display: inherit !important; }

  /* line 615, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  .hide-for-portrait,
  .show-for-landscape {
    display: none !important; }

  /* Specific visilbity for tables */
  /* line 620, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  table.show-for-portrait, table.hide-for-landscape {
    display: table; }

  /* line 624, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  thead.show-for-portrait, thead.hide-for-landscape {
    display: table-header-group !important; }

  /* line 628, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tbody.show-for-portrait, tbody.hide-for-landscape {
    display: table-row-group !important; }

  /* line 632, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  tr.show-for-portrait, tr.hide-for-landscape {
    display: table-row !important; }

  /* line 637, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
  td.show-for-portrait, td.hide-for-landscape,
  th.show-for-portrait,
  th.hide-for-landscape {
    display: table-cell !important; } }
/* Touch-enabled device targeting */
/* line 642, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.show-for-touch {
  display: none !important; }

/* line 643, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.hide-for-touch {
  display: inherit !important; }

/* line 644, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch .show-for-touch {
  display: inherit !important; }

/* line 645, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch .hide-for-touch {
  display: none !important; }

/* Specific visilbity for tables */
/* line 648, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
table.hide-for-touch {
  display: table; }

/* line 649, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch table.show-for-touch {
  display: table; }

/* line 650, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
thead.hide-for-touch {
  display: table-header-group !important; }

/* line 651, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch thead.show-for-touch {
  display: table-header-group !important; }

/* line 652, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
tbody.hide-for-touch {
  display: table-row-group !important; }

/* line 653, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch tbody.show-for-touch {
  display: table-row-group !important; }

/* line 654, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
tr.hide-for-touch {
  display: table-row !important; }

/* line 655, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch tr.show-for-touch {
  display: table-row !important; }

/* line 656, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
td.hide-for-touch {
  display: table-cell !important; }

/* line 657, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch td.show-for-touch {
  display: table-cell !important; }

/* line 658, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
th.hide-for-touch {
  display: table-cell !important; }

/* line 659, /app/bower_components/foundation/scss/foundation/components/_visibility.scss */
.touch th.show-for-touch {
  display: table-cell !important; }

/* line 616, /app/source-xampp-windows/stylesheets/all.scss */
.top-bar-section > ul > li > a:not(.button) {
  padding: 0 15px;
  /* reduce padding to allow more nav items */ }

/* line 620, /app/source-xampp-windows/stylesheets/all.scss */
.asciidoctor {
  font-size: 1em;
  /*! normalize.css v2.1.2 | MIT License | git.io/normalize */
  /* ========================================================================== HTML5 display definitions ========================================================================== */
  /** Correct `block` display not defined in IE 8/9. */
  /** Correct `inline-block` display not defined in IE 8/9. */
  /** Prevent modern browsers from displaying `audio` without controls. Remove excess height in iOS 5 devices. */
  /** Address `[hidden]` styling not present in IE 8/9. Hide the `template` element in IE, Safari, and Firefox < 22. */
  /* ========================================================================== Base ========================================================================== */
  /** 1. Set default font family to sans-serif. 2. Prevent iOS text size adjust after orientation change, without disabling user zoom. */
  /** Remove default margin. */
  /* ========================================================================== Links ========================================================================== */
  /** Remove the gray background color from active links in IE 10. */
  /** Address `outline` inconsistency between Chrome and other browsers. */
  /** Improve readability when focused and also mouse hovered in all browsers. */
  /* ========================================================================== Typography ========================================================================== */
  /** Address variable `h1` font-size and margin within `section` and `article` contexts in Firefox 4+, Safari 5, and Chrome. */
  /** Address styling not present in IE 8/9, Safari 5, and Chrome. */
  /** Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome. */
  /** Address styling not present in Safari 5 and Chrome. */
  /** Address differences between Firefox and other browsers. */
  /** Address styling not present in IE 8/9. */
  /** Correct font family set oddly in Safari 5 and Chrome. */
  /** Improve readability of pre-formatted text in all browsers. */
  /** Set consistent quote types. */
  /** Address inconsistent and variable font size in all browsers. */
  /** Prevent `sub` and `sup` affecting `line-height` in all browsers. */
  /* ========================================================================== Embedded content ========================================================================== */
  /** Remove border when inside `a` element in IE 8/9. */
  /** Correct overflow displayed oddly in IE 9. */
  /* ========================================================================== Figures ========================================================================== */
  /** Address margin not present in IE 8/9 and Safari 5. */
  /* ========================================================================== Forms ========================================================================== */
  /** Define consistent border, margin, and padding. */
  /** 1. Correct `color` not being inherited in IE 8/9. 2. Remove padding so people aren't caught out if they zero out fieldsets. */
  /** 1. Correct font family not being inherited in all browsers. 2. Correct font size not being inherited in all browsers. 3. Address margins set differently in Firefox 4+, Safari 5, and Chrome. */
  /** Address Firefox 4+ setting `line-height` on `input` using `!important` in the UA stylesheet. */
  /** Address inconsistent `text-transform` inheritance for `button` and `select`. All other form control elements do not inherit `text-transform` values. Correct `button` style inheritance in Chrome, Safari 5+, and IE 8+. Correct `select` style inheritance in Firefox 4+ and Opera. */
  /** 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio` and `video` controls. 2. Correct inability to style clickable `input` types in iOS. 3. Improve usability and consistency of cursor style between image-type `input` and others. */
  /** Re-set default cursor for disabled elements. */
  /** 1. Address box sizing set to `content-box` in IE 8/9. 2. Remove excess padding in IE 8/9. */
  /** 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome. 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome (include `-moz` to future-proof). */
  /** Remove inner padding and search cancel button in Safari 5 and Chrome on OS X. */
  /** Remove inner padding and border in Firefox 4+. */
  /** 1. Remove default vertical scrollbar in IE 8/9. 2. Improve readability and alignment in all browsers. */
  /* ========================================================================== Tables ========================================================================== */
  /** Remove most spacing between table cells. */
  /* Typography resets */
  /* Default Link Styles */
  /* Default paragraph styles */
  /* Default header styles */
  /* Helpful Typography Defaults */
  /* Lists */
  /* Unordered Lists */
  /* Ordered Lists */
  /* Definition Lists */
  /* Abbreviations */
  /* Blockquotes */
  /* Microformats */
  /* Tables */
  /* use foundation sizes */ }
  /* line 5, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor article, .asciidoctor aside, .asciidoctor details, .asciidoctor figcaption, .asciidoctor figure, .asciidoctor footer, .asciidoctor header, .asciidoctor hgroup, .asciidoctor main, .asciidoctor nav, .asciidoctor section, .asciidoctor summary {
    display: block; }
  /* line 8, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor audio, .asciidoctor canvas, .asciidoctor video {
    display: inline-block; }
  /* line 11, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor audio:not([controls]) {
    display: none;
    height: 0; }
  /* line 14, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor [hidden], .asciidoctor template {
    display: none; }
  /* line 16, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor script {
    display: none !important; }
  /* line 20, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor html {
    font-family: sans-serif;
    /* 1 */
    -ms-text-size-adjust: 100%;
    /* 2 */
    -webkit-text-size-adjust: 100%;
    /* 2 */ }
  /* line 23, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor body {
    margin: 0; }
  /* line 27, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a {
    background: transparent; }
  /* line 30, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a:focus {
    outline: thin dotted; }
  /* line 33, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a:active, .asciidoctor a:hover {
    outline: 0; }
  /* line 37, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1 {
    font-size: 2em;
    margin: 0.67em 0; }
  /* line 40, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor abbr[title] {
    border-bottom: 1px dotted; }
  /* line 43, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor b, .asciidoctor strong {
    font-weight: bold; }
  /* line 46, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor dfn {
    font-style: italic; }
  /* line 49, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor hr {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    height: 0; }
  /* line 52, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor mark {
    background: #ff0;
    color: #000; }
  /* line 55, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor code, .asciidoctor kbd, .asciidoctor pre, .asciidoctor samp {
    font-family: monospace, serif;
    font-size: 1em; }
  /* line 58, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor pre {
    white-space: pre-wrap; }
  /* line 61, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor q {
    quotes: "\201C" "\201D" "\2018" "\2019"; }
  /* line 64, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor small {
    font-size: 80%; }
  /* line 67, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor sub, .asciidoctor sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline; }
  /* line 69, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor sup {
    top: -0.5em; }
  /* line 71, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor sub {
    bottom: -0.25em; }
  /* line 75, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor img {
    border: 0; }
  /* line 78, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor svg:not(:root) {
    overflow: hidden; }
  /* line 82, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor figure {
    margin: 0; }
  /* line 86, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em; }
  /* line 89, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor legend {
    border: 0;
    /* 1 */
    padding: 0;
    /* 2 */ }
  /* line 92, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor button, .asciidoctor input, .asciidoctor select, .asciidoctor textarea {
    font-family: inherit;
    /* 1 */
    font-size: 100%;
    /* 2 */
    margin: 0;
    /* 3 */ }
  /* line 95, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor button, .asciidoctor input {
    line-height: normal; }
  /* line 98, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor button, .asciidoctor select {
    text-transform: none; }
  /* line 101, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor button, .asciidoctor html input[type="button"], .asciidoctor input[type="reset"], .asciidoctor input[type="submit"] {
    -webkit-appearance: button;
    /* 2 */
    cursor: pointer;
    /* 3 */ }
  /* line 104, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor button[disabled], .asciidoctor html input[disabled] {
    cursor: default; }
  /* line 107, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor input[type="checkbox"], .asciidoctor input[type="radio"] {
    box-sizing: border-box;
    /* 1 */
    padding: 0;
    /* 2 */ }
  /* line 110, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor input[type="search"] {
    -webkit-appearance: textfield;
    /* 1 */
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    /* 2 */
    box-sizing: content-box; }
  /* line 113, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor input[type="search"]::-webkit-search-cancel-button, .asciidoctor input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none; }
  /* line 116, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor button::-moz-focus-inner, .asciidoctor input::-moz-focus-inner {
    border: 0;
    padding: 0; }
  /* line 119, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor textarea {
    overflow: auto;
    /* 1 */
    vertical-align: top;
    /* 2 */ }
  /* line 123, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table {
    border-collapse: collapse;
    border-spacing: 0; }
  /* line 125, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor meta.foundation-mq-small {
    font-family: "only screen and (min-width: 768px)";
    width: 768px; }
  /* line 127, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor meta.foundation-mq-medium {
    font-family: "only screen and (min-width:1280px)";
    width: 1280px; }
  /* line 129, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor meta.foundation-mq-large {
    font-family: "only screen and (min-width:1440px)";
    width: 1440px; }
  /* line 131, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor *, .asciidoctor *:before, .asciidoctor *:after {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box; }
  /* line 133, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor html, .asciidoctor body {
    font-size: 100%; }
  /* line 135, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor body {
    background: white;
    color: rgba(0, 0, 0, 0.8);
    padding: 0;
    margin: 0;
    font-family: "Noto Serif", "DejaVu Serif", serif;
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    position: relative;
    cursor: auto; }
  /* line 137, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a:hover {
    cursor: pointer; }
  /* line 139, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor img, .asciidoctor object, .asciidoctor embed {
    max-width: 100%;
    height: auto; }
  /* line 141, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor object, .asciidoctor embed {
    height: 100%; }
  /* line 143, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor img {
    -ms-interpolation-mode: bicubic; }
  /* line 145, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #map_canvas img, .asciidoctor #map_canvas embed, .asciidoctor #map_canvas object, .asciidoctor .map_canvas img, .asciidoctor .map_canvas embed, .asciidoctor .map_canvas object {
    max-width: none !important; }
  /* line 147, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .left {
    float: left !important; }
  /* line 149, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .right, .asciidoctor body[class$="about"] dl dd img, body[class$="about"] dl dd .asciidoctor img {
    float: right !important; }
  /* line 151, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .text-left {
    text-align: left !important; }
  /* line 153, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .text-right {
    text-align: right !important; }
  /* line 155, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .text-center {
    text-align: center !important; }
  /* line 157, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .text-justify {
    text-align: justify !important; }
  /* line 159, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .hide {
    display: none; }
  /* line 161, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .antialiased, .asciidoctor body {
    -webkit-font-smoothing: antialiased; }
  /* line 163, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor img {
    display: inline-block;
    vertical-align: middle; }
  /* line 165, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor textarea {
    height: auto;
    min-height: 50px; }
  /* line 167, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor select {
    width: 100%; }
  /* line 169, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p.lead, .asciidoctor .paragraph.lead > p, .asciidoctor #preamble > .sectionbody > .paragraph:first-of-type p {
    font-size: 1.21875em;
    line-height: 1.6; }
  /* line 171, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .subheader, .asciidoctor .admonitionblock td.content > .title, .asciidoctor .admonitionblock ul#downloads > li td.notes > .title, ul#downloads > li .asciidoctor .admonitionblock td.notes > .title, .asciidoctor .audioblock > .title, .asciidoctor .exampleblock > .title, .asciidoctor .imageblock > .title, .asciidoctor .listingblock > .title, .asciidoctor .literalblock > .title, .asciidoctor .stemblock > .title, .asciidoctor .openblock > .title, .asciidoctor .paragraph > .title, .asciidoctor .quoteblock > .title, .asciidoctor table.tableblock > .title, .asciidoctor .verseblock > .title, .asciidoctor .videoblock > .title, .asciidoctor .dlist > .title, .asciidoctor .olist > .title, .asciidoctor .ulist > .title, .asciidoctor .qlist > .title, .asciidoctor .hdlist > .title {
    line-height: 1.45;
    color: #7a2518;
    font-weight: normal;
    margin-top: 0;
    margin-bottom: 0.25em; }
  /* line 174, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor div, .asciidoctor dl, .asciidoctor dt, .asciidoctor dd, .asciidoctor ul, .asciidoctor ol, .asciidoctor li, .asciidoctor h1, .asciidoctor h2, .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor h4, .asciidoctor h5, .asciidoctor h6, .asciidoctor pre, .asciidoctor form, .asciidoctor p, .asciidoctor blockquote, .asciidoctor th, .asciidoctor td {
    margin: 0;
    padding: 0;
    direction: ltr; }
  /* line 177, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a {
    color: #2156a5;
    text-decoration: underline;
    line-height: inherit; }
  /* line 178, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a:hover, .asciidoctor a:focus {
    color: #1d4b8f; }
  /* line 179, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a img {
    border: none; }
  /* line 182, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p {
    font-family: inherit;
    font-weight: normal;
    font-size: 1em;
    line-height: 1.6;
    margin-bottom: 1.25em;
    text-rendering: optimizeLegibility; }
  /* line 183, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p aside {
    font-size: 0.875em;
    line-height: 1.35;
    font-style: italic; }
  /* line 186, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1, .asciidoctor h2, .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor h4, .asciidoctor h5, .asciidoctor h6 {
    font-family: "Open Sans", "DejaVu Sans", sans-serif;
    font-weight: 300;
    font-style: normal;
    color: #ba3925;
    text-rendering: optimizeLegibility;
    margin-top: 1em;
    margin-bottom: 0.5em;
    line-height: 1.0125em; }
  /* line 187, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1 small, .asciidoctor h2 small, .asciidoctor h3 small, .asciidoctor #toctitle small, .asciidoctor .sidebarblock > .content > .title small, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title small, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title small, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title small, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title small, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title small, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title small, .asciidoctor h4 small, .asciidoctor h5 small, .asciidoctor h6 small {
    font-size: 60%;
    color: #e99b8f;
    line-height: 0; }
  /* line 189, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1 {
    font-size: 2.125em; }
  /* line 191, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h2 {
    font-size: 1.6875em; }
  /* line 193, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title {
    font-size: 1.375em; }
  /* line 195, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h4 {
    font-size: 1.125em; }
  /* line 197, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h5 {
    font-size: 1.125em; }
  /* line 199, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h6 {
    font-size: 1em; }
  /* line 201, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor hr {
    border: solid #ddddd8;
    border-width: 1px 0 0;
    clear: both;
    margin: 1.25em 0 1.1875em;
    height: 0; }
  /* line 204, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor em, .asciidoctor i {
    font-style: italic;
    line-height: inherit; }
  /* line 206, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor strong, .asciidoctor b {
    font-weight: bold;
    line-height: inherit; }
  /* line 208, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor small {
    font-size: 60%;
    line-height: inherit; }
  /* line 210, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor code {
    font-family: "Droid Sans Mono", "DejaVu Sans Mono", "Monospace", monospace;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.9); }
  /* line 213, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul, .asciidoctor ol, .asciidoctor dl {
    font-size: 1em;
    line-height: 1.6;
    margin-bottom: 1.25em;
    list-style-position: outside;
    font-family: inherit; }
  /* line 215, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul, .asciidoctor ol {
    margin-left: 1.5em; }
  /* line 216, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.no-bullet, .asciidoctor ul.social, .asciidoctor ul#downloads, .asciidoctor ul#docs-list, .asciidoctor ol.no-bullet, .asciidoctor ol.social {
    margin-left: 1.5em; }
  /* line 219, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul li ul, .asciidoctor ul li ol {
    margin-left: 1.25em;
    margin-bottom: 0;
    font-size: 1em;
    /* Override nested font-size change */ }
  /* line 220, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.square li ul, .asciidoctor ul.circle li ul, .asciidoctor ul.disc li ul {
    list-style: inherit; }
  /* line 221, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.square {
    list-style-type: square; }
  /* line 222, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.circle {
    list-style-type: circle; }
  /* line 223, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.disc {
    list-style-type: disc; }
  /* line 224, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.no-bullet, .asciidoctor ul.social, .asciidoctor ul#downloads, .asciidoctor ul#docs-list {
    list-style: none; }
  /* line 227, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol li ul, .asciidoctor ol li ol {
    margin-left: 1.25em;
    margin-bottom: 0; }
  /* line 230, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor dl dt {
    margin-bottom: 0.3125em;
    font-weight: bold; }
  /* line 231, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor dl dd {
    margin-bottom: 1.25em; }
  /* line 234, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor abbr, .asciidoctor acronym {
    text-transform: uppercase;
    font-size: 90%;
    color: rgba(0, 0, 0, 0.8);
    border-bottom: 1px dotted #dddddd;
    cursor: help; }
  /* line 236, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor abbr {
    text-transform: none; }
  /* line 239, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor blockquote {
    margin: 0 0 1.25em;
    padding: 0.5625em 1.25em 0 1.1875em;
    border-left: 1px solid #dddddd; }
  /* line 240, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor blockquote cite {
    display: block;
    font-size: 0.9375em;
    color: rgba(0, 0, 0, 0.6); }
  /* line 241, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor blockquote cite:before {
    content: "\2014 \0020"; }
  /* line 242, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor blockquote cite a, .asciidoctor blockquote cite a:visited {
    color: rgba(0, 0, 0, 0.6); }
  /* line 244, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor blockquote, .asciidoctor blockquote p {
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.85); }
  /* line 247, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .vcard {
    display: inline-block;
    margin: 0 0 1.25em 0;
    border: 1px solid #dddddd;
    padding: 0.625em 0.75em; }
  /* line 248, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .vcard li {
    margin: 0;
    display: block; }
  /* line 249, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .vcard .fn {
    font-weight: bold;
    font-size: 0.9375em; }
  /* line 251, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .vevent .summary {
    font-weight: bold; }
  /* line 252, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .vevent abbr {
    cursor: auto;
    text-decoration: none;
    font-weight: bold;
    border: none;
    padding: 0 0.0625em; }
  @media only screen and (min-width: 768px) {
    /* line 254, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor h1, .asciidoctor h2, .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor h4, .asciidoctor h5, .asciidoctor h6 {
      line-height: 1.2; }
    /* line 255, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor h1 {
      font-size: 2.75em; }
    /* line 256, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor h2 {
      font-size: 2.3125em; }
    /* line 257, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title {
      font-size: 1.6875em; }
    /* line 258, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor h4 {
      font-size: 1.4375em; } }
  /* line 260, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table {
    background: white;
    margin-bottom: 1.25em;
    border: solid 1px #dedede; }
  /* line 261, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table thead, .asciidoctor table tfoot {
    background: #f7f8f7;
    font-weight: bold; }
  /* line 262, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table thead tr th, .asciidoctor table thead tr td, .asciidoctor table tfoot tr th, .asciidoctor table tfoot tr td {
    padding: 0.5em 0.625em 0.625em;
    font-size: inherit;
    color: rgba(0, 0, 0, 0.8);
    text-align: left; }
  /* line 263, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table tr th, .asciidoctor table tr td {
    padding: 0.5625em 0.625em;
    font-size: inherit;
    color: rgba(0, 0, 0, 0.8); }
  /* line 264, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table tr.even, .asciidoctor table tr.alt, .asciidoctor table tr:nth-of-type(even) {
    background: #f8f8f7; }
  /* line 265, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table thead tr th, .asciidoctor table tfoot tr th, .asciidoctor table tbody tr td, .asciidoctor table tr td, .asciidoctor table tfoot tr td {
    display: table-cell;
    line-height: 1.6; }
  /* line 267, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1, .asciidoctor h2, .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor h4, .asciidoctor h5, .asciidoctor h6 {
    line-height: 1.2;
    word-spacing: -0.05em; }
  /* line 268, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1 strong, .asciidoctor h2 strong, .asciidoctor h3 strong, .asciidoctor #toctitle strong, .asciidoctor .sidebarblock > .content > .title strong, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title strong, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title strong, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title strong, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title strong, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title strong, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title strong, .asciidoctor h4 strong, .asciidoctor h5 strong, .asciidoctor h6 strong {
    font-weight: 400; }
  /* line 270, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .clearfix:before, .asciidoctor #breaking div:before, #breaking .asciidoctor div:before, .asciidoctor body[class$="about"] dl dd:before, body[class$="about"] dl .asciidoctor dd:before, .asciidoctor .clearfix:after, .asciidoctor #breaking div:after, #breaking .asciidoctor div:after, .asciidoctor body[class$="about"] dl dd:after, body[class$="about"] dl .asciidoctor dd:after, .asciidoctor .float-group:before, .asciidoctor .float-group:after {
    content: " ";
    display: table; }
  /* line 271, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .clearfix:after, .asciidoctor #breaking div:after, #breaking .asciidoctor div:after, .asciidoctor body[class$="about"] dl dd:after, body[class$="about"] dl .asciidoctor dd:after, .asciidoctor .float-group:after {
    clear: both; }
  /* line 273, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor *:not(pre) > code {
    font-size: 0.9375em;
    font-style: normal !important;
    letter-spacing: 0;
    padding: 0.1em 0.5ex;
    word-spacing: -0.15em;
    background-color: #f7f7f8;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    line-height: 1.45;
    text-rendering: optimizeSpeed; }
  /* line 275, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor pre, .asciidoctor pre > code {
    line-height: 1.45;
    color: rgba(0, 0, 0, 0.9);
    font-family: "Droid Sans Mono", "DejaVu Sans Mono", "Monospace", monospace;
    font-weight: normal;
    text-rendering: optimizeSpeed; }
  /* line 277, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .keyseq {
    color: rgba(51, 51, 51, 0.8); }
  /* line 279, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor kbd {
    display: inline-block;
    color: rgba(0, 0, 0, 0.8);
    font-size: 0.75em;
    line-height: 1.4;
    background-color: #f7f7f7;
    border: 1px solid #ccc;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), 0 0 0 0.1em white inset;
    margin: -0.15em 0.15em 0 0.15em;
    padding: 0.2em 0.6em 0.2em 0.5em;
    vertical-align: middle;
    white-space: nowrap; }
  /* line 281, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .keyseq kbd:first-child {
    margin-left: 0; }
  /* line 283, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .keyseq kbd:last-child {
    margin-right: 0; }
  /* line 285, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .menuseq, .asciidoctor .menu {
    color: rgba(0, 0, 0, 0.8); }
  /* line 287, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor b.button:before, .asciidoctor b.button:after {
    position: relative;
    top: -1px;
    font-weight: normal; }
  /* line 289, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor b.button:before {
    content: "[";
    padding: 0 3px 0 2px; }
  /* line 291, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor b.button:after {
    content: "]";
    padding: 0 2px 0 3px; }
  /* line 293, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p a > code:hover {
    color: rgba(0, 0, 0, 0.9); }
  /* line 295, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header, .asciidoctor #content, .asciidoctor #footnotes, .asciidoctor #footer {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    margin-bottom: 0;
    max-width: 62.5em;
    *zoom: 1;
    position: relative;
    padding-left: 0.9375em;
    padding-right: 0.9375em; }
  /* line 296, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header:before, .asciidoctor #header:after, .asciidoctor #content:before, .asciidoctor #content:after, .asciidoctor #footnotes:before, .asciidoctor #footnotes:after, .asciidoctor #footer:before, .asciidoctor #footer:after {
    content: " ";
    display: table; }
  /* line 297, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header:after, .asciidoctor #content:after, .asciidoctor #footnotes:after, .asciidoctor #footer:after {
    clear: both; }
  /* line 299, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content {
    margin-top: 1.25em; }
  /* line 301, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content:before {
    content: none; }
  /* line 303, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header > h1:first-child {
    color: rgba(0, 0, 0, 0.85);
    margin-top: 2.25rem;
    margin-bottom: 0; }
  /* line 304, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header > h1:first-child + #toc {
    margin-top: 8px;
    border-top: 1px solid #ddddd8; }
  /* line 305, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header > h1:only-child, .asciidoctor body.toc2 #header > h1:nth-last-child(2) {
    border-bottom: 1px solid #ddddd8;
    padding-bottom: 8px; }
  /* line 306, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details {
    border-bottom: 1px solid #ddddd8;
    line-height: 1.45;
    padding-top: 0.25em;
    padding-bottom: 0.25em;
    padding-left: 0.25em;
    color: rgba(0, 0, 0, 0.6);
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -ms-flex-flow: row wrap;
    -webkit-flex-flow: row wrap;
    flex-flow: row wrap; }
  /* line 307, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details span:first-child {
    margin-left: -0.125em; }
  /* line 308, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details span.email a {
    color: rgba(0, 0, 0, 0.85); }
  /* line 309, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details br {
    display: none; }
  /* line 310, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details br + span:before {
    content: "\00a0\2013\00a0"; }
  /* line 311, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details br + span.author:before {
    content: "\00a0\22c5\00a0";
    color: rgba(0, 0, 0, 0.85); }
  /* line 312, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header .details br + span#revremark:before {
    content: "\00a0|\00a0"; }
  /* line 313, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header #revnumber {
    text-transform: capitalize; }
  /* line 314, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #header #revnumber:after {
    content: "\00a0"; }
  /* line 316, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content > h1:first-child:not([class]) {
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid #ddddd8;
    padding-bottom: 8px;
    margin-top: 0;
    padding-top: 1rem;
    margin-bottom: 1.25rem; }
  /* line 318, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc {
    border-bottom: 1px solid #efefed;
    padding-bottom: 0.5em; }
  /* line 319, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc > ul {
    margin-left: 0.125em; }
  /* line 320, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc ul.sectlevel0 > li > a {
    font-style: italic; }
  /* line 321, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc ul.sectlevel0 ul.sectlevel1 {
    margin: 0.5em 0; }
  /* line 322, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc ul {
    font-family: "Open Sans", "DejaVu Sans", sans-serif;
    list-style-type: none; }
  /* line 323, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc a {
    text-decoration: none; }
  /* line 324, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toc a:active {
    text-decoration: underline; }
  /* line 326, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #toctitle {
    color: #7a2518;
    font-size: 1.2em; }
  @media only screen and (min-width: 768px) {
    /* line 328, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toctitle {
      font-size: 1.375em; }
    /* line 329, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor body.toc2 {
      padding-left: 15em;
      padding-right: 0; }
    /* line 330, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 {
      margin-top: 0 !important;
      background-color: #f8f8f7;
      position: fixed;
      width: 15em;
      left: 0;
      top: 0;
      border-right: 1px solid #efefed;
      border-top-width: 0 !important;
      border-bottom-width: 0 !important;
      z-index: 1000;
      padding: 1.25em 1em;
      height: 100%;
      overflow: auto; }
    /* line 331, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 #toctitle {
      margin-top: 0;
      font-size: 1.2em; }
    /* line 332, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 > ul {
      font-size: 0.9em;
      margin-bottom: 0; }
    /* line 333, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 ul ul {
      margin-left: 0;
      padding-left: 1em; }
    /* line 334, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 ul.sectlevel0 ul.sectlevel1 {
      padding-left: 0;
      margin-top: 0.5em;
      margin-bottom: 0.5em; }
    /* line 335, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor body.toc2.toc-right {
      padding-left: 0;
      padding-right: 15em; }
    /* line 336, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor body.toc2.toc-right #toc.toc2 {
      border-right-width: 0;
      border-left: 1px solid #efefed;
      left: auto;
      right: 0; } }
  @media only screen and (min-width: 1280px) {
    /* line 337, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor body.toc2 {
      padding-left: 20em;
      padding-right: 0; }
    /* line 338, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 {
      width: 20em; }
    /* line 339, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 #toctitle {
      font-size: 1.375em; }
    /* line 340, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 > ul {
      font-size: 0.95em; }
    /* line 341, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor #toc.toc2 ul ul {
      padding-left: 1.25em; }
    /* line 342, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor body.toc2.toc-right {
      padding-left: 0;
      padding-right: 20em; } }
  /* line 343, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content #toc {
    border-style: solid;
    border-width: 1px;
    border-color: #e0e0dc;
    margin-bottom: 1.25em;
    padding: 1.25em;
    background: #f8f8f7;
    -webkit-border-radius: 4px;
    border-radius: 4px; }
  /* line 344, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content #toc > :first-child {
    margin-top: 0; }
  /* line 345, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content #toc > :last-child {
    margin-bottom: 0; }
  /* line 347, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footer {
    max-width: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    padding: 1.25em; }
  /* line 349, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footer-text {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.44; }
  /* line 351, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sect1 {
    padding-bottom: 0.625em; }
  @media only screen and (min-width: 768px) {
    /* line 353, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor .sect1 {
      padding-bottom: 1.25em; } }
  /* line 354, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sect1 + .sect1 {
    border-top: 1px solid #efefed; }
  /* line 356, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content h1 > a.anchor, .asciidoctor h2 > a.anchor, .asciidoctor h3 > a.anchor, .asciidoctor #toctitle > a.anchor, .asciidoctor .sidebarblock > .content > .title > a.anchor, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title > a.anchor, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title > a.anchor, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title > a.anchor, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.anchor, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title > a.anchor, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.anchor, .asciidoctor h4 > a.anchor, .asciidoctor h5 > a.anchor, .asciidoctor h6 > a.anchor {
    position: absolute;
    z-index: 1001;
    width: 1.5ex;
    margin-left: -1.5ex;
    display: block;
    text-decoration: none !important;
    visibility: hidden;
    text-align: center;
    font-weight: normal; }
  /* line 357, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content h1 > a.anchor:before, .asciidoctor h2 > a.anchor:before, .asciidoctor h3 > a.anchor:before, .asciidoctor #toctitle > a.anchor:before, .asciidoctor .sidebarblock > .content > .title > a.anchor:before, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title > a.anchor:before, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title > a.anchor:before, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title > a.anchor:before, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.anchor:before, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title > a.anchor:before, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.anchor:before, .asciidoctor h4 > a.anchor:before, .asciidoctor h5 > a.anchor:before, .asciidoctor h6 > a.anchor:before {
    content: "\00A7";
    font-size: 0.85em;
    display: block;
    padding-top: 0.1em; }
  /* line 358, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content h1:hover > a.anchor, .asciidoctor #content h1 > a.anchor:hover, .asciidoctor h2:hover > a.anchor, .asciidoctor h2 > a.anchor:hover, .asciidoctor h3:hover > a.anchor, .asciidoctor #toctitle:hover > a.anchor, .asciidoctor .sidebarblock > .content > .title:hover > a.anchor, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title:hover > a.anchor, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title:hover > a.anchor, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title:hover > a.anchor, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title:hover > a.anchor, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title:hover > a.anchor, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title:hover > a.anchor, .asciidoctor h3 > a.anchor:hover, .asciidoctor #toctitle > a.anchor:hover, .asciidoctor .sidebarblock > .content > .title > a.anchor:hover, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title > a.anchor:hover, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title > a.anchor:hover, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title > a.anchor:hover, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.anchor:hover, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title > a.anchor:hover, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.anchor:hover, .asciidoctor h4:hover > a.anchor, .asciidoctor h4 > a.anchor:hover, .asciidoctor h5:hover > a.anchor, .asciidoctor h5 > a.anchor:hover, .asciidoctor h6:hover > a.anchor, .asciidoctor h6 > a.anchor:hover {
    visibility: visible; }
  /* line 359, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content h1 > a.link, .asciidoctor h2 > a.link, .asciidoctor h3 > a.link, .asciidoctor #toctitle > a.link, .asciidoctor .sidebarblock > .content > .title > a.link, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title > a.link, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title > a.link, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title > a.link, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.link, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title > a.link, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.link, .asciidoctor h4 > a.link, .asciidoctor h5 > a.link, .asciidoctor h6 > a.link {
    color: #ba3925;
    text-decoration: none; }
  /* line 360, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content h1 > a.link:hover, .asciidoctor h2 > a.link:hover, .asciidoctor h3 > a.link:hover, .asciidoctor #toctitle > a.link:hover, .asciidoctor .sidebarblock > .content > .title > a.link:hover, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title > a.link:hover, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title > a.link:hover, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title > a.link:hover, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.link:hover, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title > a.link:hover, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title > a.link:hover, .asciidoctor h4 > a.link:hover, .asciidoctor h5 > a.link:hover, .asciidoctor h6 > a.link:hover {
    color: #a53221; }
  /* line 362, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .audioblock, .asciidoctor .imageblock, .asciidoctor .literalblock, .asciidoctor .listingblock, .asciidoctor .stemblock, .asciidoctor .videoblock {
    margin-bottom: 1.25em; }
  /* line 364, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.content > .title, .asciidoctor .admonitionblock ul#downloads > li td.notes > .title, ul#downloads > li .asciidoctor .admonitionblock td.notes > .title, .asciidoctor .audioblock > .title, .asciidoctor .exampleblock > .title, .asciidoctor .imageblock > .title, .asciidoctor .listingblock > .title, .asciidoctor .literalblock > .title, .asciidoctor .stemblock > .title, .asciidoctor .openblock > .title, .asciidoctor .paragraph > .title, .asciidoctor .quoteblock > .title, .asciidoctor table.tableblock > .title, .asciidoctor .verseblock > .title, .asciidoctor .videoblock > .title, .asciidoctor .dlist > .title, .asciidoctor .olist > .title, .asciidoctor .ulist > .title, .asciidoctor .qlist > .title, .asciidoctor .hdlist > .title {
    text-rendering: optimizeLegibility;
    text-align: left;
    font-family: "Noto Serif", "DejaVu Serif", serif;
    font-size: 1rem;
    font-style: italic; }
  /* line 366, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.tableblock > caption.title {
    white-space: nowrap;
    overflow: visible;
    max-width: 0; }
  /* line 368, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .paragraph.lead > p, .asciidoctor #preamble > .sectionbody > .paragraph:first-of-type p {
    color: rgba(0, 0, 0, 0.85); }
  /* line 370, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.tableblock #preamble > .sectionbody > .paragraph:first-of-type p {
    font-size: inherit; }
  /* line 372, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock > table {
    border-collapse: separate;
    border: 0;
    background: none;
    width: 100%; }
  /* line 373, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock > table td.icon {
    text-align: center;
    width: 80px; }
  /* line 374, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock > table td.icon img {
    max-width: none; }
  /* line 375, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock > table td.icon .title {
    font-weight: bold;
    font-family: "Open Sans", "DejaVu Sans", sans-serif;
    text-transform: uppercase; }
  /* line 376, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock > table td.content, .asciidoctor .admonitionblock > table ul#downloads > li td.notes, ul#downloads > li .asciidoctor .admonitionblock > table td.notes {
    padding-left: 1.125em;
    padding-right: 1.25em;
    border-left: 1px solid #ddddd8;
    color: rgba(0, 0, 0, 0.6); }
  /* line 377, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock > table td.content > :last-child > :last-child, .asciidoctor .admonitionblock > table ul#downloads > li td.notes > :last-child > :last-child, ul#downloads > li .asciidoctor .admonitionblock > table td.notes > :last-child > :last-child {
    margin-bottom: 0; }
  /* line 379, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .exampleblock > .content, .asciidoctor ul#downloads > li .exampleblock > .notes, ul#downloads > li .asciidoctor .exampleblock > .notes, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"], ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"], .asciidoctor #download > div .exampleblock > ul[id$="-download"], #download > div .asciidoctor .exampleblock > ul[id$="-download"] {
    border-style: solid;
    border-width: 1px;
    border-color: #e6e6e6;
    margin-bottom: 1.25em;
    padding: 1.25em;
    background: white;
    -webkit-border-radius: 4px;
    border-radius: 4px; }
  /* line 380, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .exampleblock > .content > :first-child, .asciidoctor ul#downloads > li .exampleblock > .notes > :first-child, ul#downloads > li .asciidoctor .exampleblock > .notes > :first-child, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"] > :first-child, ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"] > :first-child, .asciidoctor #download > div .exampleblock > ul[id$="-download"] > :first-child, #download > div .asciidoctor .exampleblock > ul[id$="-download"] > :first-child {
    margin-top: 0; }
  /* line 381, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .exampleblock > .content > :last-child, .asciidoctor ul#downloads > li .exampleblock > .notes > :last-child, ul#downloads > li .asciidoctor .exampleblock > .notes > :last-child, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"] > :last-child, ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"] > :last-child, .asciidoctor #download > div .exampleblock > ul[id$="-download"] > :last-child, #download > div .asciidoctor .exampleblock > ul[id$="-download"] > :last-child {
    margin-bottom: 0; }
  /* line 383, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sidebarblock {
    border-style: solid;
    border-width: 1px;
    border-color: #e0e0dc;
    margin-bottom: 1.25em;
    padding: 1.25em;
    background: #f8f8f7;
    -webkit-border-radius: 4px;
    border-radius: 4px; }
  /* line 384, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sidebarblock > :first-child {
    margin-top: 0; }
  /* line 385, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sidebarblock > :last-child {
    margin-bottom: 0; }
  /* line 386, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title {
    color: #7a2518;
    margin-top: 0;
    text-align: center; }
  /* line 388, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .exampleblock > .content > :last-child > :last-child, .asciidoctor ul#downloads > li .exampleblock > .notes > :last-child > :last-child, ul#downloads > li .asciidoctor .exampleblock > .notes > :last-child > :last-child, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"] > :last-child > :last-child, ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"] > :last-child > :last-child, .asciidoctor #download > div .exampleblock > ul[id$="-download"] > :last-child > :last-child, #download > div .asciidoctor .exampleblock > ul[id$="-download"] > :last-child > :last-child, .asciidoctor .exampleblock > .content .olist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li .exampleblock > .notes .olist > ol > li:last-child > :last-child, ul#downloads > li .asciidoctor .exampleblock > .notes .olist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, .asciidoctor #download > div .exampleblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, #download > div .asciidoctor .exampleblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, .asciidoctor .exampleblock > .content .ulist > ul > li:last-child > :last-child, .asciidoctor ul#downloads > li .exampleblock > .notes .ulist > ul > li:last-child > :last-child, ul#downloads > li .asciidoctor .exampleblock > .notes .ulist > ul > li:last-child > :last-child, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, .asciidoctor #download > div .exampleblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, #download > div .asciidoctor .exampleblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, .asciidoctor .exampleblock > .content .qlist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li .exampleblock > .notes .qlist > ol > li:last-child > :last-child, ul#downloads > li .asciidoctor .exampleblock > .notes .qlist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, .asciidoctor #download > div .exampleblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, #download > div .asciidoctor .exampleblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, .asciidoctor .sidebarblock > .content > :last-child > :last-child, .asciidoctor ul#downloads > li .sidebarblock > .notes > :last-child > :last-child, ul#downloads > li .asciidoctor .sidebarblock > .notes > :last-child > :last-child, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > :last-child > :last-child, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > :last-child > :last-child, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > :last-child > :last-child, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > :last-child > :last-child, .asciidoctor .sidebarblock > .content .olist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li .sidebarblock > .notes .olist > ol > li:last-child > :last-child, ul#downloads > li .asciidoctor .sidebarblock > .notes .olist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] .olist > ol > li:last-child > :last-child, .asciidoctor .sidebarblock > .content .ulist > ul > li:last-child > :last-child, .asciidoctor ul#downloads > li .sidebarblock > .notes .ulist > ul > li:last-child > :last-child, ul#downloads > li .asciidoctor .sidebarblock > .notes .ulist > ul > li:last-child > :last-child, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] .ulist > ul > li:last-child > :last-child, .asciidoctor .sidebarblock > .content .qlist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li .sidebarblock > .notes .qlist > ol > li:last-child > :last-child, ul#downloads > li .asciidoctor .sidebarblock > .notes .qlist > ol > li:last-child > :last-child, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] .qlist > ol > li:last-child > :last-child {
    margin-bottom: 0; }
  /* line 390, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .literalblock pre, .asciidoctor .listingblock pre:not(.highlight), .asciidoctor .listingblock pre[class="highlight"], .asciidoctor .listingblock pre[class^="highlight "], .asciidoctor .listingblock pre.CodeRay, .asciidoctor .listingblock pre.prettyprint {
    background: #f7f7f8; }
  /* line 391, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sidebarblock .literalblock pre, .asciidoctor .sidebarblock .listingblock pre:not(.highlight), .asciidoctor .sidebarblock .listingblock pre[class="highlight"], .asciidoctor .sidebarblock .listingblock pre[class^="highlight "], .asciidoctor .sidebarblock .listingblock pre.CodeRay, .asciidoctor .sidebarblock .listingblock pre.prettyprint {
    background: #f2f1f1; }
  /* line 393, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .literalblock pre, .asciidoctor .literalblock pre[class], .asciidoctor .listingblock pre, .asciidoctor .listingblock pre[class] {
    -webkit-border-radius: 4px;
    border-radius: 4px;
    word-wrap: break-word;
    padding: 1em;
    font-size: 0.8125em; }
  /* line 394, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .literalblock pre.nowrap, .asciidoctor .literalblock pre[class].nowrap, .asciidoctor .listingblock pre.nowrap, .asciidoctor .listingblock pre[class].nowrap {
    overflow-x: auto;
    white-space: pre;
    word-wrap: normal; }
  @media only screen and (min-width: 768px) {
    /* line 395, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor .literalblock pre, .asciidoctor .literalblock pre[class], .asciidoctor .listingblock pre, .asciidoctor .listingblock pre[class] {
      font-size: 0.90625em; } }
  @media only screen and (min-width: 1280px) {
    /* line 396, /app/source-xampp-windows/stylesheets/asciidoctor.css */
    .asciidoctor .literalblock pre, .asciidoctor .literalblock pre[class], .asciidoctor .listingblock pre, .asciidoctor .listingblock pre[class] {
      font-size: 1em; } }
  /* line 398, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .literalblock.output pre {
    color: #f7f7f8;
    background-color: rgba(0, 0, 0, 0.9); }
  /* line 400, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock pre.highlightjs {
    padding: 0; }
  /* line 401, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock pre.highlightjs > code {
    padding: 1em;
    -webkit-border-radius: 4px;
    border-radius: 4px; }
  /* line 403, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock pre.prettyprint {
    border-width: 0; }
  /* line 405, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock > .content, .asciidoctor ul#downloads > li .listingblock > .notes, ul#downloads > li .asciidoctor .listingblock > .notes, .asciidoctor ul#downloads > li table .listingblock > ul[id$="-download"], ul#downloads > li table .asciidoctor .listingblock > ul[id$="-download"], .asciidoctor #download > div .listingblock > ul[id$="-download"], #download > div .asciidoctor .listingblock > ul[id$="-download"] {
    position: relative; }
  /* line 407, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock code[data-lang]:before {
    display: none;
    content: attr(data-lang);
    position: absolute;
    font-size: 0.75em;
    top: 0.425rem;
    right: 0.5rem;
    line-height: 1;
    text-transform: uppercase;
    color: #999; }
  /* line 409, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock:hover code[data-lang]:before {
    display: block; }
  /* line 411, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock.terminal pre .command:before {
    content: attr(data-prompt);
    padding-right: 0.5em;
    color: #999; }
  /* line 413, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .listingblock.terminal pre .command:not([data-prompt]):before {
    content: "$"; }
  /* line 415, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.pyhltable {
    border-collapse: separate;
    border: 0;
    margin-bottom: 0;
    background: none; }
  /* line 417, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.pyhltable td {
    vertical-align: top;
    padding-top: 0;
    padding-bottom: 0; }
  /* line 419, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.pyhltable td.code {
    padding-left: .75em;
    padding-right: 0; }
  /* line 421, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor pre.pygments .lineno, .asciidoctor table.pyhltable td:not(.code) {
    color: #999;
    padding-left: 0;
    padding-right: .5em;
    border-right: 1px solid #ddddd8; }
  /* line 423, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor pre.pygments .lineno {
    display: inline-block;
    margin-right: .25em; }
  /* line 425, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.pyhltable .linenodiv {
    background: none !important;
    padding-right: 0 !important; }
  /* line 427, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock {
    margin: 0 1em 1.25em 1.5em;
    display: table; }
  /* line 428, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock > .title {
    margin-left: -1.5em;
    margin-bottom: 0.75em; }
  /* line 429, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock blockquote, .asciidoctor .quoteblock blockquote p {
    color: rgba(0, 0, 0, 0.85);
    font-size: 1.15rem;
    line-height: 1.75;
    word-spacing: 0.1em;
    letter-spacing: 0;
    font-style: italic;
    text-align: justify; }
  /* line 430, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock blockquote {
    margin: 0;
    padding: 0;
    border: 0; }
  /* line 431, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock blockquote:before {
    content: "\201c";
    float: left;
    font-size: 2.75em;
    font-weight: bold;
    line-height: 0.6em;
    margin-left: -0.6em;
    color: #7a2518;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); }
  /* line 432, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock blockquote > .paragraph:last-child p {
    margin-bottom: 0; }
  /* line 433, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .attribution {
    margin-top: 0.5em;
    margin-right: 0.5ex;
    text-align: right; }
  /* line 434, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .quoteblock {
    margin-left: 0;
    margin-right: 0;
    padding: 0.5em 0;
    border-left: 3px solid rgba(0, 0, 0, 0.6); }
  /* line 435, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .quoteblock blockquote {
    padding: 0 0 0 0.75em; }
  /* line 436, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .quoteblock blockquote:before {
    display: none; }
  /* line 438, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .verseblock {
    margin: 0 1em 1.25em 1em; }
  /* line 439, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .verseblock pre {
    font-family: "Open Sans", "DejaVu Sans", sans;
    font-size: 1.15rem;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 300;
    text-rendering: optimizeLegibility; }
  /* line 440, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .verseblock pre strong {
    font-weight: 400; }
  /* line 441, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .verseblock .attribution {
    margin-top: 1.25rem;
    margin-left: 0.5ex; }
  /* line 443, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .attribution, .asciidoctor .verseblock .attribution {
    font-size: 0.9375em;
    line-height: 1.45;
    font-style: italic; }
  /* line 444, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .attribution br, .asciidoctor .verseblock .attribution br {
    display: none; }
  /* line 445, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock .attribution cite, .asciidoctor .verseblock .attribution cite {
    display: block;
    letter-spacing: -0.05em;
    color: rgba(0, 0, 0, 0.6); }
  /* line 447, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock.abstract {
    margin: 0 0 1.25em 0;
    display: block; }
  /* line 448, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock.abstract blockquote, .asciidoctor .quoteblock.abstract blockquote p {
    text-align: left;
    word-spacing: 0; }
  /* line 449, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .quoteblock.abstract blockquote:before, .asciidoctor .quoteblock.abstract blockquote p:first-of-type:before {
    display: none; }
  /* line 451, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.tableblock {
    max-width: 100%;
    border-collapse: separate; }
  /* line 452, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.tableblock td > .paragraph:last-child p > p:last-child, .asciidoctor table.tableblock th > p:last-child, .asciidoctor table.tableblock td > p:last-child {
    margin-bottom: 0; }
  /* line 454, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.spread {
    width: 100%; }
  /* line 456, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.tableblock, .asciidoctor th.tableblock, .asciidoctor td.tableblock {
    border: 0 solid #dedede; }
  /* line 458, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-all th.tableblock, .asciidoctor table.grid-all td.tableblock {
    border-width: 0 1px 1px 0; }
  /* line 460, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-all tfoot > tr > th.tableblock, .asciidoctor table.grid-all tfoot > tr > td.tableblock {
    border-width: 1px 1px 0 0; }
  /* line 462, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-cols th.tableblock, .asciidoctor table.grid-cols td.tableblock {
    border-width: 0 1px 0 0; }
  /* line 464, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-all * > tr > .tableblock:last-child, .asciidoctor table.grid-cols * > tr > .tableblock:last-child {
    border-right-width: 0; }
  /* line 466, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-rows th.tableblock, .asciidoctor table.grid-rows td.tableblock {
    border-width: 0 0 1px 0; }
  /* line 468, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-all tbody > tr:last-child > th.tableblock, .asciidoctor table.grid-all tbody > tr:last-child > td.tableblock, .asciidoctor table.grid-all thead:last-child > tr > th.tableblock, .asciidoctor table.grid-rows tbody > tr:last-child > th.tableblock, .asciidoctor table.grid-rows tbody > tr:last-child > td.tableblock, .asciidoctor table.grid-rows thead:last-child > tr > th.tableblock {
    border-bottom-width: 0; }
  /* line 470, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.grid-rows tfoot > tr > th.tableblock, .asciidoctor table.grid-rows tfoot > tr > td.tableblock {
    border-width: 1px 0 0 0; }
  /* line 472, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.frame-all {
    border-width: 1px; }
  /* line 474, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.frame-sides {
    border-width: 0 1px; }
  /* line 476, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table.frame-topbot {
    border-width: 1px 0; }
  /* line 478, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor th.halign-left, .asciidoctor td.halign-left {
    text-align: left; }
  /* line 480, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor th.halign-right, .asciidoctor td.halign-right {
    text-align: right; }
  /* line 482, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor th.halign-center, .asciidoctor td.halign-center {
    text-align: center; }
  /* line 484, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor th.valign-top, .asciidoctor td.valign-top {
    vertical-align: top; }
  /* line 486, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor th.valign-bottom, .asciidoctor td.valign-bottom {
    vertical-align: bottom; }
  /* line 488, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor th.valign-middle, .asciidoctor td.valign-middle {
    vertical-align: middle; }
  /* line 490, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor table thead th, .asciidoctor table tfoot th {
    font-weight: bold; }
  /* line 492, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor tbody tr th {
    display: table-cell;
    line-height: 1.6;
    background: #f7f8f7; }
  /* line 494, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor tbody tr th, .asciidoctor tbody tr th p, .asciidoctor tfoot tr th, .asciidoctor tfoot tr th p {
    color: rgba(0, 0, 0, 0.8);
    font-weight: bold; }
  /* line 496, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p.tableblock > code:only-child {
    background: none;
    padding: 0; }
  /* line 498, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p.tableblock {
    font-size: 1em; }
  /* line 500, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor td > div.verse {
    white-space: pre; }
  /* line 502, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol {
    margin-left: 1.75em; }
  /* line 504, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul li ol {
    margin-left: 1.5em; }
  /* line 506, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor dl dd {
    margin-left: 1.125em; }
  /* line 508, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor dl dd:last-child, .asciidoctor dl dd:last-child > :last-child {
    margin-bottom: 0; }
  /* line 510, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol > li p, .asciidoctor ul > li p, .asciidoctor ul dd, .asciidoctor ol dd, .asciidoctor .olist .olist, .asciidoctor .ulist .ulist, .asciidoctor .ulist .olist, .asciidoctor .olist .ulist {
    margin-bottom: 0.625em; }
  /* line 512, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.unstyled, .asciidoctor ol.unnumbered, .asciidoctor ul.checklist, .asciidoctor ul.none {
    list-style-type: none; }
  /* line 514, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.unstyled, .asciidoctor ol.unnumbered, .asciidoctor ul.checklist {
    margin-left: 0.625em; }
  /* line 516, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.checklist li > p:first-child > .fa-check-square-o:first-child, .asciidoctor ul.checklist li > p:first-child > input[type="checkbox"]:first-child {
    margin-right: 0.25em; }
  /* line 518, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.checklist li > p:first-child > input[type="checkbox"]:first-child {
    position: relative;
    top: 1px; }
  /* line 520, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.inline {
    margin: 0 auto 0.625em auto;
    margin-left: -1.375em;
    margin-right: 0;
    padding: 0;
    list-style: none;
    overflow: hidden; }
  /* line 521, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.inline > li {
    list-style: none;
    float: left;
    margin-left: 1.375em;
    display: block; }
  /* line 522, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ul.inline > li > * {
    display: block; }
  /* line 524, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .unstyled dl dt {
    font-weight: normal;
    font-style: normal; }
  /* line 526, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.arabic {
    list-style-type: decimal; }
  /* line 528, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.decimal {
    list-style-type: decimal-leading-zero; }
  /* line 530, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.loweralpha {
    list-style-type: lower-alpha; }
  /* line 532, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.upperalpha {
    list-style-type: upper-alpha; }
  /* line 534, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.lowerroman {
    list-style-type: lower-roman; }
  /* line 536, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.upperroman {
    list-style-type: upper-roman; }
  /* line 538, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor ol.lowergreek {
    list-style-type: lower-greek; }
  /* line 540, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .hdlist > table, .asciidoctor .colist > table {
    border: 0;
    background: none; }
  /* line 541, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .hdlist > table > tbody > tr, .asciidoctor .colist > table > tbody > tr {
    background: none; }
  /* line 543, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor td.hdlist1 {
    padding-right: .75em;
    font-weight: bold; }
  /* line 545, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor td.hdlist1, .asciidoctor td.hdlist2 {
    vertical-align: top; }
  /* line 547, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .literalblock + .colist, .asciidoctor .listingblock + .colist {
    margin-top: -0.5em; }
  /* line 549, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .colist > table tr > td:first-of-type {
    padding: 0 0.75em;
    line-height: 1; }
  /* line 550, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .colist > table tr > td:last-of-type {
    padding: 0.25em 0; }
  /* line 552, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .thumb, .asciidoctor .th {
    line-height: 0;
    display: inline-block;
    border: solid 4px white;
    -webkit-box-shadow: 0 0 0 1px #dddddd;
    box-shadow: 0 0 0 1px #dddddd; }
  /* line 554, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .imageblock.left, .asciidoctor .imageblock[style*="float: left"] {
    margin: 0.25em 0.625em 1.25em 0; }
  /* line 555, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .imageblock.right, .asciidoctor body[class$="about"] dl dd img.imageblock, body[class$="about"] dl dd .asciidoctor img.imageblock, .asciidoctor .imageblock[style*="float: right"] {
    margin: 0.25em 0 1.25em 0.625em; }
  /* line 556, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .imageblock > .title {
    margin-bottom: 0; }
  /* line 557, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .imageblock.thumb, .asciidoctor .imageblock.th {
    border-width: 6px; }
  /* line 558, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .imageblock.thumb > .title, .asciidoctor .imageblock.th > .title {
    padding: 0 0.125em; }
  /* line 560, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .image.left, .asciidoctor .image.right, .asciidoctor body[class$="about"] dl dd img.image, body[class$="about"] dl dd .asciidoctor img.image {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
    display: inline-block;
    line-height: 0; }
  /* line 561, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .image.left {
    margin-right: 0.625em; }
  /* line 562, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .image.right, .asciidoctor body[class$="about"] dl dd img.image, body[class$="about"] dl dd .asciidoctor img.image {
    margin-left: 0.625em; }
  /* line 564, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor a.image {
    text-decoration: none; }
  /* line 566, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor span.footnote, .asciidoctor span.footnoteref {
    vertical-align: super;
    font-size: 0.875em; }
  /* line 567, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor span.footnote a, .asciidoctor span.footnoteref a {
    text-decoration: none; }
  /* line 568, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor span.footnote a:active, .asciidoctor span.footnoteref a:active {
    text-decoration: underline; }
  /* line 570, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footnotes {
    padding-top: 0.75em;
    padding-bottom: 0.75em;
    margin-bottom: 0.625em; }
  /* line 571, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footnotes hr {
    width: 20%;
    min-width: 6.25em;
    margin: -.25em 0 .75em 0;
    border-width: 1px 0 0 0; }
  /* line 572, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footnotes .footnote {
    padding: 0 0.375em;
    line-height: 1.3;
    font-size: 0.875em;
    margin-left: 1.2em;
    text-indent: -1.2em;
    margin-bottom: .2em; }
  /* line 573, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footnotes .footnote a:first-of-type {
    font-weight: bold;
    text-decoration: none; }
  /* line 574, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #footnotes .footnote:last-of-type {
    margin-bottom: 0; }
  /* line 576, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor #content #footnotes {
    margin-top: -0.625em;
    margin-bottom: 0;
    padding: 0.75em 0; }
  /* line 578, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .gist .file-data > table {
    border: 0;
    background: #fff;
    width: 100%;
    margin-bottom: 0; }
  /* line 579, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .gist .file-data > table td.line-data {
    width: 99%; }
  /* line 581, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor div.unbreakable {
    page-break-inside: avoid; }
  /* line 583, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .big {
    font-size: larger; }
  /* line 585, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .small {
    font-size: smaller; }
  /* line 587, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .underline {
    text-decoration: underline; }
  /* line 589, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .overline {
    text-decoration: overline; }
  /* line 591, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .line-through {
    text-decoration: line-through; }
  /* line 593, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .aqua {
    color: #00bfbf; }
  /* line 595, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .aqua-background {
    background-color: #00fafa; }
  /* line 597, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .black {
    color: black; }
  /* line 599, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .black-background {
    background-color: black; }
  /* line 601, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .blue {
    color: #0000bf; }
  /* line 603, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .blue-background {
    background-color: #0000fa; }
  /* line 605, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .fuchsia {
    color: #bf00bf; }
  /* line 607, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .fuchsia-background {
    background-color: #fa00fa; }
  /* line 609, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .gray {
    color: #606060; }
  /* line 611, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .gray-background {
    background-color: #7d7d7d; }
  /* line 613, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .green {
    color: #006000; }
  /* line 615, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .green-background {
    background-color: #007d00; }
  /* line 617, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .lime {
    color: #00bf00; }
  /* line 619, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .lime-background {
    background-color: #00fa00; }
  /* line 621, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .maroon {
    color: #600000; }
  /* line 623, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .maroon-background {
    background-color: #7d0000; }
  /* line 625, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .navy {
    color: #000060; }
  /* line 627, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .navy-background {
    background-color: #00007d; }
  /* line 629, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .olive {
    color: #606000; }
  /* line 631, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .olive-background {
    background-color: #7d7d00; }
  /* line 633, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .purple {
    color: #600060; }
  /* line 635, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .purple-background {
    background-color: #7d007d; }
  /* line 637, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .red {
    color: #bf0000; }
  /* line 639, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .red-background {
    background-color: #fa0000; }
  /* line 641, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .silver {
    color: #909090; }
  /* line 643, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .silver-background {
    background-color: #bcbcbc; }
  /* line 645, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .teal {
    color: #006060; }
  /* line 647, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .teal-background {
    background-color: #007d7d; }
  /* line 649, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .white {
    color: #bfbfbf; }
  /* line 651, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .white-background {
    background-color: #fafafa; }
  /* line 653, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .yellow {
    color: #bfbf00; }
  /* line 655, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .yellow-background {
    background-color: #fafa00; }
  /* line 657, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor span.icon > .fa {
    cursor: default; }
  /* line 659, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.icon [class^="fa icon-"] {
    font-size: 2.5em;
    /*text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);*/
    cursor: default; }
  /* line 660, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.icon .icon-note:before {
    content: "\f05a";
    color: #19407c; }
  /* line 661, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.icon .icon-tip:before {
    content: "\f0eb";
    text-shadow: 1px 1px 2px rgba(155, 155, 0, 0.8);
    color: #111; }
  /* line 662, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.icon .icon-warning:before {
    content: "\f071";
    color: #bf6900; }
  /* line 663, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.icon .icon-caution:before {
    content: "\f06d";
    color: #bf3400; }
  /* line 664, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .admonitionblock td.icon .icon-important:before {
    content: "\f06a";
    color: #bf0000; }
  /* line 666, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .conum[data-value] {
    display: inline-block;
    color: #fff !important;
    background-color: rgba(0, 0, 0, 0.8);
    -webkit-border-radius: 100px;
    border-radius: 100px;
    text-align: center;
    font-size: 0.75em;
    width: 1.67em;
    height: 1.67em;
    line-height: 1.67em;
    font-family: "Open Sans", "DejaVu Sans", sans-serif;
    font-style: normal;
    font-weight: bold; }
  /* line 667, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .conum[data-value] * {
    color: #fff !important; }
  /* line 668, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .conum[data-value] + b {
    display: none; }
  /* line 669, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .conum[data-value]:after {
    content: attr(data-value); }
  /* line 670, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor pre .conum[data-value] {
    position: relative;
    top: -0.125em; }
  /* line 672, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor b.conum * {
    color: inherit !important; }
  /* line 674, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .conum:not([data-value]):empty {
    display: none; }
  /* line 676, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor h1, .asciidoctor h2 {
    letter-spacing: -0.01em; }
  /* line 678, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor dt, .asciidoctor th.tableblock, .asciidoctor td.content, .asciidoctor ul#downloads > li td.notes, ul#downloads > li .asciidoctor td.notes {
    text-rendering: optimizeLegibility; }
  /* line 680, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p, .asciidoctor td.content, .asciidoctor ul#downloads > li td.notes, ul#downloads > li .asciidoctor td.notes {
    letter-spacing: -0.01em; }
  /* line 681, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p strong, .asciidoctor td.content strong, .asciidoctor ul#downloads > li td.notes strong, ul#downloads > li .asciidoctor td.notes strong {
    letter-spacing: -0.005em; }
  /* line 683, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p, .asciidoctor blockquote, .asciidoctor dt, .asciidoctor td.content, .asciidoctor ul#downloads > li td.notes, ul#downloads > li .asciidoctor td.notes {
    font-size: 1.0625rem; }
  /* line 685, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor p {
    margin-bottom: 1.25rem; }
  /* line 687, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .sidebarblock p, .asciidoctor .sidebarblock dt, .asciidoctor .sidebarblock td.content, .asciidoctor .sidebarblock ul#downloads > li td.notes, ul#downloads > li .asciidoctor .sidebarblock td.notes, .asciidoctor p.tableblock {
    font-size: 1em; }
  /* line 689, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .exampleblock > .content, .asciidoctor ul#downloads > li .exampleblock > .notes, ul#downloads > li .asciidoctor .exampleblock > .notes, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"], ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"], .asciidoctor #download > div .exampleblock > ul[id$="-download"], #download > div .asciidoctor .exampleblock > ul[id$="-download"] {
    background-color: #fffef7;
    border-color: #e0e0dc;
    -webkit-box-shadow: 0 1px 4px #e0e0dc;
    box-shadow: 0 1px 4px #e0e0dc; }
  /* line 691, /app/source-xampp-windows/stylesheets/asciidoctor.css */
  .asciidoctor .print-only {
    display: none !important; }
  @media print {
    /* line 620, /app/source-xampp-windows/stylesheets/all.scss */
    .asciidoctor {
      @page {
        margin: 1.25cm 0.75cm; }
 }
      /* line 694, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor * {
        -webkit-box-shadow: none !important;
        box-shadow: none !important;
        text-shadow: none !important; }
      /* line 695, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor a {
        color: inherit !important;
        text-decoration: underline !important; }
      /* line 696, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor a.bare, .asciidoctor a[href^="#"], .asciidoctor a[href^="mailto:"] {
        text-decoration: none !important; }
      /* line 697, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor a[href^="http:"]:not(.bare):after, .asciidoctor a[href^="https:"]:not(.bare):after, .asciidoctor a[href^="mailto:"]:not(.bare):after {
        content: "(" attr(href) ")";
        display: inline-block;
        font-size: 0.875em;
        padding-left: 0.25em; }
      /* line 698, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor abbr[title]:after {
        content: " (" attr(title) ")"; }
      /* line 699, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor pre, .asciidoctor blockquote, .asciidoctor tr, .asciidoctor img {
        page-break-inside: avoid; }
      /* line 700, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor thead {
        display: table-header-group; }
      /* line 701, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor img {
        max-width: 100% !important; }
      /* line 702, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor p, .asciidoctor blockquote, .asciidoctor dt, .asciidoctor td.content, .asciidoctor ul#downloads > li td.notes, ul#downloads > li .asciidoctor td.notes {
        font-size: 1em;
        orphans: 3;
        widows: 3; }
      /* line 703, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor h2, .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title {
        page-break-after: avoid; }
      /* line 704, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor #toc, .asciidoctor .sidebarblock, .asciidoctor .exampleblock > .content, .asciidoctor ul#downloads > li .exampleblock > .notes, ul#downloads > li .asciidoctor .exampleblock > .notes, .asciidoctor ul#downloads > li table .exampleblock > ul[id$="-download"], ul#downloads > li table .asciidoctor .exampleblock > ul[id$="-download"], .asciidoctor #download > div .exampleblock > ul[id$="-download"], #download > div .asciidoctor .exampleblock > ul[id$="-download"] {
        background: none !important; }
      /* line 705, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor #toc {
        border-bottom: 1px solid #ddddd8 !important;
        padding-bottom: 0 !important; }
      /* line 706, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .sect1 {
        padding-bottom: 0 !important; }
      /* line 707, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .sect1 + .sect1 {
        border: 0 !important; }
      /* line 708, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor #header > h1:first-child {
        margin-top: 1.25rem; }
      /* line 709, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #header {
        text-align: center; }
      /* line 710, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #header > h1:first-child {
        border: 0 !important;
        margin: 2.5em 0 1em 0; }
      /* line 711, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #header .details {
        border: 0 !important;
        display: block;
        padding: 0 !important; }
      /* line 712, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #header .details span:first-child {
        margin-left: 0 !important; }
      /* line 713, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #header .details br {
        display: block; }
      /* line 714, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #header .details br + span:before {
        content: none !important; }
      /* line 715, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #toc {
        border: 0 !important;
        text-align: left !important;
        padding: 0 !important;
        margin: 0 !important; }
      /* line 716, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor body.book #toc, .asciidoctor body.book #preamble, .asciidoctor body.book h1.sect0, .asciidoctor body.book .sect1 > h2 {
        page-break-before: always; }
      /* line 717, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .listingblock code[data-lang]:before {
        display: block; }
      /* line 718, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor #footer {
        background: none !important;
        padding: 0 0.9375em; }
      /* line 719, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor #footer-text {
        color: rgba(0, 0, 0, 0.6) !important;
        font-size: 0.9em; }
      /* line 720, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .hide-on-print {
        display: none !important; }
      /* line 721, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .print-only {
        display: block !important; }
      /* line 722, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .hide-for-print {
        display: none !important; }
      /* line 723, /app/source-xampp-windows/stylesheets/asciidoctor.css */
      .asciidoctor .show-for-print {
        display: inherit !important; } }
  /* line 625, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor .imageblock > .content, .asciidoctor ul#downloads > li .imageblock > .notes, ul#downloads > li .asciidoctor .imageblock > .notes, .asciidoctor ul#downloads > li table .imageblock > ul[id$="-download"], ul#downloads > li table .asciidoctor .imageblock > ul[id$="-download"], .asciidoctor #download > div .imageblock > ul[id$="-download"], #download > div .asciidoctor .imageblock > ul[id$="-download"] {
    text-align: center; }
    /* line 627, /app/source-xampp-windows/stylesheets/all.scss */
    .asciidoctor .imageblock > .content > img, .asciidoctor ul#downloads > li .imageblock > .notes > img, ul#downloads > li .asciidoctor .imageblock > .notes > img, .asciidoctor ul#downloads > li table .imageblock > ul[id$="-download"] > img, ul#downloads > li table .asciidoctor .imageblock > ul[id$="-download"] > img, .asciidoctor #download > div .imageblock > ul[id$="-download"] > img, #download > div .asciidoctor .imageblock > ul[id$="-download"] > img {
      padding: 0.25em;
      border: 1px solid #eee;
      max-width: 70%; }
  /* line 634, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h1, .asciidoctor h2, .asciidoctor h3, .asciidoctor #toctitle, .asciidoctor .sidebarblock > .content > .title, .asciidoctor ul#downloads > li .sidebarblock > .notes > .title, ul#downloads > li .asciidoctor .sidebarblock > .notes > .title, .asciidoctor ul#downloads > li table .sidebarblock > ul[id$="-download"] > .title, ul#downloads > li table .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor #download > div .sidebarblock > ul[id$="-download"] > .title, #download > div .asciidoctor .sidebarblock > ul[id$="-download"] > .title, .asciidoctor h4, .asciidoctor h5, .asciidoctor h6 {
    font-family: inherit;
    color: #222222; }
  /* line 640, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h1 {
    font-size: 2.75rem; }
  /* line 641, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h2 {
    font-size: 2.3125rem; }
  /* line 642, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h3 {
    font-size: 1.6875rem; }
  /* line 643, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h4 {
    font-size: 1.4375rem; }
  /* line 644, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h5 {
    font-size: 1.125rem; }
  /* line 645, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor h6 {
    font-size: 1rem; }
  /* line 647, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor a, .asciidoctor a.anchor {
    color: #5e8949;
    text-decoration: none;
    line-height: inherit; }
    /* line 653, /app/source-xampp-windows/stylesheets/all.scss */
    .asciidoctor a:hover, .asciidoctor a:focus, .asciidoctor a.anchor:hover, .asciidoctor a.anchor:focus {
      color: #0079a1; }
  /* line 658, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor > aside {
    width: 33.33333%;
    float: right;
    margin: 0 0 1.875rem 1.875rem; }
  /* line 664, /app/source-xampp-windows/stylesheets/all.scss */
  .asciidoctor i.fa {
    font-style: normal; }

@media only screen and (min-width: 64.063em) {
  /* line 671, /app/source-xampp-windows/stylesheets/all.scss */
  .hero #download > .columns > a, .hero #stack-list #download > li > a, #stack-list .hero #download > li > a, .hero #theme-list li#download > div > a, #theme-list .hero li#download > div > a {
    margin-top: 0.5em; }
  /* line 674, /app/source-xampp-windows/stylesheets/all.scss */
  .hero #download > .columns:first-child, .hero #stack-list #download > li:first-child, #stack-list .hero #download > li:first-child, .hero #theme-list li#download > div:first-child, #theme-list .hero li#download > div:first-child {
    display: block; }

  /* line 678, /app/source-xampp-windows/stylesheets/all.scss */
  ul#downloads li table {
    width: 100%; } }
@media only screen and (min-width: 40.063em) {
  /* line 684, /app/source-xampp-windows/stylesheets/all.scss */
  .hero h1 span {
    font-size: inherit;
    display: inline; } }
/* line 690, /app/source-xampp-windows/stylesheets/all.scss */
#search-results {
  min-height: 10em;
  margin-top: -2em; }
  /* line 693, /app/source-xampp-windows/stylesheets/all.scss */
  #search-results table {
    margin: 0;
    border: 0; }
    /* line 696, /app/source-xampp-windows/stylesheets/all.scss */
    #search-results table td {
      line-height: 1em;
      padding: 0; }
  /* line 701, /app/source-xampp-windows/stylesheets/all.scss */
  #search-results .gsc-table-result {
    margin-top: 0.5em; }
    /* line 703, /app/source-xampp-windows/stylesheets/all.scss */
    #search-results .gsc-table-result td {
      padding-left: 8px; }
  /* line 707, /app/source-xampp-windows/stylesheets/all.scss */
  #search-results .gsc-selected-option-container {
    width: auto !important; }
  /* line 710, /app/source-xampp-windows/stylesheets/all.scss */
  #search-results .gcsc-branding-img-noclear {
    display: block;
    width: 51px !important; }

/* line 716, /app/source-xampp-windows/stylesheets/all.scss */
ul.sub-nav {
  border-bottom: 1px solid #ddd; }

/* line 720, /app/source-xampp-windows/stylesheets/all.scss */
a.pdf {
  display: inline-block;
  background-image: url("/dashboard/images/pdf-icon.png");
  background-repeat: no-repeat;
  background-position: 0 50%;
  background-size: 33px auto;
  padding-left: 45px;
  min-height: 40px;
  line-height: 1.2em; }
  /* line 730, /app/source-xampp-windows/stylesheets/all.scss */
  a.pdf span {
    font-size: 0.8em;
    color: #777;
    display: block; }

/* line 737, /app/source-xampp-windows/stylesheets/all.scss */
.video-wrapper {
  align-items: center;
  background-size: cover;
  background-position: center;
  display: flex;
  max-width: 480px;
  height: 270px;
  justify-content: center;
  background-repeat: no-repeat;
  margin: auto; }
  /* line 748, /app/source-xampp-windows/stylesheets/all.scss */
  .video-wrapper .play-button {
    border-radius: 3px;
    padding: .3em 1em; }
  /* line 753, /app/source-xampp-windows/stylesheets/all.scss */
  .video-wrapper .feather {
    height: 240px;
    stroke: #fff;
    stroke-width: 1;
    fill: none;
    stroke-linecap: round;
    stroke-linejoin: round;
    border-radius: 100%; }
  /* line 763, /app/source-xampp-windows/stylesheets/all.scss */
  .video-wrapper .circle {
    fill: rgba(0, 0, 0, 0.5);
    stroke-width: 1; }

/* line 771, /app/source-xampp-windows/stylesheets/all.scss */
ul#docs-list > li {
  min-height: 150px;
  margin-bottom: 1.5em; }
  /* line 776, /app/source-xampp-windows/stylesheets/all.scss */
  ul#docs-list > li h3 {
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5em; }
    /* line 779, /app/source-xampp-windows/stylesheets/all.scss */
    ul#docs-list > li h3 a {
      color: inherit; }
  /* line 784, /app/source-xampp-windows/stylesheets/all.scss */
  ul#docs-list > li a.pdf {
    margin-top: 0.5em; }

/* line 790, /app/source-xampp-windows/stylesheets/all.scss */
iframe.video {
  border: 1px solid #ccc;
  margin-bottom: 1em; }

/* line 795, /app/source-xampp-windows/stylesheets/all.scss */
#addonsVideoModal {
  width: 620px;
  /* 560px 2x20px padding */
  margin-left: -25%; }

/* line 800, /app/source-xampp-windows/stylesheets/all.scss */
.warning-message {
  background-color: #FFFBCC;
  border: 1px solid #FB8168;
  padding: 9px;
  text-align: justify; }

/* PHP 7 banner */
/* line 808, /app/source-xampp-windows/stylesheets/all.scss */
#php7-banner {
  background: #5e8949;
  text-align: center; }
  /* line 812, /app/source-xampp-windows/stylesheets/all.scss */
  #php7-banner h2 {
    color: #fff; }
  /* line 816, /app/source-xampp-windows/stylesheets/all.scss */
  #php7-banner a {
    font-size: 0.8em;
    color: rgba(255, 255, 255, 0.8); }
    /* line 820, /app/source-xampp-windows/stylesheets/all.scss */
    #php7-banner a:hover {
      text-decoration: underline; }
